
<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 102
array(53) {
  ["fs_app_root"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_system"]: string(81) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system"
  ["input_params"]: array(4) {
    ["table_name"]: string(18) "autodesk_customers"
    ["callback"]: string(32) "autodesk\generate_customer_table"
    ["data_source"]: string(0) ""
    ["column_ids"]: array(7) {
      [0]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      [1]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      [2]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      [3]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      [4]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      [5]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      [6]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
    }
  }
  ["system_views"]: array(6) {
    [0]: string(6) "system"
    [1]: string(5) "login"
    [2]: string(6) "logout"
    [3]: string(14) "reset-password"
    [4]: string(8) "settings"
    [5]: string(13) "database_dump"
  }
  ["request_uri"]: string(82) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"
  ["domain"]: string(21) "www.cadservices.co.uk"
  ["script_name"]: string(42) "/baffletrain/autocadlt/autobooks/index.php"
  ["fs_doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["fs_app"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_resources"]: string(84) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources"
  ["fs_api"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api"
  ["fs_classes"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes"
  ["fs_functions"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions"
  ["fs_views"]: string(90) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views"
  ["fs_config"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/config"
  ["fs_templates"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/templates"
  ["fs_components"]: string(95) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/components"
  ["fs_logs"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/logs"
  ["fs_sys_api"]: string(85) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api"
  ["fs_sys_classes"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes"
  ["fs_sys_functions"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions"
  ["fs_sys_views"]: string(87) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views"
  ["fs_sys_config"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/config"
  ["fs_sys_templates"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates"
  ["fs_sys_components"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/components"
  ["fs_sys_logs"]: string(86) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/logs"
  ["fs_uploads"]: string(83) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/"
  ["fs_cache"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["fs_temp"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["app_root"]: string(32) "/baffletrain/autocadlt/autobooks"
  ["app_path"]: string(33) "api/data_table/column_preferences"
  ["path_parts"]: array(4) {
    [0]: string(3) "api"
    [1]: string(10) "data_table"
    [2]: string(18) "column_preferences"
    [3]: string(15) "reorder_columns"
  }
  ["top_level"]: string(3) "api"
  ["current_page"]: string(15) "reorder_columns"
  ["fs_app_path"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences"
  ["fs_full_path"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/api/data_table/column_preferences"
  ["full_path"]: string(65) "baffletrain/autocadlt/autobooks/api/data_table/column_preferences"
  ["fs_full_page"]: string(140) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/reorder_columns"
  ["full_page"]: string(81) "baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"
  ["set_by"]: string(19) "HTTP_HX_CURRENT_URL"
  ["source_path"]: string(41) "/baffletrain/autocadlt/autobooks/autodesk"
  ["source_page"]: string(9) "customers"
  ["source_path_parts"]: array(4) {
    [0]: string(11) "baffletrain"
    [1]: string(9) "autocadlt"
    [2]: string(9) "autobooks"
    [3]: string(8) "autodesk"
  }
  ["source_app_path"]: string(8) "autodesk"
  ["hx_current_url"]: string(80) "https://www.cadservices.co.uk/baffletrain/autocadlt/autobooks/autodesk/customers"
  ["hx_current_url_parts"]: array(3) {
    ["scheme"]: string(5) "https"
    ["host"]: string(21) "www.cadservices.co.uk"
    ["path"]: string(51) "/baffletrain/autocadlt/autobooks/autodesk/customers"
  }
  ["source_app_path_parts"]: array(1) {
    [0]: string(8) "autodesk"
  }
  ["source_fs_path"]: string(99) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk"
  ["fs_sys_db_class"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php"
  ["route_tree"]: array(5) {
    ["dashboard"]: array(7) {
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(8) {
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
      ["sub_folder"]: array(5) {
        ["customers"]: array(7) {
          ["name"]: string(9) "Customers"
          ["icon"]: string(4) "user"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["orders"]: array(7) {
          ["name"]: string(6) "Orders"
          ["icon"]: string(13) "shopping-cart"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["quotes"]: array(7) {
          ["name"]: string(6) "Quotes"
          ["icon"]: string(13) "speech_bubble"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["products"]: array(7) {
          ["name"]: string(8) "Products"
          ["icon"]: string(16) "computer_desktop"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["subscriptions"]: array(7) {
          ["name"]: string(13) "Subscriptions"
          ["icon"]: string(6) "ticket"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
      }
    }
    ["system"]: array(8) {
      ["sub_folder"]: array(5) {
        ["logs"]: array(7) {
          ["name"]: string(4) "logs"
          ["icon"]: string(4) "code"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["data_sources"]: array(7) {
          ["name"]: string(12) "Data Sources"
          ["icon"]: string(12) "circle-stack"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(12) "data_sources"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["database_dump"]: array(7) {
          ["name"]: string(10) "DB Manager"
          ["icon"]: string(12) "circle-stack"
          ["required_roles"]: array(2) {
            [0]: string(5) "admin"
            [1]: string(3) "dev"
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["subscription_matching_rules"]: array(7) {
          ["name"]: string(12) "Rule Manager"
          ["icon"]: string(12) "puzzle-piece"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(true)
          ["is_system"]: bool(true)
        }
        ["users"]: array(7) {
          ["name"]: string(5) "users"
          ["icon"]: string(10) "user-group"
          ["required_roles"]: array(2) {
            [0]: string(5) "admin"
            [1]: string(3) "dev"
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(0) ""
          ["can_delete"]: bool(true)
          ["is_system"]: bool(true)
        }
      }
      ["name"]: string(6) "System"
      ["icon"]: string(3) "cog"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["email_campaigns"]: array(7) {
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["sketchup"]: array(7) {
      ["name"]: string(8) "Sketchup"
      ["icon"]: string(8) "bluebeam"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(false)
    }
  }
  ["route_list"]: array(15) {
    ["dashboard"]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(10) {
      ["id"]: int(11)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["customers"]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["logs"]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["orders"]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["data_sources"]: array(10) {
      ["id"]: int(13)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["quotes"]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["products"]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["subscriptions"]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["database_dump"]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(13) "database_dump"
      ["name"]: string(10) "DB Manager"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["subscription_matching_rules"]: array(10) {
      ["id"]: int(14)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(27) "subscription_matching_rules"
      ["name"]: string(12) "Rule Manager"
      ["icon"]: string(12) "puzzle-piece"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    ["email_campaigns"]: array(10) {
      ["id"]: int(12)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["users"]: array(10) {
      ["id"]: int(15)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(5) "users"
      ["name"]: string(5) "users"
      ["icon"]: string(10) "user-group"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    ["system"]: array(10) {
      ["id"]: int(1)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(3) "cog"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["sketchup"]: array(10) {
      ["id"]: int(34)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "sketchup"
      ["name"]: string(8) "Sketchup"
      ["icon"]: string(8) "bluebeam"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(false)
    }
  }
  ["routes"]: array(15) {
    [0]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [1]: array(10) {
      ["id"]: int(11)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [2]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [3]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [4]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [5]: array(10) {
      ["id"]: int(13)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [6]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [7]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [8]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [9]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(13) "database_dump"
      ["name"]: string(10) "DB Manager"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [10]: array(10) {
      ["id"]: int(14)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(27) "subscription_matching_rules"
      ["name"]: string(12) "Rule Manager"
      ["icon"]: string(12) "puzzle-piece"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    [11]: array(10) {
      ["id"]: int(12)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [12]: array(10) {
      ["id"]: int(15)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(5) "users"
      ["name"]: string(5) "users"
      ["icon"]: string(10) "user-group"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    [13]: array(10) {
      ["id"]: int(1)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(3) "cog"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [14]: array(10) {
      ["id"]: int(34)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "sketchup"
      ["name"]: string(8) "Sketchup"
      ["icon"]: string(8) "bluebeam"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(false)
    }
  }
}

    ----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 103
array(5) {
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["auth_token"]: string(64) "6ce7f86471937ada8f4aea8c85bb98a03969635b6ac0fa63af335af60eb20fea"
  ["debug_mode"]: bool(true)
  ["id_count"]: int(13)
}

    ----------------------------------------------------------------------------
-->

<!-- startup_sequence.class.php > start() 107: am I in debug mode?
-->

<!--
********************************************************************************************************************************************************
$row: startup_sequence.class.php > start() 111
array(1) {
  ["preferences"]: string(19) "{"debug_mode":true}"
}

    ----------------------------------------------------------------------------
-->

<!-- preferences: startup_sequence.class.php > start() 112: {"debug_mode":true}
-->

<!-- debug mode is: : startup_sequence.class.php > start() 113: on
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 121
array(5) {
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["auth_token"]: string(64) "6ce7f86471937ada8f4aea8c85bb98a03969635b6ac0fa63af335af60eb20fea"
  ["debug_mode"]: bool(true)
  ["id_count"]: int(13)
}

    ----------------------------------------------------------------------------
-->

<!-- index.php > global() 29: starting route
-->

<!-- router.class.php > route() 18: starting route for api/data_table/column_preferences/reorder_columns
-->

<!-- router.class.php > get_api() 435: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api/data_table/column_preferences.api.php
-->

<!-- router.class.php > get_api() 435: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!-- router.class.php > get_api() 437: API file found: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!--
********************************************************************************************************************************************************
endpointy: router.class.php > route() 54
array(5) {
  ["parts"]: array(3) {
    [0]: string(3) "api"
    [1]: string(10) "data_table"
    [2]: string(18) "column_preferences"
  }
  ["endpoint"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["function_call"]: string(15) "reorder_columns"
  ["api_result"]: array(2) {
    ["status"]: string(7) "success"
    ["path"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  }
  ["api_result_status"]: string(7) "success"
}

    ----------------------------------------------------------------------------
-->

<!-- endpoint: router.class.php > route() 150: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for reorder_columns
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(104) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/reorder_columns.fn.php"
  ["functions_folder"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/reorder_columns.fn.php"
  ["functions_folder_lowercase"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/reorder_columns.fn.php"
  ["views_directory_current_page"]: string(147) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/reorder_columns.fn.php"
  ["sys_views_directory_current_page"]: string(144) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/reorder_columns.fn.php"
  ["views_file_current_page"]: string(113) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/reorder_columns.fn.php"
  ["system_functions"]: string(114) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/reorder_columns.fn.php"
  ["system_functions_lowercase"]: string(114) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/reorder_columns.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 176
         <strong>Arguments:</strong>
         0: "reorder_columns"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/reorder_columns.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.fn.php
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(114) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php.fn.php"
  ["functions_folder"]: string(127) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php.fn.php"
  ["functions_folder_lowercase"]: string(127) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php.fn.php"
  ["views_directory_current_page"]: string(157) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php.fn.php"
  ["sys_views_directory_current_page"]: string(154) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php.fn.php"
  ["views_file_current_page"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php.fn.php"
  ["system_functions"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php.fn.php"
  ["system_functions_lowercase"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 177
         <strong>Arguments:</strong>
         0: "column_preferences.fn.php"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for api
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api.fn.php"
  ["functions_folder"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php"
  ["functions_folder_lowercase"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php"
  ["views_directory_current_page"]: string(135) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/api.fn.php"
  ["sys_views_directory_current_page"]: string(132) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/api.fn.php"
  ["views_file_current_page"]: string(101) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api.fn.php"
  ["system_functions"]: string(102) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php"
  ["system_functions_lowercase"]: string(102) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/autoloader.php, Line: 276
         <strong>Arguments:</strong>
         0: "api"
      <strong>Function:</strong> autobooks_load_path_functions, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 180
         <strong>Arguments:</strong>
         0: ["api","data_table","column_preferences","reorder_columns"]
         1: "reorder_columns"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for data_table
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(99) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/data_table.fn.php"
  ["functions_folder"]: string(112) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php"
  ["functions_folder_lowercase"]: string(112) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php"
  ["views_directory_current_page"]: string(142) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/data_table.fn.php"
  ["sys_views_directory_current_page"]: string(139) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/data_table.fn.php"
  ["views_file_current_page"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/data_table.fn.php"
  ["system_functions"]: string(109) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/data_table.fn.php"
  ["system_functions_lowercase"]: string(109) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/data_table.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/autoloader.php, Line: 276
         <strong>Arguments:</strong>
         0: "data_table"
      <strong>Function:</strong> autobooks_load_path_functions, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 180
         <strong>Arguments:</strong>
         0: ["api","data_table","column_preferences","reorder_columns"]
         1: "reorder_columns"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/data_table.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 249: found function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for column_preferences
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(107) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php"
  ["functions_folder"]: string(120) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php"
  ["functions_folder_lowercase"]: string(120) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php"
  ["views_directory_current_page"]: string(150) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php"
  ["sys_views_directory_current_page"]: string(147) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php"
  ["views_file_current_page"]: string(116) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php"
  ["system_functions"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php"
  ["system_functions_lowercase"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/autoloader.php, Line: 276
         <strong>Arguments:</strong>
         0: "column_preferences"
      <strong>Function:</strong> autobooks_load_path_functions, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 180
         <strong>Arguments:</strong>
         0: ["api","data_table","column_preferences","reorder_columns"]
         1: "reorder_columns"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- launching layout-api with: router.class.php > route() 194: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->
<!-- launching layout-api with /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php-->
<!-- router.class.php > route() 209: regular view found
-->

<!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php
-->




<!--
********************************************************************************************************************************************************
calling: layout-api.edge.php > include() 45
array(5) {
  ["namespace"]: string(34) "api\data_table\column_preferences\"
  ["view"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["view_exists"]: string(4) "true"
  ["function_call"]: string(15) "reorder_columns"
  ["path_parts"]: array(2) {
    [0]: string(10) "data_table"
    [1]: string(18) "column_preferences"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->

<!-- calling in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php: layout-api.edge.php > include() 53: api\data_table\column_preferences\reorder_columns
-->

<!--
********************************************************************************************************************************************************
modal_tabs_init: layout-api.edge.php > include() 76
array(4) {
  ["modal_tabs_file"]: string(110) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/modal_tabs.class.php"
  ["file_exists"]: bool(true)
  ["class_exists"]: bool(false)
  ["hx_target"]: string(0) ""
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
column_preferences.api.php > api\data_table\column_preferences\reorder_columns() 409
array(4) {
  ["table_name"]: string(18) "autodesk_customers"
  ["callback"]: string(32) "autodesk\generate_customer_table"
  ["data_source"]: string(0) ""
  ["column_ids"]: array(7) {
    [0]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    [1]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
    [2]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
    [3]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
    [4]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
    [5]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
    [6]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\reorder_columns, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 86
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_preferences: column_preferences.api.php > api\data_table\column_preferences\reorder_columns() 463
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\reorder_columns, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 86
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_structure: column_preferences.api.php > api\data_table\column_preferences\reorder_columns() 464
array(7) {
  [0]: array(6) {
    ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    ["label"]: string(7) "Company"
    ["field"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
    ["filter"]: bool(false)
    ["fields"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
    ["visible"]: bool(true)
  }
  [1]: array(6) {
    ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
    ["label"]: string(8) "Customer"
    ["field"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["filter"]: bool(false)
    ["fields"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["visible"]: bool(true)
  }
  [2]: array(6) {
    ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
    ["label"]: string(3) "CSN"
    ["field"]: string(19) "endcust_account_csn"
    ["filter"]: bool(false)
    ["fields"]: array(1) {
      [0]: string(19) "endcust_account_csn"
    }
    ["visible"]: bool(true)
  }
  [3]: array(6) {
    ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
    ["label"]: string(13) "Primary Admin"
    ["field"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["filter"]: bool(false)
    ["fields"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["visible"]: bool(true)
  }
  [4]: array(6) {
    ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
    ["label"]: string(8) "Location"
    ["field"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["filter"]: bool(false)
    ["fields"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["visible"]: bool(true)
  }
  [5]: array(6) {
    ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
    ["label"]: string(13) "Last Modified"
    ["field"]: string(21) "endcust_last_modified"
    ["filter"]: bool(false)
    ["fields"]: array(1) {
      [0]: string(21) "endcust_last_modified"
    }
    ["visible"]: bool(true)
  }
  [6]: array(6) {
    ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
    ["label"]: string(7) "Actions"
    ["field"]: NULL
    ["filter"]: bool(false)
    ["fields"]: array(1) {
      [0]: NULL
    }
    ["visible"]: bool(true)
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\reorder_columns, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 86
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
api_process_criteria: data_table.class.php > api_process_criteria() 353
array(4) {
  ["table_name"]: string(18) "autodesk_customers"
  ["callback"]: string(32) "autodesk\generate_customer_table"
  ["data_source"]: string(0) ""
  ["column_ids"]: array(7) {
    [0]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    [1]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
    [2]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
    [3]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
    [4]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
    [5]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
    [6]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api_process_criteria, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 305
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> reload_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 488
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> api\data_table\column_preferences\reorder_columns, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 86
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
database: autodesk_customers.class.php > get_all() 79
array(4) {
  ["order_by"]: string(21) "endcust_last_modified"
  ["limit"]: int(300)
  ["search_columns"]: array(17) {
    [0]: string(19) "endcust.account_csn"
    [1]: string(12) "endcust.name"
    [2]: string(16) "endcust.address1"
    [3]: string(16) "endcust.address2"
    [4]: string(16) "endcust.address3"
    [5]: string(12) "endcust.city"
    [6]: string(22) "endcust.state_province"
    [7]: string(19) "endcust.postal_code"
    [8]: string(15) "endcust.country"
    [9]: string(32) "endcust.primary_admin_first_name"
    [10]: string(31) "endcust.primary_admin_last_name"
    [11]: string(27) "endcust.primary_admin_email"
    [12]: string(15) "endcust.team_id"
    [13]: string(17) "endcust.team_name"
    [14]: string(18) "endcust.first_name"
    [15]: string(17) "endcust.last_name"
    [16]: string(13) "endcust.email"
  }
  ["just_table"]: bool(true)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 72
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 329
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> reload_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 488
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...

----------------------------------------------------------------------------
-->

<!-- database.php > {closure}() 318: looking for endcust in query_tables
-->

<!-- database.php > {closure}() 321:   found
-->

<!--
********************************************************************************************************************************************************
$populate_column qt: database.php > {closure}() 323
array(1) {
  ["endcust"]: string(30) "FROM autodesk_accounts endcust"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> {closure}, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/database.php, Line: 402
         <strong>Arguments:</strong>
         0: "endcust_last_modified"
      <strong>Function:</strong> tcs_db_build_criteria, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 99
         <strong>Arguments:</strong>
         0: {"limit":300,"order_by":"endcust_last_modified","search_columns":["endcust.account_csn","endcust.nam...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- $where qt: database.php > tcs_db_build_criteria() 420: << empty string>>
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables_cols: database.php > tcs_db_build_tables() 456
array(1) {
  ["endcustlast_modified"]: string(46) "endcust.last_modified AS endcust_last_modified"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified"}
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 72
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables_tables: database.php > tcs_db_build_tables() 457
array(1) {
  ["endcust"]: string(30) "FROM autodesk_accounts endcust"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified"}
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 72
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables data: database.php > tcs_db_build_tables() 458
array(11) {
  [0]: string(12) "endcust_name"
  [1]: string(13) "endcust_email"
  [2]: string(18) "endcust_first_name"
  [3]: string(17) "endcust_last_name"
  [4]: string(19) "endcust_account_csn"
  [5]: string(32) "endcust_primary_admin_first_name"
  [6]: string(31) "endcust_primary_admin_last_name"
  [7]: string(12) "endcust_city"
  [8]: string(19) "endcust_postal_code"
  [9]: string(21) "endcust_last_modified"
  [10]: NULL
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified"}
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 72
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_email
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_first_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_last_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_account_csn
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_primary_admin_first_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_primary_admin_last_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_city
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_postal_code
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_last_modified
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables end: database.php > tcs_db_build_tables() 470
array(10) {
  ["endcustlast_modified"]: string(46) "endcust.last_modified AS endcust_last_modified"
  ["endcustname"]: string(28) "endcust.name AS endcust_name"
  ["endcustemail"]: string(30) "endcust.email AS endcust_email"
  ["endcustfirst_name"]: string(40) "endcust.first_name AS endcust_first_name"
  ["endcustlast_name"]: string(38) "endcust.last_name AS endcust_last_name"
  ["endcustaccount_csn"]: string(42) "endcust.account_csn AS endcust_account_csn"
  ["endcustprimary_admin_first_name"]: string(68) "endcust.primary_admin_first_name AS endcust_primary_admin_first_name"
  ["endcustprimary_admin_last_name"]: string(66) "endcust.primary_admin_last_name AS endcust_primary_admin_last_name"
  ["endcustcity"]: string(28) "endcust.city AS endcust_city"
  ["endcustpostal_code"]: string(42) "endcust.postal_code AS endcust_postal_code"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified","endcustname":"endcust.name...
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 72
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
finalq: autodesk_customers.class.php > database_get_all() 103
string(530) "SELECT endcust.last_modified AS endcust_last_modified, endcust.name AS endcust_name, endcust.email AS endcust_email, endcust.first_name AS endcust_first_name, endcust.last_name AS endcust_last_name, endcust.account_csn AS endcust_account_csn, endcust.primary_admin_first_name AS endcust_primary_admin_first_name, endcust.primary_admin_last_name AS endcust_primary_admin_last_name, endcust.city AS endcust_city, endcust.postal_code AS endcust_postal_code FROM autodesk_accounts endcust   ORDER BY endcust_last_modified    LIMIT 300"

    ----------------------------------------------------------------------------
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 72
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 329
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- database.php > tcs_db_query() 509: SELECT endcust.last_modified AS endcust_last_modified, endcust.name AS endcust_name, endcust.email AS endcust_email, endcust.first_name AS endcust_first_name, endcust.last_name AS endcust_last_name, endcust.account_csn AS endcust_account_csn, endcust.primary_admin_first_name AS endcust_primary_admin_first_name, endcust.primary_admin_last_name AS endcust_primary_admin_last_name, endcust.city AS endcust_city, endcust.postal_code AS endcust_postal_code FROM autodesk_accounts endcust   ORDER BY endcust_last_modified    LIMIT 300
-->

<!--
********************************************************************************************************************************************************
build column filters end: data_table.class.php > build_column_filters() 422
array(7) {
  [0]: array(2) {
    ["label"]: string(7) "Company"
    ["field"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
  }
  [1]: array(2) {
    ["label"]: string(8) "Customer"
    ["field"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
  }
  [2]: array(3) {
    ["label"]: string(3) "CSN"
    ["field"]: string(19) "endcust_account_csn"
    ["selected"]: string(3) "CSN"
  }
  [3]: array(2) {
    ["label"]: string(13) "Primary Admin"
    ["field"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
  }
  [4]: array(2) {
    ["label"]: string(8) "Location"
    ["field"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
  }
  [5]: array(3) {
    ["label"]: string(13) "Last Modified"
    ["field"]: string(21) "endcust_last_modified"
    ["selected"]: string(13) "Last Modified"
  }
  [6]: array(3) {
    ["label"]: string(7) "Actions"
    ["content"]: object(Closure)#6 (1) {
      ["parameter"]: array(1) {
        ["$item"]: string(10) "<required>"
      }
    }
    ["selected"]: string(7) "Actions"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> build_column_filters, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 178
         <strong>Arguments:</strong>
      <strong>Function:</strong> process_data_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 79
         <strong>Arguments:</strong>
         0: {"table_id":"customers","db_table":"autodesk_accounts","columns":[{"label":"Company","field":["endcu...
         1: [{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust_email":"...
         2: ""
         3: "autodesk\\generate_customer_table"
         4: []
         5: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
         6: []
         7: false
         8: false
         9: false
         10: false
         11: null
         12: "autodesk_customers"
         13: []
         14: true
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 329
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- datad: data_table.class.php > process_data_table() 286: tcs returning full table
-->

<!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php
-->


<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 622
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php, Line: 39
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
data_table_storage.class.php > prepare_template_data() 663
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php, Line: 39
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 669
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php, Line: 39
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 676
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php, Line: 39
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"

----------------------------------------------------------------------------
-->






<input type="hidden" class="data_table_filter" name="callback" value="autodesk\generate_customer_table">
<input type="hidden" class="data_table_filter" name="table_name" value="autodesk_customers">
<input type="hidden" class="data_table_filter" name="data_source_id" value="">
<div class="relative">
    <div class="absolute top-0 right-0 z-10 p-2">

        <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php
        -->


        <!--
        ********************************************************************************************************************************************************
        tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 622
        array(6) {
          ["hidden"]: array(0) {
          }
          ["structure"]: array(7) {
            [0]: array(6) {
              ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["visible"]: bool(true)
            }
            [1]: array(6) {
              ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["visible"]: bool(true)
            }
            [2]: array(6) {
              ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(19) "endcust_account_csn"
              }
              ["visible"]: bool(true)
            }
            [3]: array(6) {
              ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["visible"]: bool(true)
            }
            [4]: array(6) {
              ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["visible"]: bool(true)
            }
            [5]: array(6) {
              ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(21) "endcust_last_modified"
              }
              ["visible"]: bool(true)
            }
            [6]: array(6) {
              ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
              ["label"]: string(7) "Actions"
              ["field"]: NULL
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: NULL
              }
              ["visible"]: bool(true)
            }
          }
          ["columns"]: array(7) {
            [0]: array(2) {
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
            }
            [1]: array(2) {
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
            }
            [2]: array(3) {
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["selected"]: string(3) "CSN"
            }
            [3]: array(2) {
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
            }
            [4]: array(2) {
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
            }
            [5]: array(3) {
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["selected"]: string(13) "Last Modified"
            }
            [6]: array(3) {
              ["label"]: string(7) "Actions"
              ["content"]: array(0) {
              }
              ["selected"]: string(7) "Actions"
            }
          }
          ["data_source_type"]: string(9) "hardcoded"
          ["data_source_id"]: NULL
          ["created_at"]: string(19) "2025-09-01 10:41:22"
        }

            ----------------------------------------------------------------------------
              <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php, Line: 35
                 <strong>Arguments:</strong>
                 0: {"items":[],"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_e...
              <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
                 <strong>Arguments:</strong>
                 0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"
              <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
                 <strong>Arguments:</strong>
                 0: {"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_email"],"fil...
                 1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"

        ----------------------------------------------------------------------------
        -->

        <!--
        ********************************************************************************************************************************************************
        data_table_storage.class.php > prepare_template_data() 663
        array(6) {
          ["hidden"]: array(0) {
          }
          ["structure"]: array(7) {
            [0]: array(6) {
              ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["visible"]: bool(true)
            }
            [1]: array(6) {
              ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["visible"]: bool(true)
            }
            [2]: array(6) {
              ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(19) "endcust_account_csn"
              }
              ["visible"]: bool(true)
            }
            [3]: array(6) {
              ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["visible"]: bool(true)
            }
            [4]: array(6) {
              ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["visible"]: bool(true)
            }
            [5]: array(6) {
              ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(21) "endcust_last_modified"
              }
              ["visible"]: bool(true)
            }
            [6]: array(6) {
              ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
              ["label"]: string(7) "Actions"
              ["field"]: NULL
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: NULL
              }
              ["visible"]: bool(true)
            }
          }
          ["columns"]: array(7) {
            [0]: array(2) {
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
            }
            [1]: array(2) {
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
            }
            [2]: array(3) {
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["selected"]: string(3) "CSN"
            }
            [3]: array(2) {
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
            }
            [4]: array(2) {
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
            }
            [5]: array(3) {
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["selected"]: string(13) "Last Modified"
            }
            [6]: array(3) {
              ["label"]: string(7) "Actions"
              ["content"]: array(0) {
              }
              ["selected"]: string(7) "Actions"
            }
          }
          ["data_source_type"]: string(9) "hardcoded"
          ["data_source_id"]: NULL
          ["created_at"]: string(19) "2025-09-01 10:41:22"
        }

            ----------------------------------------------------------------------------
              <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php, Line: 35
                 <strong>Arguments:</strong>
                 0: {"items":[],"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_e...
              <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
                 <strong>Arguments:</strong>
                 0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"
              <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
                 <strong>Arguments:</strong>
                 0: {"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_email"],"fil...
                 1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"

        ----------------------------------------------------------------------------
        -->

        <!--
        ********************************************************************************************************************************************************
        tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 669
        array(6) {
          ["hidden"]: array(0) {
          }
          ["structure"]: array(7) {
            [0]: array(6) {
              ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["visible"]: bool(true)
            }
            [1]: array(6) {
              ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["visible"]: bool(true)
            }
            [2]: array(6) {
              ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(19) "endcust_account_csn"
              }
              ["visible"]: bool(true)
            }
            [3]: array(6) {
              ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["visible"]: bool(true)
            }
            [4]: array(6) {
              ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["visible"]: bool(true)
            }
            [5]: array(6) {
              ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(21) "endcust_last_modified"
              }
              ["visible"]: bool(true)
            }
            [6]: array(6) {
              ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
              ["label"]: string(7) "Actions"
              ["field"]: NULL
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: NULL
              }
              ["visible"]: bool(true)
            }
          }
          ["columns"]: array(7) {
            [0]: array(2) {
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
            }
            [1]: array(2) {
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
            }
            [2]: array(3) {
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["selected"]: string(3) "CSN"
            }
            [3]: array(2) {
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
            }
            [4]: array(2) {
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
            }
            [5]: array(3) {
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["selected"]: string(13) "Last Modified"
            }
            [6]: array(3) {
              ["label"]: string(7) "Actions"
              ["content"]: array(0) {
              }
              ["selected"]: string(7) "Actions"
            }
          }
          ["data_source_type"]: string(9) "hardcoded"
          ["data_source_id"]: NULL
          ["created_at"]: string(19) "2025-09-01 10:41:22"
        }

            ----------------------------------------------------------------------------
              <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php, Line: 35
                 <strong>Arguments:</strong>
                 0: {"items":[],"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_e...
              <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
                 <strong>Arguments:</strong>
                 0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"
              <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
                 <strong>Arguments:</strong>
                 0: {"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_email"],"fil...
                 1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"

        ----------------------------------------------------------------------------
        -->

        <!--
        ********************************************************************************************************************************************************
        tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 676
        array(6) {
          ["hidden"]: array(0) {
          }
          ["structure"]: array(7) {
            [0]: array(6) {
              ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
              ["visible"]: bool(true)
            }
            [1]: array(6) {
              ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
              ["visible"]: bool(true)
            }
            [2]: array(6) {
              ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(19) "endcust_account_csn"
              }
              ["visible"]: bool(true)
            }
            [3]: array(6) {
              ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
              ["visible"]: bool(true)
            }
            [4]: array(6) {
              ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["filter"]: bool(false)
              ["fields"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
              ["visible"]: bool(true)
            }
            [5]: array(6) {
              ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: string(21) "endcust_last_modified"
              }
              ["visible"]: bool(true)
            }
            [6]: array(6) {
              ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
              ["label"]: string(7) "Actions"
              ["field"]: NULL
              ["filter"]: bool(false)
              ["fields"]: array(1) {
                [0]: NULL
              }
              ["visible"]: bool(true)
            }
          }
          ["columns"]: array(7) {
            [0]: array(2) {
              ["label"]: string(7) "Company"
              ["field"]: array(2) {
                [0]: string(12) "endcust_name"
                [1]: string(13) "endcust_email"
              }
            }
            [1]: array(2) {
              ["label"]: string(8) "Customer"
              ["field"]: array(2) {
                [0]: string(18) "endcust_first_name"
                [1]: string(17) "endcust_last_name"
              }
            }
            [2]: array(3) {
              ["label"]: string(3) "CSN"
              ["field"]: string(19) "endcust_account_csn"
              ["selected"]: string(3) "CSN"
            }
            [3]: array(2) {
              ["label"]: string(13) "Primary Admin"
              ["field"]: array(2) {
                [0]: string(32) "endcust_primary_admin_first_name"
                [1]: string(31) "endcust_primary_admin_last_name"
              }
            }
            [4]: array(2) {
              ["label"]: string(8) "Location"
              ["field"]: array(2) {
                [0]: string(12) "endcust_city"
                [1]: string(19) "endcust_postal_code"
              }
            }
            [5]: array(3) {
              ["label"]: string(13) "Last Modified"
              ["field"]: string(21) "endcust_last_modified"
              ["selected"]: string(13) "Last Modified"
            }
            [6]: array(3) {
              ["label"]: string(7) "Actions"
              ["content"]: array(0) {
              }
              ["selected"]: string(7) "Actions"
            }
          }
          ["data_source_type"]: string(9) "hardcoded"
          ["data_source_id"]: NULL
          ["created_at"]: string(19) "2025-09-01 10:41:22"
        }

            ----------------------------------------------------------------------------
              <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php, Line: 35
                 <strong>Arguments:</strong>
                 0: {"items":[],"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_e...
              <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
                 <strong>Arguments:</strong>
                 0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"
              <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
                 <strong>Arguments:</strong>
                 0: {"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_email"],"fil...
                 1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"

        ----------------------------------------------------------------------------
        -->



        <!--
        ********************************************************************************************************************************************************
        data_table_column_manager_start: data-table-column-manager.edge.php > include() 85
        array(1) {
          ["data_table_column_manager_start"]: array(4) {
            ["table_name"]: string(18) "autodesk_customers"
            ["available_fields"]: array(0) {
            }
            ["current_data_source_type"]: string(9) "hardcoded"
            ["current_data_source_id"]: NULL
          }
        }

            ----------------------------------------------------------------------------
              <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
                 <strong>Arguments:</strong>
                 0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"
              <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
                 <strong>Arguments:</strong>
                 0: {"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_email"],"fil...
                 1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-column-manager.edge.php"
              <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php, Line: 78
                 <strong>Arguments:</strong>
                 0: "data-table-column-manager"
                 1: {"columns":[{"label":"Company","field":"endcust_name","fields":["endcust_name","endcust_email"],"fil...

        ----------------------------------------------------------------------------
        -->
        <div id="column_manager_autodesk_customers"
             x-data="{
         open: false,
         newColumnName: '',
         showAddColumn: false,

         toggleRenameInput(index) {
             const input = this.$refs['renameInput_' + index];
             if (!input) return;

             const isHidden = input.style.display === 'none' || !input.style.display;
             input.style.display = isHidden ? 'block' : 'none';

             if (isHidden) {
                 this.$nextTick(() => {
                     input.querySelector('input').focus();
                     input.querySelector('input').select();
                 });
             }
         },

         hideRenameInput(index) {
             const input = this.$refs['renameInput_' + index];
             if (input) input.style.display = 'none';
         },

         toggleActionForm(index) {
             const form = this.$refs['actionForm_' + index];
             if (!form) return;

             const isHidden = form.style.display === 'none' || !form.style.display;
             form.style.display = isHidden ? 'block' : 'none';
         },

         hideActionForm(index) {
             const form = this.$refs['actionForm_' + index];
             if (form) form.style.display = 'none';
         }

     }"

             class="relative inline-block text-left"
             @click.away="open = false">


            <button type="button"
                    @click="open = !open"
                    class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                    aria-expanded="false"
                    aria-haspopup="true">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z"/>
                </svg>
                Columns
                <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5"/>
                </svg>
            </button>
            <div x-show="open"
                 x-transition:enter="transition ease-out duration-100"
                 x-transition:enter-start="transform opacity-0 scale-95"
                 x-transition:enter-end="transform opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="transform opacity-100 scale-100"
                 x-transition:leave-end="transform opacity-0 scale-95"
                 class="absolute right-0 z-20 mt-2 w-126 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
                 style="height: 80vh;"
                 role="menu"
                 aria-orientation="vertical"
                 tabindex="-1"
                 id="column_manager_panel_autodesk_customers"
            >



                <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php
                -->


                <div class="p-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
                        <div class="flex gap-2">

                            <button type="button"
                                    @click="showAddColumn = !showAddColumn"
                                    class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                + Column
                            </button>
                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_all_columns"
                                    hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                Show All
                            </button>
                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/hide_all_columns"
                                    hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                                Hide All
                            </button>
                        </div>
                    </div>


                    <div class="flex items-center space-x-3 mb-3">
                        <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
                        <select id="data-source-select"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source"
                                hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-trigger="change"
                                name="data_source_selection"
                                class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="hardcoded" selected>
                                Default (Hardcoded Data)
                            </option>
                            <optgroup label="Other">
                                <option value="1"
                                >
                                    Autodesk_autorenew                                                            </option>
                            </optgroup>
                            <optgroup label="Autodesk Integration">
                                <option value="30"
                                >
                                    Autodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                                <option value="31"
                                >
                                    Autodesk Accounts                                                                    - Customer account information                                                            </option>
                                <option value="32"
                                >
                                    Expiring Subscriptions                                                                    - Active subscriptions expiring within 90 days                                                            </option>
                                <option value="33"
                                >
                                    Autodesk Email History                                                                    - Email communication history                                                            </option>
                                <option value="34"
                                >
                                    Copy of Autodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                                <option value="35"
                                >
                                    Full Autodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                                <option value="51"
                                >
                                    tstAutodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                                <option value="63"
                                >
                                    Autodesk_products                                                            </option>
                            </optgroup>
                            <optgroup label="Csv_import">
                                <option value="79"
                                >
                                    CSV Import: Sketchup                                                                    - Auto-generated data source for CSV import (79 rows, 59 columns)                                                            </option>
                            </optgroup>
                        </select>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Hardcoded
                    </span>
                    </div>


                    <div class="flex items-center space-x-3 mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="apply_default_config_autodesk_customers"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/unified_field_definitions/apply_default_config"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   hx-trigger="change"
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="apply_default_config_autodesk_customers" class="ml-2 text-xs font-medium text-gray-700">
                                Apply default field configuration
                            </label>
                        </div>
                        <div class="flex-1 flex items-center">
                            <div class="relative" x-data="{ showTooltip: false }">
                                <button type="button"
                                        @mouseenter="showTooltip = true"
                                        @mouseleave="showTooltip = false"
                                        class="ml-2 text-gray-400 hover:text-gray-600">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </button>
                                <div x-show="showTooltip"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0 transform scale-95"
                                     x-transition:enter-end="opacity-100 transform scale-100"
                                     x-transition:leave="transition ease-in duration-150"
                                     x-transition:leave-start="opacity-100 transform scale-100"
                                     x-transition:leave-end="opacity-0 transform scale-95"
                                     class="absolute z-50 w-64 p-2 mt-1 text-xs text-white bg-gray-800 rounded shadow-lg -top-2 left-6">
                                    Uses the "Show by default" settings from unified field definitions to configure initial column
                                    display.
                                    <div class="absolute top-2 -left-1 w-2 h-2 bg-gray-800 transform rotate-45"></div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div x-show="showAddColumn" x-transition class="mb-3 p-2 bg-blue-50 rounded border">
                        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_column"
                              hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                              hx-target=".data_table"
                              hx-swap="outerHTML"
                              @htmx:after-request="showAddColumn = false; newColumnName = ''"
                              class="flex gap-2 items-center">
                            <input type="text"
                                   name="column_name"
                                   x-model="newColumnName"
                                   placeholder="Column name (e.g., Contact Info)"
                                   class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                                   required>
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Add
                            </button>
                            <button type="button"
                                    @click="showAddColumn = false"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>

                    <div class="text-xs text-gray-500">
                        Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
                    </div>
                </div>


                <div class="flex-1 overflow-y-auto p-4" >
                    <div class="sortable space-y-3"
                         data-sortable-group="columns"
                         data-sortable-handle=".column-drag-handle"
                         hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"
                         hx-trigger="sorted"
                         hx-target=".data_table"
                         hx-swap="outerHTML"
                         hx-include=".column-data"
                         x-ref="columnList">
                        <input type="hidden" name="table_name" class="column-data" value="autodesk_customers">
                        <input type="hidden" name="callback" class="column-data" value="autodesk\generate_customer_table">
                        <input type="hidden" name="data_source" class="column-data" value="">

                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_0_1c76cbfe21c6f44c1d1e59d54f3e4420">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_0_1c76cbfe21c6f44c1d1e59d54f3e4420">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="Company"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420", "loop_index": "0"}'
                                            hx-target=".add_action_form_container-col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_0_1c76cbfe21c6f44c1d1e59d54f3e4420">



                                <div class="add_action_form_container_col_0_1c76cbfe21c6f44c1d1e59d54f3e4420">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_name">
                                    <input type="hidden" name="field_names[]" value="endcust_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420", "field_name": "endcust_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_email">
                                    <input type="hidden" name="field_names[]" value="endcust_email">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_email</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420", "field_name": "endcust_email"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_1_ce26601dac0dea138b7295f02b7620a7">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_1_ce26601dac0dea138b7295f02b7620a7">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="Customer"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7", "loop_index": "1"}'
                                            hx-target=".add_action_form_container-col_1_ce26601dac0dea138b7295f02b7620a7"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_1_ce26601dac0dea138b7295f02b7620a7"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_1_ce26601dac0dea138b7295f02b7620a7">



                                <div class="add_action_form_container_col_1_ce26601dac0dea138b7295f02b7620a7">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_first_name">
                                    <input type="hidden" name="field_names[]" value="endcust_first_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_first_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7", "field_name": "endcust_first_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_last_name">
                                    <input type="hidden" name="field_names[]" value="endcust_last_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_last_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_1_ce26601dac0dea138b7295f02b7620a7", "field_name": "endcust_last_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_2_adc4365ac462ffb7ae7f47f348acbad4">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_2_adc4365ac462ffb7ae7f47f348acbad4">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_2_adc4365ac462ffb7ae7f47f348acbad4", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="CSN"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_2_adc4365ac462ffb7ae7f47f348acbad4"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_2_adc4365ac462ffb7ae7f47f348acbad4"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_2_adc4365ac462ffb7ae7f47f348acbad4", "loop_index": "2"}'
                                            hx-target=".add_action_form_container-col_2_adc4365ac462ffb7ae7f47f348acbad4"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_2_adc4365ac462ffb7ae7f47f348acbad4"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_2_adc4365ac462ffb7ae7f47f348acbad4"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_2_adc4365ac462ffb7ae7f47f348acbad4">



                                <div class="add_action_form_container_col_2_adc4365ac462ffb7ae7f47f348acbad4">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_account_csn">
                                    <input type="hidden" name="field_names[]" value="endcust_account_csn">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_account_csn</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_2_adc4365ac462ffb7ae7f47f348acbad4", "field_name": "endcust_account_csn"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="Primary Admin"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf", "loop_index": "3"}'
                                            hx-target=".add_action_form_container-col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf">



                                <div class="add_action_form_container_col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_primary_admin_first_name">
                                    <input type="hidden" name="field_names[]" value="endcust_primary_admin_first_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_primary_admin_first_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf", "field_name": "endcust_primary_admin_first_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_primary_admin_last_name">
                                    <input type="hidden" name="field_names[]" value="endcust_primary_admin_last_name">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_primary_admin_last_name</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf", "field_name": "endcust_primary_admin_last_name"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_4_ce5bf551379459c1c61d2a204061c455">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_4_ce5bf551379459c1c61d2a204061c455">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="Location"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455", "loop_index": "4"}'
                                            hx-target=".add_action_form_container-col_4_ce5bf551379459c1c61d2a204061c455"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_4_ce5bf551379459c1c61d2a204061c455"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_4_ce5bf551379459c1c61d2a204061c455">



                                <div class="add_action_form_container_col_4_ce5bf551379459c1c61d2a204061c455">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_city">
                                    <input type="hidden" name="field_names[]" value="endcust_city">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_city</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455", "field_name": "endcust_city"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_postal_code">
                                    <input type="hidden" name="field_names[]" value="endcust_postal_code">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_postal_code</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_4_ce5bf551379459c1c61d2a204061c455", "field_name": "endcust_postal_code"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_5_84ba4cf46c874b0b5951eb3b75298329">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_5_84ba4cf46c874b0b5951eb3b75298329">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_5_84ba4cf46c874b0b5951eb3b75298329", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="Last Modified"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_5_84ba4cf46c874b0b5951eb3b75298329"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_5_84ba4cf46c874b0b5951eb3b75298329"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_5_84ba4cf46c874b0b5951eb3b75298329", "loop_index": "5"}'
                                            hx-target=".add_action_form_container-col_5_84ba4cf46c874b0b5951eb3b75298329"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_5_84ba4cf46c874b0b5951eb3b75298329"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_5_84ba4cf46c874b0b5951eb3b75298329"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_5_84ba4cf46c874b0b5951eb3b75298329">



                                <div class="add_action_form_container_col_5_84ba4cf46c874b0b5951eb3b75298329">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="endcust_last_modified">
                                    <input type="hidden" name="field_names[]" value="endcust_last_modified">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">endcust_last_modified</span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_5_84ba4cf46c874b0b5951eb3b75298329", "field_name": "endcust_last_modified"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                        <div class="border border-gray-200 rounded-lg bg-white" data-column-id="col_6_06df33001c1d7187fdd81ea1f5b277aa">
                            <input type="hidden" name="column_ids[]" class="column-data" value="col_6_06df33001c1d7187fdd81ea1f5b277aa">


                            <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                                <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>


                                <label class="flex items-center flex-1 cursor-pointer">
                                    <input type="checkbox"
                                           checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                           hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_6_06df33001c1d7187fdd81ea1f5b277aa", "data_source": ""}'
                                           hx-target=".data_table"
                                           hx-swap="outerHTML"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="">
                            <input type="text"
                                   name="label"
                                   value="Actions"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_6_06df33001c1d7187fdd81ea1f5b277aa"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                                </label>

                                <div class="">
                                    <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-trigger="change[target.value != '']"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_6_06df33001c1d7187fdd81ea1f5b277aa"}'
                                            name="field_name"
                                            @htmx:after-request="$event.target.value = ''"
                                            class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                        <option value="">+ Field...</option>
                                    </select>
                                </div>

                                <div class="">
                                    <button type="button"
                                            hx-get="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_add_action_form"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_6_06df33001c1d7187fdd81ea1f5b277aa", "loop_index": "6"}'
                                            hx-target=".add_action_form_container-col_6_06df33001c1d7187fdd81ea1f5b277aa"
                                            hx-swap="innerHTML"
                                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                        + Add Action Button
                                    </button>
                                </div>



                                <button type="button"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                        hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_6_06df33001c1d7187fdd81ea1f5b277aa"}'
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-confirm="Are you sure you want to remove this column?"
                                        class="ml-2 text-red-400 hover:text-red-600"
                                        title="Remove Column">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>



                            <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                                 data-sortable-group="fields-col_6_06df33001c1d7187fdd81ea1f5b277aa"
                                 data-sortable-handle=".field-drag-handle, .action-drag-handle"
                                 hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
                                 hx-trigger="sorted"
                                 hx-target=".data_table"
                                 hx-swap="outerHTML">
                                <input type="hidden" name="table_name" value="autodesk_customers">
                                <input type="hidden" name="callback" value="autodesk\generate_customer_table">
                                <input type="hidden" name="data_source" value="">
                                <input type="hidden" name="target_column_id" value="col_6_06df33001c1d7187fdd81ea1f5b277aa">



                                <div class="add_action_form_container_col_6_06df33001c1d7187fdd81ea1f5b277aa">

                                </div>


                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="">
                                    <input type="hidden" name="field_names[]" value="">

                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700"></span>

                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table", "column_id": "col_6_06df33001c1d7187fdd81ea1f5b277aa", "field_name": ""}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>




                            </div>
                        </div>
                    </div>
                </div>


                <div class="p-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex justify-between items-center">
                        <div class="flex gap-2 items-center">
                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/hide_all_columns"
                                    hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                                Hide All
                            </button>
                            <span class="text-xs text-gray-500">7 columns, 11 fields</span>
                        </div>
                        <button type="button"
                                @click="open = false"
                                class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                            Done
                        </button>
                    </div>
                </div>

                <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager-panel.edge.php

                -->
            </div>
        </div>
        <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-column-manager.edge.php

        -->
    </div>
</div>

<!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php
-->


<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 622
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
data_table_storage.class.php > prepare_template_data() 663
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 669
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 676
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["field"]: NULL
      ["filter"]: bool(false)
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-01 10:41:22"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"

----------------------------------------------------------------------------
-->


<!--
********************************************************************************************************************************************************
tcs_structs: data-table-structure.edge.php > include() 53
array(3) {
  ["items"]: array(5) {
    [0]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(17) "Conrad Energy Ltd"
      ["endcust_email"]: string(30) "<EMAIL>"
      ["endcust_first_name"]: string(2) "IT"
      ["endcust_last_name"]: string(8) "Services"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(6) "Oxford"
      ["endcust_postal_code"]: string(7) "OX4 4GP"
    }
    [1]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(7) "POQ LTD"
      ["endcust_email"]: string(16) "<EMAIL>"
      ["endcust_first_name"]: string(6) "Sanjay"
      ["endcust_last_name"]: string(6) "Odedra"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(8) "Stanmore"
      ["endcust_postal_code"]: string(7) "HA7 3DA"
    }
    [2]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(20) "YOUR GOLF TRAVEL Ltd"
      ["endcust_email"]: string(33) "<EMAIL>"
      ["endcust_first_name"]: string(6) "Antony"
      ["endcust_last_name"]: string(7) "Puncher"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: string(6) "Antony"
      ["endcust_primary_admin_last_name"]: string(7) "Puncher"
      ["endcust_city"]: string(6) "London"
      ["endcust_postal_code"]: string(8) "EC1R 3AU"
    }
    [3]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(26) "STRATEGIC PM SOLUTIONS Ltd"
      ["endcust_email"]: string(28) "<EMAIL>"
      ["endcust_first_name"]: string(5) "Sarah"
      ["endcust_last_name"]: string(11) "Park-Murray"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: string(5) "Sarah"
      ["endcust_primary_admin_last_name"]: string(11) "Park-Murray"
      ["endcust_city"]: string(9) "Isleworth"
      ["endcust_postal_code"]: string(7) "TW7 5LF"
    }
    [4]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(20) "MS CAD Solutions Ltd"
      ["endcust_email"]: string(19) "<EMAIL>"
      ["endcust_first_name"]: string(4) "Mark"
      ["endcust_last_name"]: string(7) "SINNETT"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(8) "Barnsley"
      ["endcust_postal_code"]: string(7) "S72 9HR"
    }
  }
  ["columns"]: array(7) {
    [0]: array(11) {
      ["label"]: string(7) "Company"
      ["field"]: string(12) "endcust_name"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [1]: array(11) {
      ["label"]: string(8) "Customer"
      ["field"]: string(18) "endcust_first_name"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [2]: array(11) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [3]: array(11) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: string(32) "endcust_primary_admin_first_name"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [4]: array(11) {
      ["label"]: string(8) "Location"
      ["field"]: string(12) "endcust_city"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [5]: array(11) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [6]: array(11) {
      ["label"]: string(7) "Actions"
      ["field"]: string(0) ""
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: object(Closure)#6 (1) {
        ["parameter"]: array(1) {
          ["$item"]: string(10) "<required>"
        }
      }
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
  }
  ["available_fields"]: array(0) {
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-structure.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php, Line: 106
         <strong>Arguments:</strong>
         0: "data-table-structure"
         1: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...

----------------------------------------------------------------------------
-->

<table class="min-w-full border-collapse search_target data_table ">
    <thead>
    <tr>
        <th scope="col"
            class="relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8"
            style="isolation: isolate;"
            data-column-field="endcust_name">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>Company</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_name_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_name","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="endcust_first_name">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>Customer</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_first_name_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_first_name","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="endcust_account_csn">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>CSN</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_account_csn_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_account_csn","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="endcust_primary_admin_first_name">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>Primary Admin</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_primary_admin_first_name_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_primary_admin_first_name","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="endcust_city">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>Location</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_city_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_city","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="endcust_last_modified">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>Last Modified</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_last_modified_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_last_modified","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_14: false,
            open_14: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_14 = !open_14">
                        <span>Actions</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_14"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_14 = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-filter.edge.php

            -->
        </th>
    </tr>
    </thead>
    <tbody class="bg-white data_table_body">

    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-rows.edge.php
    -->


    <!--
    ********************************************************************************************************************************************************
    tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 622
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["field"]: NULL
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-01 10:41:22"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->

    <!--
    ********************************************************************************************************************************************************
    data_table_storage.class.php > prepare_template_data() 663
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["field"]: NULL
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-01 10:41:22"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->

    <!--
    ********************************************************************************************************************************************************
    tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 669
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["field"]: NULL
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-01 10:41:22"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->

    <!--
    ********************************************************************************************************************************************************
    tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 676
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["field"]: NULL
          ["filter"]: bool(false)
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-01 10:41:22"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->





    <!--
    ********************************************************************************************************************************************************
    data-table-rows.edge.php > include() 61
    array(5) {
      ["items"]: array(5) {
        [0]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(24) "<EMAIL>"
          ["endcust_first_name"]: string(4) "Mike"
          ["endcust_last_name"]: string(5) "Jones"
          ["endcust_account_csn"]: int(**********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [1]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(30) "<EMAIL>"
          ["endcust_first_name"]: string(4) "Liam"
          ["endcust_last_name"]: string(7) "Mullins"
          ["endcust_account_csn"]: int(********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [2]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(28) "<EMAIL>"
          ["endcust_first_name"]: string(5) "Barry"
          ["endcust_last_name"]: string(8) "Williams"
          ["endcust_account_csn"]: int(********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [3]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(27) "<EMAIL>"
          ["endcust_first_name"]: string(4) "Adam"
          ["endcust_last_name"]: string(9) "Strudwick"
          ["endcust_account_csn"]: int(*********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [4]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(24) "<EMAIL>"
          ["endcust_first_name"]: string(7) "Richard"
          ["endcust_last_name"]: string(7) "Greaves"
          ["endcust_account_csn"]: int(*********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
      }
      ["count"]: int(300)
      ["columns"]: array(7) {
        [0]: array(11) {
          ["label"]: string(7) "Company"
          ["field"]: string(12) "endcust_name"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [1]: array(11) {
          ["label"]: string(8) "Customer"
          ["field"]: string(18) "endcust_first_name"
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [2]: array(11) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [3]: array(11) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: string(32) "endcust_primary_admin_first_name"
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [4]: array(11) {
          ["label"]: string(8) "Location"
          ["field"]: string(12) "endcust_city"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [5]: array(11) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [6]: array(11) {
          ["label"]: string(7) "Actions"
          ["field"]: string(0) ""
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: object(Closure)#6 (1) {
            ["parameter"]: array(1) {
              ["$item"]: string(10) "<required>"
            }
          }
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
      }
      ["column_count"]: int(7)
      ["available_fields"]: array(0) {
      }
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 156
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 149
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table-rows.edge.php"
          <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php, Line: 84
             <strong>Arguments:</strong>
             0: "data-table-rows"
             1: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...

    ----------------------------------------------------------------------------
    -->
    <tr class="border-t border-gray-300 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Conrad Energy Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    IT<br>Services                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Oxford<br>OX4 4GP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    POQ LTD<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sanjay<br>Odedra                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Stanmore<br>HA7 3DA                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    YOUR GOLF TRAVEL Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Antony<br>Puncher                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Antony<br>Puncher                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    London<br>EC1R 3AU                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    STRATEGIC PM SOLUTIONS Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sarah<br>Park-Murray                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sarah<br>Park-Murray                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Isleworth<br>TW7 5LF                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    MS CAD Solutions Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Mark<br>SINNETT                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Barnsley<br>S72 9HR                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Proactive Learning Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Matt<br>Dawson                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    St. Neots<br>PE19 5ZA                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Vida Design Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Joe<br>Dobson                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Joe<br>Dobson                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bristol<br>BS1 6AA                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":null,"tab_title":"Customer "}' @click='showModal = true' data-tab-title='Customer ' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Serena<br>Federico                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Camland Developments Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Adam<br>Owen                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Adam<br>Owen                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Swanscombe<br>DA10 0DF                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    TD SYNNEX UK Limited                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    ADR Consulting<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Alexis<br>Rouzee                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Alexis<br>Rouzee                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Canterbury<br>CT4 6HD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Coleman Hicks Partnership<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sam<br>Cook                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Kidlington<br>OX5 2DN                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Rosie Wilkins Landscape Design<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Rosie<br>Wilkins                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Rosie<br>Wilkins                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sheffield<br>S5 7DD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    STEEL LINE Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Craig<br>Stanley                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sheffield<br>S13 9NR                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Jonathan Carter Design<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Jonathan<br>Carter                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Jonathan<br>Carter                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    London<br>SE4 1YD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    MACLEAN ARCHITECTURE<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    James<br>Maclean                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    James<br>Maclean                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Knaresborough<br>HG5 0AD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Mark Cheetham                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Stockton-On-Tees<br>TS17 6AQ                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Clarice<br>Elliot                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    *********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    TCS CAD & BIM Solutions Limited<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Aurangzaib<br>Mahmood                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Aurangzaib<br>Mahmood                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Stockton On Tees<br>TS17 6BX                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    COVENTRY CONSTRUCTION Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Roman<br>Pundyk                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Coventry<br>CV4 9AP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    POQ Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sanjay<br>Odedra                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sanjay<br>Odedra                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bushey<br>WD23 1NP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    WISBECH GRAMMAR SCHOOL<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Andrew<br>Dighton                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Peterborough<br>PE7 2AX                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    CPI Group<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Wes<br>Dennehy                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Wes<br>Dennehy                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Chatham<br>ME5 8TD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    BLOCK ARCHITECTS Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bruce<br>O'Brien                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bellshill<br>ML4 3NP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    KELBROOK MILL Co.<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Keith<br>Hendry                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Thirsk<br>YO7 4AZ                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Pace Property Reports & Projec<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Phil<br>Griffiths                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    **********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Wallasey<br>CH44 8EE                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Kim<br>West                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    ********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":********,"tab_title":"Customer ********"}' @click='showModal = true' data-tab-title='Customer ********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Susan<br>Fisher                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    *********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Ian<br>Kirkland                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    *********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Peter<br>Crowther                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    *********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="endcust_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Matt<br>Button                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_account_csn">
            <div class="flex items-center gap-2"><div class="flex-1">
                    ********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_primary_admin_first_name">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_city">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":********,"tab_title":"Customer ********"}' @click='showModal = true' data-tab-title='Customer ********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>

    <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-rows.edge.php

    -->
    </tbody>
    <tfoot>
    <tr>
        <td colspan="7">

            <!-- edge: edge.class.php > phprender() 155: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/pagination-strip.edge.php
            -->


            <div class="flex bottom-0 items-center justify-between border-t border-gray-200 bg-white px-4 py-2 sm:px-6">
                <div class="flex flex-1 justify-between sm:hidden">
                    <a href="#"
                       hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                       hx-target=".search_target"
                       hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                       hx-vals='{"page": 1}'
                       class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
                    <a href="#"
                       hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                       hx-target=".search_target"
                       hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                       hx-vals='{"page": 0}'
                       class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing
                            <span class="font-medium">1</span>
                            to
                            <span class="font-medium">0</span>
                            of
                            <span class="font-medium">0 </span>
                            results
                        </p>
                    </div>
                    <div>
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <a href="#"
                               hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                               hx-target=".search_target"
                               hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                               hx-vals='{"page": 1}'
                               class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Previous</span>
                                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                    <path fill-rule="evenodd"
                                          d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </a>



                            <a href="#"
                               hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                               hx-target=".search_target"
                               hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                               hx-vals='{"page": 0}'
                               class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Next</span>
                                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                    <path fill-rule="evenodd"
                                          d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/pagination-strip.edge.php

            -->
        </td>
    </tr>
    </tfoot>
</table>

<!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table-structure.edge.php

-->

<!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-table.edge.php

-->


<!-- edge.class.php > phprender() 157: [edge] [2025-09-01 10:42:33] [edge.class.php:157] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php

-->
