
<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 102
array(53) {
  ["fs_app_root"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_system"]: string(81) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system"
  ["input_params"]: array(5) {
    ["table_name"]: string(18) "autodesk_customers"
    ["callback"]: string(32) "autodesk\generate_customer_table"
    ["data_source"]: string(0) ""
    ["target_column_id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    ["field_names"]: array(3) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
      [2]: string(19) "endcust_account_csn"
    }
  }
  ["system_views"]: array(6) {
    [0]: string(6) "system"
    [1]: string(5) "login"
    [2]: string(6) "logout"
    [3]: string(14) "reset-password"
    [4]: string(8) "settings"
    [5]: string(13) "database_dump"
  }
  ["request_uri"]: string(84) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
  ["domain"]: string(21) "www.cadservices.co.uk"
  ["script_name"]: string(42) "/baffletrain/autocadlt/autobooks/index.php"
  ["fs_doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["fs_app"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_resources"]: string(84) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources"
  ["fs_api"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api"
  ["fs_classes"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes"
  ["fs_functions"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions"
  ["fs_views"]: string(90) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views"
  ["fs_config"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/config"
  ["fs_templates"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/templates"
  ["fs_components"]: string(95) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/components"
  ["fs_logs"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/logs"
  ["fs_sys_api"]: string(85) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api"
  ["fs_sys_classes"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes"
  ["fs_sys_functions"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions"
  ["fs_sys_views"]: string(87) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views"
  ["fs_sys_config"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/config"
  ["fs_sys_templates"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates"
  ["fs_sys_components"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/components"
  ["fs_sys_logs"]: string(86) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/logs"
  ["fs_uploads"]: string(83) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/"
  ["fs_cache"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["fs_temp"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["app_root"]: string(32) "/baffletrain/autocadlt/autobooks"
  ["app_path"]: string(33) "api/data_table/column_preferences"
  ["path_parts"]: array(4) {
    [0]: string(3) "api"
    [1]: string(10) "data_table"
    [2]: string(18) "column_preferences"
    [3]: string(17) "move_field_simple"
  }
  ["top_level"]: string(3) "api"
  ["current_page"]: string(17) "move_field_simple"
  ["fs_app_path"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences"
  ["fs_full_path"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/api/data_table/column_preferences"
  ["full_path"]: string(65) "baffletrain/autocadlt/autobooks/api/data_table/column_preferences"
  ["fs_full_page"]: string(142) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/move_field_simple"
  ["full_page"]: string(83) "baffletrain/autocadlt/autobooks/api/data_table/column_preferences/move_field_simple"
  ["set_by"]: string(19) "HTTP_HX_CURRENT_URL"
  ["source_path"]: string(41) "/baffletrain/autocadlt/autobooks/autodesk"
  ["source_page"]: string(9) "customers"
  ["source_path_parts"]: array(4) {
    [0]: string(11) "baffletrain"
    [1]: string(9) "autocadlt"
    [2]: string(9) "autobooks"
    [3]: string(8) "autodesk"
  }
  ["source_app_path"]: string(8) "autodesk"
  ["hx_current_url"]: string(80) "https://www.cadservices.co.uk/baffletrain/autocadlt/autobooks/autodesk/customers"
  ["hx_current_url_parts"]: array(3) {
    ["scheme"]: string(5) "https"
    ["host"]: string(21) "www.cadservices.co.uk"
    ["path"]: string(51) "/baffletrain/autocadlt/autobooks/autodesk/customers"
  }
  ["source_app_path_parts"]: array(1) {
    [0]: string(8) "autodesk"
  }
  ["source_fs_path"]: string(99) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk"
  ["fs_sys_db_class"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php"
  ["route_tree"]: array(5) {
    ["dashboard"]: array(7) {
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(8) {
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
      ["sub_folder"]: array(5) {
        ["customers"]: array(7) {
          ["name"]: string(9) "Customers"
          ["icon"]: string(4) "user"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["orders"]: array(7) {
          ["name"]: string(6) "Orders"
          ["icon"]: string(13) "shopping-cart"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["quotes"]: array(7) {
          ["name"]: string(6) "Quotes"
          ["icon"]: string(13) "speech_bubble"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["products"]: array(7) {
          ["name"]: string(8) "Products"
          ["icon"]: string(16) "computer_desktop"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["subscriptions"]: array(7) {
          ["name"]: string(13) "Subscriptions"
          ["icon"]: string(6) "ticket"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
      }
    }
    ["system"]: array(8) {
      ["sub_folder"]: array(5) {
        ["logs"]: array(7) {
          ["name"]: string(4) "logs"
          ["icon"]: string(4) "code"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["data_sources"]: array(7) {
          ["name"]: string(12) "Data Sources"
          ["icon"]: string(12) "circle-stack"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(12) "data_sources"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["database_dump"]: array(7) {
          ["name"]: string(10) "DB Manager"
          ["icon"]: string(12) "circle-stack"
          ["required_roles"]: array(2) {
            [0]: string(5) "admin"
            [1]: string(3) "dev"
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["subscription_matching_rules"]: array(7) {
          ["name"]: string(12) "Rule Manager"
          ["icon"]: string(12) "puzzle-piece"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(true)
          ["is_system"]: bool(true)
        }
        ["users"]: array(7) {
          ["name"]: string(5) "users"
          ["icon"]: string(10) "user-group"
          ["required_roles"]: array(2) {
            [0]: string(5) "admin"
            [1]: string(3) "dev"
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(0) ""
          ["can_delete"]: bool(true)
          ["is_system"]: bool(true)
        }
      }
      ["name"]: string(6) "System"
      ["icon"]: string(3) "cog"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["email_campaigns"]: array(7) {
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["sketchup"]: array(7) {
      ["name"]: string(8) "Sketchup"
      ["icon"]: string(8) "bluebeam"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(false)
    }
  }
  ["route_list"]: array(15) {
    ["dashboard"]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(10) {
      ["id"]: int(11)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["customers"]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["logs"]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["orders"]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["data_sources"]: array(10) {
      ["id"]: int(13)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["quotes"]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["products"]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["subscriptions"]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["database_dump"]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(13) "database_dump"
      ["name"]: string(10) "DB Manager"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["subscription_matching_rules"]: array(10) {
      ["id"]: int(14)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(27) "subscription_matching_rules"
      ["name"]: string(12) "Rule Manager"
      ["icon"]: string(12) "puzzle-piece"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    ["email_campaigns"]: array(10) {
      ["id"]: int(12)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["users"]: array(10) {
      ["id"]: int(15)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(5) "users"
      ["name"]: string(5) "users"
      ["icon"]: string(10) "user-group"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    ["system"]: array(10) {
      ["id"]: int(1)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(3) "cog"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["sketchup"]: array(10) {
      ["id"]: int(34)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "sketchup"
      ["name"]: string(8) "Sketchup"
      ["icon"]: string(8) "bluebeam"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(false)
    }
  }
  ["routes"]: array(15) {
    [0]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [1]: array(10) {
      ["id"]: int(11)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [2]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [3]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [4]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [5]: array(10) {
      ["id"]: int(13)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [6]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [7]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [8]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [9]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(13) "database_dump"
      ["name"]: string(10) "DB Manager"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [10]: array(10) {
      ["id"]: int(14)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(27) "subscription_matching_rules"
      ["name"]: string(12) "Rule Manager"
      ["icon"]: string(12) "puzzle-piece"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    [11]: array(10) {
      ["id"]: int(12)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [12]: array(10) {
      ["id"]: int(15)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(5) "users"
      ["name"]: string(5) "users"
      ["icon"]: string(10) "user-group"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(true)
    }
    [13]: array(10) {
      ["id"]: int(1)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(3) "cog"
      ["required_roles"]: array(2) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [14]: array(10) {
      ["id"]: int(34)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "sketchup"
      ["name"]: string(8) "Sketchup"
      ["icon"]: string(8) "bluebeam"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(true)
      ["is_system"]: bool(false)
    }
  }
}

    ----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 103
array(5) {
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["auth_token"]: string(64) "0bce4eb548c3674522a26b60c9f54b6090894adbe9cd4921f581576dc8d1f726"
  ["debug_mode"]: bool(true)
  ["id_count"]: int(6)
}

    ----------------------------------------------------------------------------
-->

<!-- startup_sequence.class.php > start() 107: am I in debug mode?
-->

<!--
********************************************************************************************************************************************************
$row: startup_sequence.class.php > start() 111
array(1) {
  ["preferences"]: string(19) "{"debug_mode":true}"
}

    ----------------------------------------------------------------------------
-->

<!-- preferences: startup_sequence.class.php > start() 112: {"debug_mode":true}
-->

<!-- debug mode is: : startup_sequence.class.php > start() 113: on
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 121
array(5) {
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["auth_token"]: string(64) "0bce4eb548c3674522a26b60c9f54b6090894adbe9cd4921f581576dc8d1f726"
  ["debug_mode"]: bool(true)
  ["id_count"]: int(6)
}

    ----------------------------------------------------------------------------
-->

<!-- index.php > global() 29: starting route
-->

<!-- router.class.php > route() 18: starting route for api/data_table/column_preferences/move_field_simple
-->

<!-- router.class.php > get_api() 435: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api/data_table/column_preferences.api.php
-->

<!-- router.class.php > get_api() 435: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!-- router.class.php > get_api() 437: API file found: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!--
********************************************************************************************************************************************************
endpointy: router.class.php > route() 54
array(5) {
  ["parts"]: array(3) {
    [0]: string(3) "api"
    [1]: string(10) "data_table"
    [2]: string(18) "column_preferences"
  }
  ["endpoint"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["function_call"]: string(17) "move_field_simple"
  ["api_result"]: array(2) {
    ["status"]: string(7) "success"
    ["path"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  }
  ["api_result_status"]: string(7) "success"
}

    ----------------------------------------------------------------------------
-->

<!-- endpoint: router.class.php > route() 150: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for move_field_simple
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(106) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/move_field_simple.fn.php"
  ["functions_folder"]: string(119) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/move_field_simple.fn.php"
  ["functions_folder_lowercase"]: string(119) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/move_field_simple.fn.php"
  ["views_directory_current_page"]: string(149) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/move_field_simple.fn.php"
  ["sys_views_directory_current_page"]: string(146) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/move_field_simple.fn.php"
  ["views_file_current_page"]: string(115) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/move_field_simple.fn.php"
  ["system_functions"]: string(116) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/move_field_simple.fn.php"
  ["system_functions_lowercase"]: string(116) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/move_field_simple.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 176
         <strong>Arguments:</strong>
         0: "move_field_simple"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/move_field_simple.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.fn.php
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(114) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php.fn.php"
  ["functions_folder"]: string(127) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php.fn.php"
  ["functions_folder_lowercase"]: string(127) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php.fn.php"
  ["views_directory_current_page"]: string(157) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php.fn.php"
  ["sys_views_directory_current_page"]: string(154) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php.fn.php"
  ["views_file_current_page"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php.fn.php"
  ["system_functions"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php.fn.php"
  ["system_functions_lowercase"]: string(124) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 177
         <strong>Arguments:</strong>
         0: "column_preferences.fn.php"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for api
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api.fn.php"
  ["functions_folder"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php"
  ["functions_folder_lowercase"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php"
  ["views_directory_current_page"]: string(135) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/api.fn.php"
  ["sys_views_directory_current_page"]: string(132) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/api.fn.php"
  ["views_file_current_page"]: string(101) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api.fn.php"
  ["system_functions"]: string(102) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php"
  ["system_functions_lowercase"]: string(102) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/autoloader.php, Line: 276
         <strong>Arguments:</strong>
         0: "api"
      <strong>Function:</strong> autobooks_load_path_functions, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 180
         <strong>Arguments:</strong>
         0: ["api","data_table","column_preferences","move_field_simple"]
         1: "move_field_simple"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/api.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for data_table
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(99) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/data_table.fn.php"
  ["functions_folder"]: string(112) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php"
  ["functions_folder_lowercase"]: string(112) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php"
  ["views_directory_current_page"]: string(142) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/data_table.fn.php"
  ["sys_views_directory_current_page"]: string(139) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/data_table.fn.php"
  ["views_file_current_page"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/data_table.fn.php"
  ["system_functions"]: string(109) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/data_table.fn.php"
  ["system_functions_lowercase"]: string(109) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/data_table.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/autoloader.php, Line: 276
         <strong>Arguments:</strong>
         0: "data_table"
      <strong>Function:</strong> autobooks_load_path_functions, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 180
         <strong>Arguments:</strong>
         0: ["api","data_table","column_preferences","move_field_simple"]
         1: "move_field_simple"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/data_table.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 249: found function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/data_table.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 160: loading function files for column_preferences
-->

<!--
********************************************************************************************************************************************************
function paths to try: autoloader.php > autobooks_load_function_file() 211
array(8) {
  ["current_folder"]: string(107) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php"
  ["functions_folder"]: string(120) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php"
  ["functions_folder_lowercase"]: string(120) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php"
  ["views_directory_current_page"]: string(150) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php"
  ["sys_views_directory_current_page"]: string(147) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php"
  ["views_file_current_page"]: string(116) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php"
  ["system_functions"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php"
  ["system_functions_lowercase"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> autobooks_load_function_file, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/autoloader.php, Line: 276
         <strong>Arguments:</strong>
         0: "column_preferences"
      <strong>Function:</strong> autobooks_load_path_functions, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 180
         <strong>Arguments:</strong>
         0: ["api","data_table","column_preferences","move_field_simple"]
         1: "move_field_simple"
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/api/data_table/column_preferences/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- autoloader.php > autobooks_load_function_file() 247: Looking for function file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/column_preferences.fn.php
-->

<!-- launching layout-api with: router.class.php > route() 194: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php
-->
<!-- launching layout-api with /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php-->
<!-- router.class.php > route() 209: regular view found
-->

<!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_layout_layout-api.edge.php
-->



<!--
********************************************************************************************************************************************************
calling: sys_layout_layout-api.edge.php > include() 23
array(5) {
  ["namespace"]: string(23) "api\autodesk\customers\"
  ["endpoint"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["endpoint_exists"]: string(4) "true"
  ["function_call"]: string(17) "move_field_simple"
  ["path_parts"]: NULL
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->
;




<!--
********************************************************************************************************************************************************
calling: sys_layout_layout-api.edge.php > include() 57
array(5) {
  ["namespace"]: string(34) "api\data_table\column_preferences\"
  ["endpoint"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"
  ["endpoint_exists"]: string(4) "true"
  ["function_call"]: string(17) "move_field_simple"
  ["path_parts"]: array(2) {
    [0]: string(10) "data_table"
    [1]: string(18) "column_preferences"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->

<!-- calling in /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php: sys_layout_layout-api.edge.php > include() 65: api\data_table\column_preferences\move_field_simple
-->

<!--
********************************************************************************************************************************************************
modal_tabs_init: sys_layout_layout-api.edge.php > include() 88
array(4) {
  ["modal_tabs_file"]: string(110) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/modal_tabs.class.php"
  ["file_exists"]: bool(true)
  ["class_exists"]: bool(false)
  ["hx_target"]: string(0) ""
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_structure0: column_preferences.api.php > api\data_table\column_preferences\move_field_simple() 359
array(7) {
  [0]: array(6) {
    ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    ["label"]: string(7) "Company"
    ["fields"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
    ["field"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [1]: array(6) {
    ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
    ["label"]: string(8) "Customer"
    ["fields"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["field"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [2]: array(6) {
    ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
    ["label"]: string(3) "CSN"
    ["fields"]: array(1) {
      [0]: string(19) "endcust_account_csn"
    }
    ["field"]: string(19) "endcust_account_csn"
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [3]: array(6) {
    ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
    ["label"]: string(13) "Primary Admin"
    ["fields"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["field"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [4]: array(6) {
    ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
    ["label"]: string(8) "Location"
    ["fields"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["field"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [5]: array(6) {
    ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
    ["label"]: string(13) "Last Modified"
    ["fields"]: array(1) {
      [0]: string(21) "endcust_last_modified"
    }
    ["field"]: string(21) "endcust_last_modified"
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [6]: array(6) {
    ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
    ["label"]: string(7) "Actions"
    ["fields"]: array(1) {
      [0]: NULL
    }
    ["field"]: NULL
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\move_field_simple, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_layout_layout-api.edge.php, Line: 98
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_structure2: column_preferences.api.php > api\data_table\column_preferences\move_field_simple() 380
array(7) {
  [0]: array(6) {
    ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    ["label"]: string(7) "Company"
    ["fields"]: array(0) {
    }
    ["field"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [1]: array(6) {
    ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
    ["label"]: string(8) "Customer"
    ["fields"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["field"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [2]: array(6) {
    ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
    ["label"]: string(3) "CSN"
    ["fields"]: array(0) {
    }
    ["field"]: string(19) "endcust_account_csn"
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [3]: array(6) {
    ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
    ["label"]: string(13) "Primary Admin"
    ["fields"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["field"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [4]: array(6) {
    ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
    ["label"]: string(8) "Location"
    ["fields"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["field"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [5]: array(6) {
    ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
    ["label"]: string(13) "Last Modified"
    ["fields"]: array(1) {
      [0]: string(21) "endcust_last_modified"
    }
    ["field"]: string(21) "endcust_last_modified"
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [6]: &array(6) {
    ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
    ["label"]: string(7) "Actions"
    ["fields"]: array(1) {
      [0]: NULL
    }
    ["field"]: NULL
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\move_field_simple, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_layout_layout-api.edge.php, Line: 98
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_structure3: column_preferences.api.php > api\data_table\column_preferences\move_field_simple() 417
array(7) {
  [0]: &array(6) {
    ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
    ["label"]: string(7) "Company"
    ["fields"]: array(3) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
      [2]: string(19) "endcust_account_csn"
    }
    ["field"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [1]: array(6) {
    ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
    ["label"]: string(8) "Customer"
    ["fields"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["field"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [2]: array(6) {
    ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
    ["label"]: string(3) "CSN"
    ["fields"]: array(0) {
    }
    ["field"]: string(19) "endcust_account_csn"
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [3]: array(6) {
    ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
    ["label"]: string(13) "Primary Admin"
    ["fields"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["field"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [4]: array(6) {
    ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
    ["label"]: string(8) "Location"
    ["fields"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["field"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [5]: array(6) {
    ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
    ["label"]: string(13) "Last Modified"
    ["fields"]: array(1) {
      [0]: string(21) "endcust_last_modified"
    }
    ["field"]: string(21) "endcust_last_modified"
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
  [6]: array(6) {
    ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
    ["label"]: string(7) "Actions"
    ["fields"]: array(1) {
      [0]: NULL
    }
    ["field"]: NULL
    ["filter"]: bool(false)
    ["visible"]: bool(true)
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\move_field_simple, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_layout_layout-api.edge.php, Line: 98
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_layout_layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
api_process_criteria: data_table.class.php > api_process_criteria() 361
array(4) {
  ["table_name"]: string(18) "autodesk_customers"
  ["callback"]: string(32) "autodesk\generate_customer_table"
  ["data_source"]: string(0) ""
  ["field_names"]: array(3) {
    [0]: string(12) "endcust_name"
    [1]: string(13) "endcust_email"
    [2]: string(19) "endcust_account_csn"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api_process_criteria, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 308
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> reload_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 420
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...
      <strong>Function:</strong> api\data_table\column_preferences\move_field_simple, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_layout_layout-api.edge.php, Line: 98
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
database: autodesk_customers.class.php > get_all() 79
array(4) {
  ["order_by"]: string(21) "endcust_last_modified"
  ["limit"]: int(300)
  ["search_columns"]: array(17) {
    [0]: string(19) "endcust.account_csn"
    [1]: string(12) "endcust.name"
    [2]: string(16) "endcust.address1"
    [3]: string(16) "endcust.address2"
    [4]: string(16) "endcust.address3"
    [5]: string(12) "endcust.city"
    [6]: string(22) "endcust.state_province"
    [7]: string(19) "endcust.postal_code"
    [8]: string(15) "endcust.country"
    [9]: string(32) "endcust.primary_admin_first_name"
    [10]: string(31) "endcust.primary_admin_last_name"
    [11]: string(27) "endcust.primary_admin_email"
    [12]: string(15) "endcust.team_id"
    [13]: string(17) "endcust.team_name"
    [14]: string(18) "endcust.first_name"
    [15]: string(17) "endcust.last_name"
    [16]: string(13) "endcust.email"
  }
  ["just_table"]: bool(true)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 74
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 332
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> reload_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 420
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","...

----------------------------------------------------------------------------
-->

<!-- database.php > {closure}() 318: looking for endcust in query_tables
-->

<!-- database.php > {closure}() 321:   found
-->

<!--
********************************************************************************************************************************************************
$populate_column qt: database.php > {closure}() 323
array(1) {
  ["endcust"]: string(30) "FROM autodesk_accounts endcust"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> {closure}, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/database.php, Line: 402
         <strong>Arguments:</strong>
         0: "endcust_last_modified"
      <strong>Function:</strong> tcs_db_build_criteria, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 99
         <strong>Arguments:</strong>
         0: {"limit":300,"order_by":"endcust_last_modified","search_columns":["endcust.account_csn","endcust.nam...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- $where qt: database.php > tcs_db_build_criteria() 420: << empty string>>
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables_cols: database.php > tcs_db_build_tables() 456
array(1) {
  ["endcustlast_modified"]: string(46) "endcust.last_modified AS endcust_last_modified"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified"}
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 74
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables_tables: database.php > tcs_db_build_tables() 457
array(1) {
  ["endcust"]: string(30) "FROM autodesk_accounts endcust"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified"}
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 74
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables data: database.php > tcs_db_build_tables() 458
array(10) {
  [0]: string(12) "endcust_name"
  [1]: string(13) "endcust_email"
  [2]: string(18) "endcust_first_name"
  [3]: string(17) "endcust_last_name"
  [4]: string(19) "endcust_account_csn"
  [5]: string(32) "endcust_primary_admin_first_name"
  [6]: string(31) "endcust_primary_admin_last_name"
  [7]: string(12) "endcust_city"
  [8]: string(19) "endcust_postal_code"
  [9]: string(21) "endcust_last_modified"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified"}
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 74
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_email
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_first_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_last_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_account_csn
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_primary_admin_first_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_primary_admin_last_name
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_city
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_postal_code
-->

<!-- tcs_db_build_tables db: database.php > tcs_db_build_tables() 462: endcust_last_modified
-->

<!--
********************************************************************************************************************************************************
tcs_db_build_tables end: database.php > tcs_db_build_tables() 470
array(10) {
  ["endcustlast_modified"]: string(46) "endcust.last_modified AS endcust_last_modified"
  ["endcustname"]: string(28) "endcust.name AS endcust_name"
  ["endcustemail"]: string(30) "endcust.email AS endcust_email"
  ["endcustfirst_name"]: string(40) "endcust.first_name AS endcust_first_name"
  ["endcustlast_name"]: string(38) "endcust.last_name AS endcust_last_name"
  ["endcustaccount_csn"]: string(42) "endcust.account_csn AS endcust_account_csn"
  ["endcustprimary_admin_first_name"]: string(68) "endcust.primary_admin_first_name AS endcust_primary_admin_first_name"
  ["endcustprimary_admin_last_name"]: string(66) "endcust.primary_admin_last_name AS endcust_primary_admin_last_name"
  ["endcustcity"]: string(28) "endcust.city AS endcust_city"
  ["endcustpostal_code"]: string(42) "endcust.postal_code AS endcust_postal_code"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> tcs_db_build_tables, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 100
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"endcust":{"query":"FROM autodesk_accounts endcust"}}
         2: {"endcustlast_modified":"endcust.last_modified AS endcust_last_modified","endcustname":"endcust.name...
         3: {"endcust":"FROM autodesk_accounts endcust"}
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 74
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
finalq: autodesk_customers.class.php > database_get_all() 103
string(530) "SELECT endcust.last_modified AS endcust_last_modified, endcust.name AS endcust_name, endcust.email AS endcust_email, endcust.first_name AS endcust_first_name, endcust.last_name AS endcust_last_name, endcust.account_csn AS endcust_account_csn, endcust.primary_admin_first_name AS endcust_primary_admin_first_name, endcust.primary_admin_last_name AS endcust_primary_admin_last_name, endcust.city AS endcust_city, endcust.postal_code AS endcust_postal_code FROM autodesk_accounts endcust   ORDER BY endcust_last_modified    LIMIT 300"

    ----------------------------------------------------------------------------
      <strong>Function:</strong> database_get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_customers.class.php, Line: 80
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> get_all, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 74
         <strong>Arguments:</strong>
         0: ["endcust_name","endcust_email","endcust_first_name","endcust_last_name","endcust_account_csn","endc...
         1: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 332
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!-- database.php > tcs_db_query() 509: SELECT endcust.last_modified AS endcust_last_modified, endcust.name AS endcust_name, endcust.email AS endcust_email, endcust.first_name AS endcust_first_name, endcust.last_name AS endcust_last_name, endcust.account_csn AS endcust_account_csn, endcust.primary_admin_first_name AS endcust_primary_admin_first_name, endcust.primary_admin_last_name AS endcust_primary_admin_last_name, endcust.city AS endcust_city, endcust.postal_code AS endcust_postal_code FROM autodesk_accounts endcust   ORDER BY endcust_last_modified    LIMIT 300
-->

<!--
********************************************************************************************************************************************************
build column filters end: data_table.class.php > build_column_filters() 430
array(7) {
  [0]: array(2) {
    ["label"]: string(7) "Company"
    ["field"]: array(2) {
      [0]: string(12) "endcust_name"
      [1]: string(13) "endcust_email"
    }
  }
  [1]: array(2) {
    ["label"]: string(8) "Customer"
    ["field"]: array(2) {
      [0]: string(18) "endcust_first_name"
      [1]: string(17) "endcust_last_name"
    }
  }
  [2]: array(3) {
    ["label"]: string(3) "CSN"
    ["field"]: string(19) "endcust_account_csn"
    ["selected"]: string(3) "CSN"
  }
  [3]: array(2) {
    ["label"]: string(13) "Primary Admin"
    ["field"]: array(2) {
      [0]: string(32) "endcust_primary_admin_first_name"
      [1]: string(31) "endcust_primary_admin_last_name"
    }
  }
  [4]: array(2) {
    ["label"]: string(8) "Location"
    ["field"]: array(2) {
      [0]: string(12) "endcust_city"
      [1]: string(19) "endcust_postal_code"
    }
  }
  [5]: array(3) {
    ["label"]: string(13) "Last Modified"
    ["field"]: string(21) "endcust_last_modified"
    ["selected"]: string(13) "Last Modified"
  }
  [6]: array(3) {
    ["label"]: string(7) "Actions"
    ["content"]: object(Closure)#6 (1) {
      ["parameter"]: array(1) {
        ["$item"]: string(10) "<required>"
      }
    }
    ["selected"]: string(7) "Actions"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> build_column_filters, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 178
         <strong>Arguments:</strong>
      <strong>Function:</strong> process_data_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 81
         <strong>Arguments:</strong>
         0: {"table_id":"customers","db_table":"autodesk_accounts","columns":[{"label":"Company","field":["endcu...
         1: [{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust_email":"...
         2: ""
         3: "autodesk\\generate_customer_table"
         4: []
         5: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...
         6: []
         7: false
         8: false
         9: true
         10: false
         11: null
         12: "autodesk_customers"
         13: []
         14: true
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 332
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.nam...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
datad: data_table.class.php > process_data_table() 283
array(15) {
  ["title"]: string(13) "subscriptions"
  ["description"]: string(0) ""
  ["items"]: array(5) {
    [0]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(17) "Conrad Energy Ltd"
      ["endcust_email"]: string(30) "<EMAIL>"
      ["endcust_first_name"]: string(2) "IT"
      ["endcust_last_name"]: string(8) "Services"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(6) "Oxford"
      ["endcust_postal_code"]: string(7) "OX4 4GP"
    }
    [1]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(7) "POQ LTD"
      ["endcust_email"]: string(16) "<EMAIL>"
      ["endcust_first_name"]: string(6) "Sanjay"
      ["endcust_last_name"]: string(6) "Odedra"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(8) "Stanmore"
      ["endcust_postal_code"]: string(7) "HA7 3DA"
    }
    [2]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(20) "YOUR GOLF TRAVEL Ltd"
      ["endcust_email"]: string(33) "<EMAIL>"
      ["endcust_first_name"]: string(6) "Antony"
      ["endcust_last_name"]: string(7) "Puncher"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: string(6) "Antony"
      ["endcust_primary_admin_last_name"]: string(7) "Puncher"
      ["endcust_city"]: string(6) "London"
      ["endcust_postal_code"]: string(8) "EC1R 3AU"
    }
    [3]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(26) "STRATEGIC PM SOLUTIONS Ltd"
      ["endcust_email"]: string(28) "<EMAIL>"
      ["endcust_first_name"]: string(5) "Sarah"
      ["endcust_last_name"]: string(11) "Park-Murray"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: string(5) "Sarah"
      ["endcust_primary_admin_last_name"]: string(11) "Park-Murray"
      ["endcust_city"]: string(9) "Isleworth"
      ["endcust_postal_code"]: string(7) "TW7 5LF"
    }
    [4]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(20) "MS CAD Solutions Ltd"
      ["endcust_email"]: string(19) "<EMAIL>"
      ["endcust_first_name"]: string(4) "Mark"
      ["endcust_last_name"]: string(7) "SINNETT"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(8) "Barnsley"
      ["endcust_postal_code"]: string(7) "S72 9HR"
    }
  }
  ["columns"]: array(7) {
    [0]: array(9) {
      ["label"]: string(7) "Company"
      ["fields"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["field"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: NULL
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
    [1]: array(9) {
      ["label"]: string(8) "Customer"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: NULL
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
    [2]: array(9) {
      ["label"]: string(3) "CSN"
      ["fields"]: array(0) {
      }
      ["field"]: array(0) {
      }
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: object(Closure)#6 (1) {
        ["parameter"]: array(1) {
          ["$item"]: string(10) "<required>"
        }
      }
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
    [3]: array(9) {
      ["label"]: string(13) "Primary Admin"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: NULL
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
    [4]: array(9) {
      ["label"]: string(8) "Location"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: NULL
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
    [5]: array(9) {
      ["label"]: string(13) "Last Modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: NULL
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
    [6]: array(9) {
      ["label"]: string(7) "Actions"
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["field"]: NULL
      ["filter"]: bool(false)
      ["extra_parameters"]: string(0) ""
      ["replacements"]: NULL
      ["content"]: object(Closure)#6 (1) {
        ["parameter"]: array(1) {
          ["$item"]: string(10) "<required>"
        }
      }
      ["auto_filter"]: NULL
      ["filter_data"]: NULL
    }
  }
  ["available_fields"]: array(0) {
  }
  ["rows"]: array(2) {
    ["id"]: string(16) "subscription_id_"
    ["extra_parameters"]: string(0) ""
  }
  ["just_body"]: bool(false)
  ["just_rows"]: bool(false)
  ["just_table"]: bool(true)
  ["sort_column"]: array(2) {
    [0]: string(12) "endcust_name"
    [1]: string(13) "endcust_email"
  }
  ["sort_direction"]: string(3) "ASC"
  ["callback"]: string(32) "autodesk\generate_customer_table"
  ["table_name"]: string(18) "autodesk_customers"
  ["column_preferences"]: array(6) {
    ["hidden"]: array(0) {
    }
    ["structure"]: array(7) {
      [0]: array(6) {
        ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
        ["label"]: string(7) "Company"
        ["fields"]: array(3) {
          [0]: string(12) "endcust_name"
          [1]: string(13) "endcust_email"
          [2]: string(19) "endcust_account_csn"
        }
        ["field"]: array(2) {
          [0]: string(12) "endcust_name"
          [1]: string(13) "endcust_email"
        }
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
      [1]: array(6) {
        ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
        ["label"]: string(8) "Customer"
        ["fields"]: array(2) {
          [0]: string(18) "endcust_first_name"
          [1]: string(17) "endcust_last_name"
        }
        ["field"]: array(2) {
          [0]: string(18) "endcust_first_name"
          [1]: string(17) "endcust_last_name"
        }
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
      [2]: array(6) {
        ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
        ["label"]: string(3) "CSN"
        ["fields"]: array(0) {
        }
        ["field"]: string(19) "endcust_account_csn"
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
      [3]: array(6) {
        ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
        ["label"]: string(13) "Primary Admin"
        ["fields"]: array(2) {
          [0]: string(32) "endcust_primary_admin_first_name"
          [1]: string(31) "endcust_primary_admin_last_name"
        }
        ["field"]: array(2) {
          [0]: string(32) "endcust_primary_admin_first_name"
          [1]: string(31) "endcust_primary_admin_last_name"
        }
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
      [4]: array(6) {
        ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
        ["label"]: string(8) "Location"
        ["fields"]: array(2) {
          [0]: string(12) "endcust_city"
          [1]: string(19) "endcust_postal_code"
        }
        ["field"]: array(2) {
          [0]: string(12) "endcust_city"
          [1]: string(19) "endcust_postal_code"
        }
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
      [5]: array(6) {
        ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
        ["label"]: string(13) "Last Modified"
        ["fields"]: array(1) {
          [0]: string(21) "endcust_last_modified"
        }
        ["field"]: string(21) "endcust_last_modified"
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
      [6]: array(6) {
        ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
        ["label"]: string(7) "Actions"
        ["fields"]: array(1) {
          [0]: NULL
        }
        ["field"]: NULL
        ["filter"]: bool(false)
        ["visible"]: bool(true)
      }
    }
    ["columns"]: array(7) {
      [0]: array(2) {
        ["label"]: string(7) "Company"
        ["field"]: array(2) {
          [0]: string(12) "endcust_name"
          [1]: string(13) "endcust_email"
        }
      }
      [1]: array(2) {
        ["label"]: string(8) "Customer"
        ["field"]: array(2) {
          [0]: string(18) "endcust_first_name"
          [1]: string(17) "endcust_last_name"
        }
      }
      [2]: array(3) {
        ["label"]: string(3) "CSN"
        ["field"]: string(19) "endcust_account_csn"
        ["selected"]: string(3) "CSN"
      }
      [3]: array(2) {
        ["label"]: string(13) "Primary Admin"
        ["field"]: array(2) {
          [0]: string(32) "endcust_primary_admin_first_name"
          [1]: string(31) "endcust_primary_admin_last_name"
        }
      }
      [4]: array(2) {
        ["label"]: string(8) "Location"
        ["field"]: array(2) {
          [0]: string(12) "endcust_city"
          [1]: string(19) "endcust_postal_code"
        }
      }
      [5]: array(3) {
        ["label"]: string(13) "Last Modified"
        ["field"]: string(21) "endcust_last_modified"
        ["selected"]: string(13) "Last Modified"
      }
      [6]: array(3) {
        ["label"]: string(7) "Actions"
        ["content"]: array(0) {
        }
        ["selected"]: string(7) "Actions"
      }
    }
    ["data_source_type"]: string(9) "hardcoded"
    ["data_source_id"]: NULL
    ["created_at"]: string(19) "2025-09-04 22:33:14"
  }
  ["show_data_source_selector"]: string(4) "true"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> process_data_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/customers.fn.php, Line: 81
         <strong>Arguments:</strong>
         0: {"table_id":"customers","db_table":"autodesk_accounts","columns":[{"label":"Company","field":["endcust_name","endcust_email"]},{"label":"Customer","field":["endcust_first_name","endcust_last_name"]},{"label":"CSN","field":"endcust_account_csn"},{"label":"Primary Admin","field":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"]},{"label":"Location","field":["endcust_city","endcust_postal_code"]},{"label":"Last Modified","field":"endcust_last_modified"},{"label":"Actions","content":{}}]}
         1: [{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust_email":"<EMAIL>","endcust_first_name":"IT","endcust_last_name":"Services","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Oxford","endcust_postal_code":"OX4 4GP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"POQ LTD","endcust_email":"<EMAIL>","endcust_first_name":"Sanjay","endcust_last_name":"Odedra","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Stanmore","endcust_postal_code":"HA7 3DA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"YOUR GOLF TRAVEL Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Antony","endcust_last_name":"Puncher","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Antony","endcust_primary_admin_last_name":"Puncher","endcust_city":"London","endcust_postal_code":"EC1R 3AU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"STRATEGIC PM SOLUTIONS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Sarah","endcust_last_name":"Park-Murray","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Sarah","endcust_primary_admin_last_name":"Park-Murray","endcust_city":"Isleworth","endcust_postal_code":"TW7 5LF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"MS CAD Solutions Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"SINNETT","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Barnsley","endcust_postal_code":"S72 9HR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Proactive Learning Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Matt","endcust_last_name":"Dawson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"St. Neots","endcust_postal_code":"PE19 5ZA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Vida Design Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Joe","endcust_last_name":"Dobson","endcust_account_csn":null,"endcust_primary_admin_first_name":"Joe","endcust_primary_admin_last_name":"Dobson","endcust_city":"Bristol","endcust_postal_code":"BS1 6AA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Serena","endcust_last_name":"Federico","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Camland Developments Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Adam","endcust_last_name":"Owen","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Adam","endcust_primary_admin_last_name":"Owen","endcust_city":"Swanscombe","endcust_postal_code":"DA10 0DF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"TD SYNNEX UK Limited","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ADR Consulting","endcust_email":"<EMAIL>","endcust_first_name":"Alexis","endcust_last_name":"Rouzee","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alexis","endcust_primary_admin_last_name":"Rouzee","endcust_city":"Canterbury","endcust_postal_code":"CT4 6HD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Coleman Hicks Partnership","endcust_email":"<EMAIL>","endcust_first_name":"Sam","endcust_last_name":"Cook","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Kidlington","endcust_postal_code":"OX5 2DN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Rosie Wilkins Landscape Design","endcust_email":"<EMAIL>","endcust_first_name":"Rosie","endcust_last_name":"Wilkins","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Rosie","endcust_primary_admin_last_name":"Wilkins","endcust_city":"Sheffield","endcust_postal_code":"S5 7DD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"STEEL LINE Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Craig","endcust_last_name":"Stanley","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Sheffield","endcust_postal_code":"S13 9NR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Jonathan Carter Design","endcust_email":"<EMAIL>","endcust_first_name":"Jonathan","endcust_last_name":"Carter","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Jonathan","endcust_primary_admin_last_name":"Carter","endcust_city":"London","endcust_postal_code":"SE4 1YD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"MACLEAN ARCHITECTURE","endcust_email":"<EMAIL>","endcust_first_name":"James","endcust_last_name":"Maclean","endcust_account_csn":**********,"endcust_primary_admin_first_name":"James","endcust_primary_admin_last_name":"Maclean","endcust_city":"Knaresborough","endcust_postal_code":"HG5 0AD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Mark Cheetham","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Stockton-On-Tees","endcust_postal_code":"TS17 6AQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Clarice","endcust_last_name":"Elliot","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"TCS CAD & BIM Solutions Limited","endcust_email":"<EMAIL>","endcust_first_name":"Aurangzaib","endcust_last_name":"Mahmood","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Aurangzaib","endcust_primary_admin_last_name":"Mahmood","endcust_city":"Stockton On Tees","endcust_postal_code":"TS17 6BX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"COVENTRY CONSTRUCTION Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Roman","endcust_last_name":"Pundyk","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Coventry","endcust_postal_code":"CV4 9AP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"POQ Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Sanjay","endcust_last_name":"Odedra","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Sanjay","endcust_primary_admin_last_name":"Odedra","endcust_city":"Bushey","endcust_postal_code":"WD23 1NP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"WISBECH GRAMMAR SCHOOL","endcust_email":"<EMAIL>","endcust_first_name":"Andrew","endcust_last_name":"Dighton","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Peterborough","endcust_postal_code":"PE7 2AX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CPI Group","endcust_email":"<EMAIL>","endcust_first_name":"Wes","endcust_last_name":"Dennehy","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Wes","endcust_primary_admin_last_name":"Dennehy","endcust_city":"Chatham","endcust_postal_code":"ME5 8TD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"BLOCK ARCHITECTS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Bruce","endcust_last_name":"O'Brien","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Bellshill","endcust_postal_code":"ML4 3NP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"KELBROOK MILL Co.","endcust_email":"<EMAIL>","endcust_first_name":"Keith","endcust_last_name":"Hendry","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Thirsk","endcust_postal_code":"YO7 4AZ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Pace Property Reports & Projec","endcust_email":"<EMAIL>","endcust_first_name":"Phil","endcust_last_name":"Griffiths","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Wallasey","endcust_postal_code":"CH44 8EE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Kim","endcust_last_name":"West","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Susan","endcust_last_name":"Fisher","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Ian","endcust_last_name":"Kirkland","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Peter","endcust_last_name":"Crowther","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Matt","endcust_last_name":"Button","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Nick","endcust_last_name":"Smith","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Robert","endcust_last_name":"Selway","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Jonathan","endcust_last_name":"Chown","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Ashley","endcust_last_name":"Sutton","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"THOMAS","endcust_last_name":"Clark","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Antony","endcust_last_name":"Morvan","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Dean","endcust_last_name":"Whitbrook","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"RH IRVING INDUSTRIALS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Younger","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mark","endcust_primary_admin_last_name":"Younger","endcust_city":"Newcastle Upon Tyne","endcust_postal_code":"NE20 0RQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ONE PLANETS CONSULTANTS","endcust_email":"<EMAIL>","endcust_first_name":"Mike","endcust_last_name":"Freeman","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Carterton","endcust_postal_code":"OX18 3EZ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Workbox UK Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Ella","endcust_last_name":"Wood","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Ella","endcust_primary_admin_last_name":"Wood","endcust_city":"Chesterfield","endcust_postal_code":"S43 4UL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Glyn Grainger","endcust_email":"<EMAIL>","endcust_first_name":"Glyn","endcust_last_name":"Grainger","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Glyn","endcust_primary_admin_last_name":"Grainger","endcust_city":"Manchester","endcust_postal_code":"M30 9HG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SHEARSTONE MECHANICAL Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Tim","endcust_last_name":"Shearstone","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Tim","endcust_primary_admin_last_name":"Shearstone","endcust_city":"Rotherham","endcust_postal_code":"S65 3DX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CPM Group Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Flavell","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Wolverhampton","endcust_postal_code":"WV5 8AP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Sinorrah IT Solutions","endcust_email":"<EMAIL>","endcust_first_name":"Colin","endcust_last_name":"Harrison","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Princes Risborough","endcust_postal_code":"HP27 9DT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"BUILDING STRUCTURES ASSOCIATES","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Wadsworth","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Wadsworth","endcust_city":"East Grinstead","endcust_postal_code":"RH19 2BX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"EPS Site Solutions","endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Fleming","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Paul","endcust_primary_admin_last_name":"Fleming","endcust_city":"London","endcust_postal_code":"WC1X 9DB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Leada Engineering","endcust_email":"<EMAIL>","endcust_first_name":"Anna-Louise","endcust_last_name":"Lea","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Ripon","endcust_postal_code":"HG4 1JD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Andor Management Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Stuart","endcust_last_name":"Masters","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Stuart","endcust_primary_admin_last_name":"Masters","endcust_city":"Bradford","endcust_postal_code":"BD5 8HH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CONNECT SPACE","endcust_email":"<EMAIL>","endcust_first_name":"Barry","endcust_last_name":"Mullen","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Barry","endcust_primary_admin_last_name":"Mullen","endcust_city":"Newtownards","endcust_postal_code":"BT22 1AX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"HOLLOWAYS OF LUDLOW","endcust_email":"<EMAIL>","endcust_first_name":"Amar","endcust_last_name":"Brar","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Amar","endcust_primary_admin_last_name":"Brar","endcust_city":"London","endcust_postal_code":"W6 7LP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Workspace Design and Build","endcust_email":"<EMAIL>","endcust_first_name":"Lottie","endcust_last_name":"Porat","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Lottie","endcust_primary_admin_last_name":"Porat","endcust_city":"Manchester","endcust_postal_code":"M2 2BQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"A & M Architectural Partnership","endcust_email":"<EMAIL>","endcust_first_name":"Steve","endcust_last_name":"Morgan","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Steve","endcust_primary_admin_last_name":"Morgan","endcust_city":"Hastings","endcust_postal_code":"TN34 1DT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"C3D Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Ian","endcust_last_name":"Nock","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Cheltenham","endcust_postal_code":"GL54 3EU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mandy","endcust_last_name":"Oestreich","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Simon","endcust_last_name":"Hall","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ETCH ASSOCIATES","endcust_email":"<EMAIL>","endcust_first_name":"Etch","endcust_last_name":"Associates","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Etch","endcust_primary_admin_last_name":"Associates","endcust_city":"Witney","endcust_postal_code":"OX28 6HD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Audrey Knight","endcust_email":"<EMAIL>","endcust_first_name":"Audrey","endcust_last_name":"Knight","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Audrey","endcust_primary_admin_last_name":"Knight","endcust_city":"Witney","endcust_postal_code":"OX29 8FG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"GAN Architecture","endcust_email":"<EMAIL>","endcust_first_name":"Caroline","endcust_last_name":"Johnson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Caroline","endcust_primary_admin_last_name":"Johnson","endcust_city":"Newbury","endcust_postal_code":"RG14 7QT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Aves Architectural","endcust_email":"<EMAIL>","endcust_first_name":"MARTIN","endcust_last_name":"AVES","endcust_account_csn":**********,"endcust_primary_admin_first_name":"MARTIN","endcust_primary_admin_last_name":"AVES","endcust_city":"Alton","endcust_postal_code":"GU34 2RN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ALIGN ARCHITECTURE Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Nick","endcust_last_name":"Clewer","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Nick","endcust_primary_admin_last_name":"Clewer","endcust_city":"Birmingham","endcust_postal_code":"B3 1NQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"RJ STEARN Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Jan","endcust_last_name":"Stearn","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Jan","endcust_primary_admin_last_name":"Stearn","endcust_city":"Milton Keynes","endcust_postal_code":"MK8 0AB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JPP Consulting Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Nigel","endcust_last_name":"Smith","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Daventry","endcust_postal_code":"NN11 2NH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Theo Green Designs Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Nepheli","endcust_last_name":"Theophanidis","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Nepheli","endcust_primary_admin_last_name":"Theophanidis","endcust_city":"Dorchester","endcust_postal_code":"DT2 9BE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"DCB BUILDING SERVICES","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Baxter","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Baxter","endcust_city":"Ipswich","endcust_postal_code":"IP3 9DD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"OCE Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Peter","endcust_last_name":"Nicholls","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Cannock","endcust_postal_code":"WS11 0EL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"DSC Fabrication Ltd","endcust_email":"<EMAIL>","endcust_first_name":"tom","endcust_last_name":"courtenage","endcust_account_csn":**********,"endcust_primary_admin_first_name":"tom","endcust_primary_admin_last_name":"courtenage","endcust_city":"Westbury","endcust_postal_code":"BA13 4JP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Baxter","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Gilliard","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mike","endcust_last_name":"Jones","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Liam","endcust_last_name":"Mullins","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Barry","endcust_last_name":"Williams","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Adam","endcust_last_name":"Strudwick","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Richard","endcust_last_name":"Greaves","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Gareth","endcust_last_name":"Fowler","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Moira","endcust_last_name":"Mcintyre","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Cockrill","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Alex","endcust_last_name":"Rosen","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Right Heat Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Theakston","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mark","endcust_primary_admin_last_name":"Theakston","endcust_city":"Washington","endcust_postal_code":"NE38 8QP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Scott","endcust_last_name":"Baldam","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ENVIRONMENTAL MANAGEMENT CONSU","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Grantham","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Lincoln","endcust_postal_code":"LN2 1BD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Sims Hilditch Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Claire","endcust_last_name":"Eastwood","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Claire","endcust_primary_admin_last_name":"Eastwood","endcust_city":"Chippenham","endcust_postal_code":"SN14 8JR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"DH Lowe Ltd","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Lowe","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Lowe","endcust_city":"Diss","endcust_postal_code":"IP22 1RY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"LINROC Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Scott","endcust_last_name":"Wilson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Scott","endcust_primary_admin_last_name":"Wilson","endcust_city":"Swindon","endcust_postal_code":"SN5 7EX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"STORRAX SHOPFITTERS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Barry","endcust_last_name":"Williams","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Barry","endcust_primary_admin_last_name":"Williams","endcust_city":"Melton Mowbray","endcust_postal_code":"LE13 1NU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Surrey Tech Services Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Dan","endcust_last_name":"Parsons","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dan","endcust_primary_admin_last_name":"Parsons","endcust_city":"Sunbury On Thames","endcust_postal_code":"TW16 6AS"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ABSOLUTE SOLAR AND WIND","endcust_email":"<EMAIL>","endcust_first_name":"Nigel","endcust_last_name":"Benson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Alexandria","endcust_postal_code":"G83 8ND"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"HPS CHARTERED SURVEYORS","endcust_email":"<EMAIL>","endcust_first_name":"IT","endcust_last_name":"Department","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Houghton Le Spring","endcust_postal_code":"DH4 4UG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CHASE EQUIPMENT Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Alex","endcust_last_name":"Thomson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Bilston","endcust_postal_code":"WV14 9EE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Theakston","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"M Green Architect","endcust_email":"<EMAIL>","endcust_first_name":"Malcolm","endcust_last_name":"Green","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Malcolm","endcust_primary_admin_last_name":"Green","endcust_city":"Windermere","endcust_postal_code":"LA23 3HR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"TSB FABRICATION Ltd","endcust_email":"<EMAIL>","endcust_first_name":"William","endcust_last_name":"Grey","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Shildon","endcust_postal_code":"DL4 1HF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CONCRETE DESIGN AND DETAILING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Chris","endcust_last_name":"Younger","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Chris","endcust_primary_admin_last_name":"Younger","endcust_city":"Barnsley","endcust_postal_code":"S73 0HB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ROSEBURY Ltd","endcust_email":"<EMAIL>","endcust_first_name":"THOMAS","endcust_last_name":"POSTLETHWAITE","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Baldock","endcust_postal_code":"SG7 6LR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Sarah Darlow Darlow","endcust_email":"<EMAIL>","endcust_first_name":"Sarah","endcust_last_name":"Darlow","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Sarah","endcust_primary_admin_last_name":"Darlow","endcust_city":"Spalding","endcust_postal_code":"PE12 7EE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Anderson","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"STREET DESIGN PARTNERSHIP","endcust_email":"<EMAIL>","endcust_first_name":"John","endcust_last_name":"Starkey","endcust_account_csn":**********,"endcust_primary_admin_first_name":"John","endcust_primary_admin_last_name":"Starkey","endcust_city":"Salford","endcust_postal_code":"M3 7BB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"BATTERSEA ARTS CENTRE","endcust_email":"<EMAIL>","endcust_first_name":"Daniel","endcust_last_name":"Palmer","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"London","endcust_postal_code":"SW11 5TN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"PAUL SILVESTER DESIGNS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Silvester","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Paul","endcust_primary_admin_last_name":"Silvester","endcust_city":"Sutton Coldfield","endcust_postal_code":"B73 6LD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CLA FABRICATIONS","endcust_email":"<EMAIL>","endcust_first_name":"Cameron","endcust_last_name":"Worthington","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Williams","endcust_city":"Wednesbury","endcust_postal_code":"WS10 7NB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"PARSLOE CONSULTING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Chris","endcust_last_name":"Parsloe","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Chris","endcust_primary_admin_last_name":"Parsloe","endcust_city":"Totland Bay","endcust_postal_code":"PO39 0BX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"IMAGE CEILINGS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Nigel","endcust_last_name":"Rawlins","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Sutton","endcust_postal_code":"SM1 4PL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Autorain Irrigation Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Johan","endcust_last_name":"Cornelius","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Johan","endcust_primary_admin_last_name":"Cornelius","endcust_city":"Jersey","endcust_postal_code":"JE3 6BB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"LAR Housing Trust","endcust_email":"<EMAIL>","endcust_first_name":"Philip","endcust_last_name":"Walker","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Dunfermline","endcust_postal_code":"KY11 8UU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"NEXUS (SOUTH EAST) Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Brian","endcust_last_name":"Fuller","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Brian","endcust_primary_admin_last_name":"Fuller","endcust_city":"Hastings","endcust_postal_code":"TN35 4FB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"OAKDALE PROPERTY","endcust_email":"<EMAIL>","endcust_first_name":"Carl","endcust_last_name":"Rodgers","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Frodsham","endcust_postal_code":"WA6 6PY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SPEC Engineering Ltd","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Leatherhead","endcust_postal_code":"KT22 7LP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Skye Consultnts Ltd","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Glasgow","endcust_postal_code":"G61 3AH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"RED7 INSHORE DIVING","endcust_email":"<EMAIL>","endcust_first_name":"Debbie","endcust_last_name":"Jones","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Debbie","endcust_primary_admin_last_name":"Jones","endcust_city":"Dereham","endcust_postal_code":"NR20 3DU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Ionatan","endcust_last_name":"Lavric","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ICAX Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Edward","endcust_last_name":"Thompson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Edward","endcust_primary_admin_last_name":"Thompson","endcust_city":"London","endcust_postal_code":"SW15 1BN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"THE BUSH CONSULTANCY Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Robert","endcust_last_name":"Davis","endcust_account_csn":**********,"endcust_primary_admin_first_name":"IT","endcust_primary_admin_last_name":"Team","endcust_city":"Bristol","endcust_postal_code":"BS4 3EH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Ground Condition Consultants L","endcust_email":"<EMAIL>","endcust_first_name":"Antony","endcust_last_name":"Platt","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Antony","endcust_primary_admin_last_name":"Platt","endcust_city":"Southampton","endcust_postal_code":"SO19 9RY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Aubrey Technical Services","endcust_email":"<EMAIL>","endcust_first_name":"Raymond","endcust_last_name":"Crosby","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Raymond","endcust_primary_admin_last_name":"Crosby","endcust_city":"Hemel Hempstead","endcust_postal_code":"HP3 9PP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Phoenix Fire Protection (Servi","endcust_email":"<EMAIL>","endcust_first_name":"Christopher","endcust_last_name":"Morgan","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Christopher","endcust_primary_admin_last_name":"Morgan","endcust_city":"Kingswinford","endcust_postal_code":"DY6 8AL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"KOHA ARCHITECTS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Antony","endcust_last_name":"Morvan","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Antony","endcust_primary_admin_last_name":"Morvan","endcust_city":"Penryn","endcust_postal_code":"TR10 9TA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Halsall Michael","endcust_email":"<EMAIL>","endcust_first_name":"Michael","endcust_last_name":"Halsall","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Stoke-On-Trent","endcust_postal_code":"ST10 2EF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"RYGAN ENERGY SOLUTIONS","endcust_email":"<EMAIL>","endcust_first_name":"Jack","endcust_last_name":"Hormell","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Worksop","endcust_postal_code":"S80 4LX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CPN Systems Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Carl","endcust_last_name":"Nash","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Wolverhampton","endcust_postal_code":"WV10 9TG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"NEIL","endcust_last_name":"AYRES","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Bond","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Hudson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"SKYE","endcust_last_name":"Consultants","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Michael","endcust_last_name":"Duffy","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Peter","endcust_last_name":"Goldthorpe","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Debbie","endcust_last_name":"Jones","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Iain","endcust_last_name":"Dunn","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Howard","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"RICHARD","endcust_last_name":"DONKIN","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"FORMAN METAL PRODUCTS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Peter","endcust_last_name":"Overton","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Peter","endcust_primary_admin_last_name":"Overton","endcust_city":"Stockton-On-Tees","endcust_postal_code":"TS18 2PH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Lancashire CCC","endcust_email":"<EMAIL>","endcust_first_name":"Adrian","endcust_last_name":"Jones","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Manchester","endcust_postal_code":"M16 0PX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Beamish Museum Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Chris","endcust_last_name":"Armstrong","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Stuart","endcust_primary_admin_last_name":"Shaw","endcust_city":"Stanley","endcust_postal_code":"DH9 0RG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"PLASTIC ENERGY Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Andy","endcust_last_name":"Yabrudy","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"London","endcust_postal_code":"EC4V 5DY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Ashley Group","endcust_email":"<EMAIL>","endcust_first_name":"Darren","endcust_last_name":"Drew","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Darren","endcust_primary_admin_last_name":"Drew","endcust_city":"Sutton","endcust_postal_code":"SM3 9QP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Alex Grey","endcust_email":"<EMAIL>","endcust_first_name":"Alex","endcust_last_name":"Grey","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alex","endcust_primary_admin_last_name":"Grey","endcust_city":"Sutton Coldfield","endcust_postal_code":"B73 6LB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CJ MILLING SERVICES","endcust_email":"<EMAIL>","endcust_first_name":"Christopher","endcust_last_name":"Hammond","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Christopher","endcust_primary_admin_last_name":"Hammond","endcust_city":"Witham","endcust_postal_code":"CM8 1UJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Surrey Tech Services Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Dan","endcust_last_name":"Parsons","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dan","endcust_primary_admin_last_name":"Parsons","endcust_city":"Sunbury-On-Thames","endcust_postal_code":"TW16 6AS"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CEADA ENVIRONMENTAL","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Cadwallader","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Lechlade","endcust_postal_code":"GL7 3FS"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ACESCOTT Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Tim","endcust_last_name":"Kemp","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Castleford","endcust_postal_code":"WF10 5HW"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"2B Engineering Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Matthew","endcust_last_name":"Baxter","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Matthew","endcust_primary_admin_last_name":"Baxter","endcust_city":"Omagh","endcust_postal_code":"BT79 7TZ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Energy Compliance Consultancy Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Marcin","endcust_last_name":"Gorecki","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Marcin","endcust_primary_admin_last_name":"Gorecki","endcust_city":"Milton Keynes","endcust_postal_code":"MK4 3FG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Barker Associates (Essex) Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Richard","endcust_last_name":"Paynter","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Braintree","endcust_postal_code":"CM77 7AA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CIKU CONSTRUCTION SERVICES Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Marius","endcust_last_name":"Todorovici","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Surbiton","endcust_postal_code":"KT6 5PR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SUBSCANTECHNOLOGY","endcust_email":"<EMAIL>","endcust_first_name":"James","endcust_last_name":"Wheeler","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Rugby","endcust_postal_code":"CV22 7DB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Dan","endcust_last_name":"Parsons","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"KELLY WELDING SERVICES Ltd","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Wetherby","endcust_postal_code":"LS23 7FL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Hinton Properties","endcust_email":"<EMAIL>","endcust_first_name":"Richard","endcust_last_name":"Di Duca","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Bromsgrove","endcust_postal_code":"B60 4JE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"McAuliffe Group","endcust_email":"<EMAIL>","endcust_first_name":"Jim","endcust_last_name":"Yearley","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Jim","endcust_primary_admin_last_name":"Yearley","endcust_city":"Bilston","endcust_postal_code":"WV14 0TP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Grayson Shopfitters Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Stuart","endcust_last_name":"Grayson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Stuart","endcust_primary_admin_last_name":"Grayson","endcust_city":"Derby","endcust_postal_code":"DE3 9NN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CONTOUR ARCHITECTURE Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Philip","endcust_last_name":"Bailey","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Berkhamsted","endcust_postal_code":"HP4 2DF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"OS Design Planning Services Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Owen","endcust_last_name":"Sandle","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Owen","endcust_primary_admin_last_name":"Sandle","endcust_city":"Burton-On-Trent","endcust_postal_code":"DE14 2FS"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CARMAC BUILDING & CIVIL ENGINEERING","endcust_email":"<EMAIL>","endcust_first_name":"tom","endcust_last_name":"mcgowan","endcust_account_csn":**********,"endcust_primary_admin_first_name":"tom","endcust_primary_admin_last_name":"mcgowan","endcust_city":"Wellingborough","endcust_postal_code":"NN9 5HX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"NR SES Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Survey","endcust_last_name":"wiz","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Survey","endcust_primary_admin_last_name":"wiz","endcust_city":"Hayle","endcust_postal_code":"TR27 4BP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CONCREATE SKATEPARKS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Robert","endcust_last_name":"Young","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Falkirk","endcust_postal_code":"FK1 5HU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ADVANCED FIRE AND SECURITY SERVICES","endcust_email":"<EMAIL>","endcust_first_name":"james","endcust_last_name":"bourner","endcust_account_csn":**********,"endcust_primary_admin_first_name":"james","endcust_primary_admin_last_name":"bourner","endcust_city":"Dartford","endcust_postal_code":"DA1 4AL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"GWSR PLC","endcust_email":"<EMAIL>","endcust_first_name":"Gordon","endcust_last_name":"Wood","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Cheltenham","endcust_postal_code":"GL54 5DT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Adrian","endcust_last_name":"Brown","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"damien","endcust_last_name":"gommersall","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"K BAVA ARCHITECTS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Kamlesh","endcust_last_name":"Bava","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Kamlesh","endcust_primary_admin_last_name":"Bava","endcust_city":"London","endcust_postal_code":"N4 1HT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"De Saram Chandima","endcust_email":"<EMAIL>","endcust_first_name":"Chandima","endcust_last_name":"De saram","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Sunbury-On-Thames","endcust_postal_code":"TW16 6JH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"L & Architects","endcust_email":"<EMAIL>","endcust_first_name":"lindsey","endcust_last_name":"webster","endcust_account_csn":**********,"endcust_primary_admin_first_name":"lindsey","endcust_primary_admin_last_name":"webster","endcust_city":"London","endcust_postal_code":"SW14 8EQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JAMESDODDRELL:ARCHITECT Ltd","endcust_email":"<EMAIL>","endcust_first_name":"James","endcust_last_name":"Doddrell","endcust_account_csn":**********,"endcust_primary_admin_first_name":"James","endcust_primary_admin_last_name":"Doddrell","endcust_city":"Leatherhead","endcust_postal_code":"KT23 4LG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Dean Fletcher","endcust_email":"<EMAIL>","endcust_first_name":"Dean","endcust_last_name":"Fletcher","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dean","endcust_primary_admin_last_name":"Fletcher","endcust_city":"Bideford","endcust_postal_code":"EX39 3NJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Isomass Ltd","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Bignell","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Bignell","endcust_city":"Cambridge","endcust_postal_code":"CB23 3GY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"WIRED DESIGN Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Rupert","endcust_last_name":"Harwood","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Newton Abbot","endcust_postal_code":"TQ12 1QB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Steve","endcust_last_name":"Atkinson","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Collins","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Sharon","endcust_last_name":"Lewis","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Julian","endcust_last_name":"Wilson","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Andy","endcust_last_name":"Taylor","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"John","endcust_last_name":"Tempest","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Peeks","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Leeuwerke Leeuwerke","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Harry","endcust_last_name":"Hale","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Martyn","endcust_last_name":"Lowther","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Tony","endcust_last_name":"Stuchberry","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Soho House UK Ltd","endcust_email":"<EMAIL>","endcust_first_name":"MICRONET","endcust_last_name":"SHG","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alex","endcust_primary_admin_last_name":"Reid","endcust_city":"LONDON","endcust_postal_code":"WC2R 1EA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ANDOOI DESIGN","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Anderson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Anderson","endcust_city":"Pewsey","endcust_postal_code":"SN9 5BL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"OPUS ENGINEERING CONSULTANTS L","endcust_email":"<EMAIL>","endcust_first_name":"Nick","endcust_last_name":"Kitchen","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Newton Abbot","endcust_postal_code":"TQ12 5QF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Michael Burrowes Associates Ltd","endcust_email":"<EMAIL>","endcust_first_name":"John","endcust_last_name":"Burrowes","endcust_account_csn":**********,"endcust_primary_admin_first_name":"John","endcust_primary_admin_last_name":"Burrowes","endcust_city":"London","endcust_postal_code":"W14 0JD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"VELOSO ALEX","endcust_email":"<EMAIL>","endcust_first_name":"Alex","endcust_last_name":"Veloso","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Eastbourne","endcust_postal_code":"BN21 3ZE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Karen","endcust_last_name":"Horrocks","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"VKE Contractors","endcust_email":"<EMAIL>","endcust_first_name":"Oliver","endcust_last_name":"Fas","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Boston","endcust_primary_admin_last_name":"Hajdari","endcust_city":"London","endcust_postal_code":"E8 3DL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Stone Bathwear UK","endcust_email":"<EMAIL>","endcust_first_name":"Stefano","endcust_last_name":"Bell\u00e8","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dan","endcust_primary_admin_last_name":"Ucci","endcust_city":"London","endcust_postal_code":"EC3M 1EU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Griffin Alan","endcust_email":"<EMAIL>","endcust_first_name":"Alan","endcust_last_name":"Griffin","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Stourport On Severn","endcust_postal_code":"DY13 8TF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Cherish Homes Ltd","endcust_email":"<EMAIL>","endcust_first_name":"stuart","endcust_last_name":"law","endcust_account_csn":**********,"endcust_primary_admin_first_name":"stuart","endcust_primary_admin_last_name":"law","endcust_city":"Manchester","endcust_postal_code":"M22 5LW"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"No 30 Design Studio","endcust_email":"<EMAIL>","endcust_first_name":"Sara","endcust_last_name":"Edwards","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Sara","endcust_primary_admin_last_name":"Edwards","endcust_city":"Hereford","endcust_postal_code":"HR1 3ED"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"PETER YOUNG DESIGN","endcust_email":"<EMAIL>","endcust_first_name":"patrick","endcust_last_name":"hendrick","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"London","endcust_postal_code":"EC1V 7JL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"TR Harris Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Tim","endcust_last_name":"Harris","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Pinner","endcust_postal_code":"HA5 5HP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ANTHAM ELECTRICAL Ltd","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Hamell","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Bracknell","endcust_postal_code":"RG12 8YT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"DAHL SVERIGE AB","endcust_email":"<EMAIL>","endcust_first_name":"Conny","endcust_last_name":"Abrahamsson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Gulliverv\u00e3\u00a4gen","endcust_postal_code":"J\u00c3RF\u00c3 176"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Duncan","endcust_last_name":"Noble","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JON SHEAFF & ASSOCIATES","endcust_email":"<EMAIL>","endcust_first_name":"Emma","endcust_last_name":"Kirk","endcust_account_csn":null,"endcust_primary_admin_first_name":"Gemma","endcust_primary_admin_last_name":"Woodfall","endcust_city":"London","endcust_postal_code":"E8 3SE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Barden Chapman","endcust_email":"<EMAIL>","endcust_first_name":"Bianca","endcust_last_name":"Blackbeard","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Bianca","endcust_primary_admin_last_name":"Blackbeard","endcust_city":"London","endcust_postal_code":"EC3R 7AG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"IDM Properties","endcust_email":"<EMAIL>","endcust_first_name":"Bal","endcust_last_name":"Heer","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"London","endcust_postal_code":"N1 5EA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"MARK CARPENTER","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Carpenter","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mark","endcust_primary_admin_last_name":"Carpenter","endcust_city":"RIPLEY","endcust_postal_code":"GU23 7AW"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Malcolm Scott Consultants Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Alexandra","endcust_last_name":"Gines Heiwegen","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alexandra","endcust_primary_admin_last_name":"Gines Heiwegen","endcust_city":"Worcester","endcust_postal_code":"WR1 1HA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Gavin McEvoy","endcust_email":"<EMAIL>","endcust_first_name":"Gavin","endcust_last_name":"Mcevoy","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Gavin","endcust_primary_admin_last_name":"Mcevoy","endcust_city":"Troon","endcust_postal_code":"KA10 7LS"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CASTLE REFRIGERATION","endcust_email":"<EMAIL>","endcust_first_name":"Dean","endcust_last_name":"Tilley","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dean","endcust_primary_admin_last_name":"Tilley","endcust_city":"Sunbury-on-thames","endcust_postal_code":"TW16 5LB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Best Little Projects","endcust_email":"<EMAIL>","endcust_first_name":"THOMAS","endcust_last_name":"Clark","endcust_account_csn":**********,"endcust_primary_admin_first_name":"THOMAS","endcust_primary_admin_last_name":"Clark","endcust_city":"Stamford","endcust_postal_code":"PE9 3SZ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Keir Townsend Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Alastair","endcust_last_name":"Keir","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alastair","endcust_primary_admin_last_name":"Keir","endcust_city":"London","endcust_postal_code":"W1J 5AP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Stour Structural Partnership","endcust_email":"<EMAIL>","endcust_first_name":"chris","endcust_last_name":"vivian","endcust_account_csn":**********,"endcust_primary_admin_first_name":"chris","endcust_primary_admin_last_name":"vivian","endcust_city":"Wimborne","endcust_postal_code":"BH21 2PG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"WESTBEACH","endcust_email":"<EMAIL>","endcust_first_name":"Andy","endcust_last_name":"Clarke","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Whitstable","endcust_postal_code":"CT5 2EH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"FIRETEC SYSTEMS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"FSL","endcust_last_name":"(Firetec Systems LTD)","endcust_account_csn":**********,"endcust_primary_admin_first_name":"FSL","endcust_primary_admin_last_name":"(Firetec Systems LTD)","endcust_city":"Wokingham","endcust_postal_code":"RG41 2QZ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"BHK UK Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Chris","endcust_last_name":"Longley","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Peterlee","endcust_postal_code":"SR8 2JF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Mark Cheetham","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Stockton-On-Tees","endcust_postal_code":"TS20 2QQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Richard","endcust_last_name":"Swindell","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Boyce Business Equipment Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Dan","endcust_last_name":"Richardson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dan","endcust_primary_admin_last_name":"Richardson","endcust_city":"High Wycombe","endcust_postal_code":"HP12 4BY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"AIRTHERM ENGINEERING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Andrew","endcust_last_name":"Brown","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Stourbridge","endcust_postal_code":"DY9 7ND"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"AIR HANDLING SYSTEMS","endcust_email":"<EMAIL>","endcust_first_name":"Jonathan","endcust_last_name":"Darby","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Jonathan","endcust_primary_admin_last_name":"Darby","endcust_city":"Shildon","endcust_postal_code":"DL4 1QB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Igloo Design and Consultancy Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Hinson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Paul","endcust_primary_admin_last_name":"Hinson","endcust_city":"Nantwich","endcust_postal_code":"CW5 6JY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Max Rollitt Bespoke Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Max","endcust_last_name":"Rollitt","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Max","endcust_primary_admin_last_name":"Rollitt","endcust_city":"Winchester","endcust_postal_code":"SO21 1DA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"79 LIGHTING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Owen","endcust_last_name":"Claridge","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Cheltenham","endcust_postal_code":"GL51 6TQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Food Projects Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Ian","endcust_last_name":"Spencer","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Winchester","endcust_postal_code":"SO21 2BX"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Sara","endcust_last_name":"Owen","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"graham","endcust_last_name":"taylor","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"John","endcust_last_name":"Sherrington","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Stuart","endcust_last_name":"Wall Wall","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Monika","endcust_last_name":"Lendvai","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Sam","endcust_last_name":"Steel","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Heena","endcust_last_name":"Dalal","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Helen","endcust_last_name":"Eaves","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Brook","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Dan","endcust_last_name":"Cramond","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Rob","endcust_last_name":"Andrews","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Cheetham","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JB Associates","endcust_email":"<EMAIL>","endcust_first_name":"Dave","endcust_last_name":"Prince","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Brockenhurst","endcust_postal_code":"SO42 7SB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CTS DESIGN Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Alina","endcust_last_name":"Chalubowicz","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alina","endcust_primary_admin_last_name":"Chalubowicz","endcust_city":"Birmingham","endcust_postal_code":"B32 4JU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"AD BLY CONSTRUCTION Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Sharon","endcust_last_name":"Lawrence","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Alexander","endcust_primary_admin_last_name":"Kannemeyer","endcust_city":"Knebworth","endcust_postal_code":"SG3 6QJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Amari Technologies Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Charlotte","endcust_last_name":"Fonfara","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Charlotte","endcust_primary_admin_last_name":"Fonfara","endcust_city":"Brighton","endcust_postal_code":"BN1 8JG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Micam Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Pat","endcust_last_name":"Dunlea","endcust_account_csn":**********,"endcust_primary_admin_first_name":"William","endcust_primary_admin_last_name":"Buckley","endcust_city":"Mallow","endcust_postal_code":"P51 Yd51"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"M & E Design Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mike","endcust_last_name":"O'Boyle","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mike","endcust_primary_admin_last_name":"O'Boyle","endcust_city":"Stourbridge","endcust_postal_code":"DY8 1LU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Russell Alan","endcust_email":"<EMAIL>","endcust_first_name":"Alan","endcust_last_name":"Russell","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Guildford","endcust_postal_code":"GU2 4DQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"AIRSUN SYSTEMS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Scott","endcust_last_name":"Johnson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Tewkesbury","endcust_postal_code":"GL20 8SD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"OUNDLE ENGINEERS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Simon","endcust_last_name":"Felton","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Simon","endcust_primary_admin_last_name":"Felton","endcust_city":"Peterborough","endcust_postal_code":"PE8 4LF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ark architecture + design","endcust_email":"<EMAIL>","endcust_first_name":"Khairena","endcust_last_name":"Sean-Walker","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Khairena","endcust_primary_admin_last_name":"Sean-Walker","endcust_city":"Glasgow","endcust_postal_code":"G3 7NY"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"The Extracare Charitable Trust","endcust_email":"<EMAIL>","endcust_first_name":"Dipika","endcust_last_name":"Patel","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Coventry","endcust_postal_code":"CV3 2SN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"MACDONALD GROUNDWORKS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Lee","endcust_last_name":"Thomson","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Inverness","endcust_postal_code":"IV2 6XN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"KERF INTERIORS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Jones","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Wakefield","endcust_postal_code":"WF4 3HT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Digital River Ireland, Ltd.","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Besana UK Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Edward","endcust_last_name":"Bloomfield","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Edward","endcust_primary_admin_last_name":"Bloomfield","endcust_city":"Ipswich","endcust_postal_code":"IP3 9RR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Nguyen Sang","endcust_email":"<EMAIL>","endcust_first_name":"Sang","endcust_last_name":"Nguyen","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Birmingham","endcust_postal_code":"B29 6JW"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Derek","endcust_last_name":"Rial","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Fiona","endcust_last_name":"Collier","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ABSOLUTE CINNERCIAK INTERIORS","endcust_email":"<EMAIL>","endcust_first_name":"Danielle","endcust_last_name":"Fleming","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Harrogate","endcust_postal_code":"HG1 5EW"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"310 STUDIO","endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Cannon","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Paul","endcust_primary_admin_last_name":"Cannon","endcust_city":"Bristol","endcust_postal_code":"BS3 4HL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ALBANY FACADE ENGINEERING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Steve","endcust_last_name":"Parker","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Cambridge","endcust_postal_code":"CB4 1AL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"KEM CONTRACTING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"laura","endcust_last_name":"massey","endcust_account_csn":**********,"endcust_primary_admin_first_name":"laura","endcust_primary_admin_last_name":"massey","endcust_city":"Stourbridge","endcust_postal_code":"DY8 1AQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SHASHLESS WINDOWCOLTD","endcust_email":"<EMAIL>","endcust_first_name":"Jonathan","endcust_last_name":"Chown","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Jonathan","endcust_primary_admin_last_name":"Chown","endcust_city":"Northallerton","endcust_postal_code":"DL6 2XA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Tribotics Ltd","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Merthyr Tydfil","endcust_postal_code":"CF48 4TQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Libellum Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Cholmondeley","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mark","endcust_primary_admin_last_name":"Cholmondeley","endcust_city":"Middlesbrough","endcust_postal_code":"TS1 5AJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CT GLASS","endcust_email":"<EMAIL>","endcust_first_name":"Gary","endcust_last_name":"Thirkell","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Gary","endcust_primary_admin_last_name":"Thirkell","endcust_city":"Bradford","endcust_postal_code":"BD4 8QJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"DSB PROPERTY DESIGNS","endcust_email":"<EMAIL>","endcust_first_name":"David","endcust_last_name":"Blacker","endcust_account_csn":**********,"endcust_primary_admin_first_name":"David","endcust_primary_admin_last_name":"Blacker","endcust_city":"Southend-on-sea","endcust_postal_code":"SS2 4JJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Dr Groundworks Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Colin","endcust_last_name":"Northcott","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Colin","endcust_primary_admin_last_name":"Northcott","endcust_city":"Winchester","endcust_postal_code":"SO21 2LD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"MONUMENT FIRE DETECTION","endcust_email":"<EMAIL>","endcust_first_name":"Donna","endcust_last_name":"Eccles","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Jim","endcust_primary_admin_last_name":"Eccles","endcust_city":"Solihull","endcust_postal_code":"B90 4NG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"THOMAS DESIGN ARCHITECTS","endcust_email":"<EMAIL>","endcust_first_name":"Thomas","endcust_last_name":"Waddell","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Glasgow","endcust_postal_code":"G76 9ES"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"TIMELESS IMS","endcust_email":"<EMAIL>","endcust_first_name":"Daniel","endcust_last_name":"Wright","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Daniel","endcust_primary_admin_last_name":"Wright","endcust_city":"Sunbury","endcust_postal_code":"TW16 5DA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"ALUMINO Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Peeks","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mark","endcust_primary_admin_last_name":"Peeks","endcust_city":"Mansfield","endcust_postal_code":"NG21 0RT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Errol McLean","endcust_email":"<EMAIL>","endcust_first_name":"errol","endcust_last_name":"mclean","endcust_account_csn":**********,"endcust_primary_admin_first_name":"errol","endcust_primary_admin_last_name":"mclean","endcust_city":"Dagenham","endcust_postal_code":"RM9 6XG"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Autodesk","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"DUNCAN WOLAGE ARCHITECTS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Duncan","endcust_last_name":"Wolage","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Duncan","endcust_primary_admin_last_name":"Wolage","endcust_city":"Wantage","endcust_postal_code":"OX12 9FF"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JSA Design & Development","endcust_email":"<EMAIL>","endcust_first_name":"John","endcust_last_name":"Skelton","endcust_account_csn":**********,"endcust_primary_admin_first_name":"John","endcust_primary_admin_last_name":"Skelton","endcust_city":"York","endcust_postal_code":"YO31 1BP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JON SHEAFF & ASSOCIATES","endcust_email":"<EMAIL>","endcust_first_name":"JS&","endcust_last_name":"A","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"London","endcust_postal_code":"E8 3SE"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Marcus","endcust_last_name":"Armand","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Caitlan","endcust_last_name":"Scorer","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Richard","endcust_last_name":"Rogers","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"rhys","endcust_last_name":"gosling","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Richard","endcust_last_name":"Smith","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Darren","endcust_last_name":"Cowell","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Hayden","endcust_last_name":"Thompson","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Michael","endcust_last_name":"Parnahm","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Kath","endcust_last_name":"Marsland","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"HYWEL","endcust_last_name":"WILLIAMS","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Rob","endcust_last_name":"West","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Gcf","endcust_last_name":"Design","endcust_account_csn":********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Nigel","endcust_last_name":"Greyson","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Bedford","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CBT ELECTRICAL","endcust_email":"<EMAIL>","endcust_first_name":"Barry","endcust_last_name":"Tohill","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Barry","endcust_primary_admin_last_name":"Tohill","endcust_city":"Enfield","endcust_postal_code":"EN3 7PN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Dawson WAM Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Brian","endcust_last_name":"Hagan","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Ballynahinch","endcust_postal_code":"BT24 7EP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Neil Jones","endcust_email":"<EMAIL>","endcust_first_name":"Neil","endcust_last_name":"Jones","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Neil","endcust_primary_admin_last_name":"Jones","endcust_city":"Evesham","endcust_postal_code":"WR11 7SS"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SHEERNESS RECYCLING Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Toby","endcust_last_name":"Hills","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Toby","endcust_primary_admin_last_name":"Hills","endcust_city":"Sevenoaks","endcust_postal_code":"TN15 8JL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Artium Construction Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Dean","endcust_last_name":"Welsh","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Dean","endcust_primary_admin_last_name":"Welsh","endcust_city":"Harrogate","endcust_postal_code":"HG1 1DQ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Designer Metals Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Matthew","endcust_last_name":"Hanson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Matthew","endcust_primary_admin_last_name":"Hanson","endcust_city":"Leicester","endcust_postal_code":"LE3 5DL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Watertight Management Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Paul","endcust_last_name":"Castle","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Bromsgrove","endcust_postal_code":"B60 4AD"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":null,"endcust_email":"<EMAIL>","endcust_first_name":"Roger","endcust_last_name":"Betts","endcust_account_csn":*********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Autovision Group Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Stewart","endcust_last_name":"Collier","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Stewart","endcust_primary_admin_last_name":"Collier","endcust_city":"London","endcust_postal_code":"N1 7GU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Spectrum Design","endcust_email":"<EMAIL>","endcust_first_name":"William","endcust_last_name":"Henderson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"William","endcust_primary_admin_last_name":"Henderson","endcust_city":"Saltburn-by-the-sea","endcust_postal_code":"TS12 1PB"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"LINKS SIGNS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"James","endcust_last_name":"Kidby","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"St Leonards On Sea","endcust_postal_code":"TN38 9BA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"JOHN FOWKES ARCHITECTS","endcust_email":"<EMAIL>","endcust_first_name":"John","endcust_last_name":"Fowkes","endcust_account_csn":**********,"endcust_primary_admin_first_name":"John","endcust_primary_admin_last_name":"Fowkes","endcust_city":"Nottingham","endcust_postal_code":"NG5 5EN"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Dale Construction Services","endcust_email":"<EMAIL>","endcust_first_name":"Michael","endcust_last_name":"Dale","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Michael","endcust_primary_admin_last_name":"Dale","endcust_city":"Dunfermline","endcust_postal_code":"KY11 8GU"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"AW ELECTRICAL CONTRACTORS Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Steven","endcust_last_name":"Whitehouse","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Steven","endcust_primary_admin_last_name":"Whitehouse","endcust_city":"Birmingham","endcust_postal_code":"B46 3AL"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Man and Machine Ltd","endcust_email":null,"endcust_first_name":null,"endcust_last_name":null,"endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":null,"endcust_postal_code":null},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Morphylawrence","endcust_email":"<EMAIL>","endcust_first_name":"Philip","endcust_last_name":"Morphy","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Saxmundham","endcust_postal_code":"IP17 1BA"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Byjc Ltd","endcust_email":"<EMAIL>","endcust_first_name":"james","endcust_last_name":"carley","endcust_account_csn":**********,"endcust_primary_admin_first_name":"james","endcust_primary_admin_last_name":"carley","endcust_city":"London","endcust_postal_code":"N10 1LP"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"CIRQUE FURNITURE INTERNATIONAL Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Harry","endcust_last_name":"Hale","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Harry","endcust_primary_admin_last_name":"Hale","endcust_city":"London","endcust_postal_code":"W1F 9RT"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Mdlandscape","endcust_email":"<EMAIL>","endcust_first_name":"Marianne","endcust_last_name":"Dobson","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Marianne","endcust_primary_admin_last_name":"Dobson","endcust_city":"Knutsford","endcust_postal_code":"WA16 6LZ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SIMONMIDDLECOTEARCHITECTURELTD","endcust_email":"<EMAIL>","endcust_first_name":"Simon","endcust_last_name":"Middlecote","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Simon","endcust_primary_admin_last_name":"Middlecote","endcust_city":"Nottingham","endcust_postal_code":"NG2 6LJ"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Kuwait Petroleum Int Aviation Co. LT","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Humber","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Woking","endcust_postal_code":"GU21 5BH"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"SPF JIGSAW Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Lee","endcust_last_name":"Feast","endcust_account_csn":**********,"endcust_primary_admin_first_name":null,"endcust_primary_admin_last_name":null,"endcust_city":"Southampton","endcust_postal_code":"SO32 2LR"},{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"STRUCTURAL DESIGN SERVICES Ltd","endcust_email":"<EMAIL>","endcust_first_name":"Mark","endcust_last_name":"Goring","endcust_account_csn":**********,"endcust_primary_admin_first_name":"Mark","endcust_primary_admin_last_name":"Goring","endcust_city":"Brierley Hill","endcust_postal_code":"DY5 1XP"}]
         2: ""
         3: "autodesk\\generate_customer_table"
         4: []
         5: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.name","endcust.address1","endcust.address2","endcust.address3","endcust.city","endcust.state_province","endcust.postal_code","endcust.country","endcust.primary_admin_first_name","endcust.primary_admin_last_name","endcust.primary_admin_email","endcust.team_id","endcust.team_name","endcust.first_name","endcust.last_name","endcust.email"],"just_table":true}
         6: []
         7: false
         8: false
         9: true
         10: false
         11: null
         12: "autodesk_customers"
         13: []
         14: true
      <strong>Function:</strong> autodesk\generate_customer_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 332
         <strong>Arguments:</strong>
         0: {"order_by":"endcust_last_modified","limit":300,"search_columns":["endcust.account_csn","endcust.name","endcust.address1","endcust.address2","endcust.address3","endcust.city","endcust.state_province","endcust.postal_code","endcust.country","endcust.primary_admin_first_name","endcust.primary_admin_last_name","endcust.primary_admin_email","endcust.team_id","endcust.team_name","endcust.first_name","endcust.last_name","endcust.email"],"just_table":true}
      <strong>Function:</strong> reload_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 420
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_customers","callback":"autodesk\\generate_customer_table","data_source":"","field_names":["endcust_name","endcust_email","endcust_account_csn"]}

----------------------------------------------------------------------------
-->

<!-- datad: data_table.class.php > process_data_table() 285: tcs returning just table
-->

<!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php
-->


<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 623
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["fields"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["fields"]: array(0) {
      }
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["field"]: NULL
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-04 22:33:14"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
data_table_storage.class.php > prepare_template_data() 664
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["fields"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["fields"]: array(0) {
      }
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["field"]: NULL
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-04 22:33:14"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 670
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["fields"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["fields"]: array(0) {
      }
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["field"]: NULL
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-04 22:33:14"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 677
array(6) {
  ["hidden"]: array(0) {
  }
  ["structure"]: array(7) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["label"]: string(7) "Company"
      ["fields"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["label"]: string(8) "Customer"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["label"]: string(3) "CSN"
      ["fields"]: array(0) {
      }
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["label"]: string(13) "Primary Admin"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["label"]: string(8) "Location"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["label"]: string(13) "Last Modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["label"]: string(7) "Actions"
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["field"]: NULL
      ["filter"]: bool(false)
      ["visible"]: bool(true)
    }
  }
  ["columns"]: array(7) {
    [0]: array(2) {
      ["label"]: string(7) "Company"
      ["field"]: array(2) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
      }
    }
    [1]: array(2) {
      ["label"]: string(8) "Customer"
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
    }
    [2]: array(3) {
      ["label"]: string(3) "CSN"
      ["field"]: string(19) "endcust_account_csn"
      ["selected"]: string(3) "CSN"
    }
    [3]: array(2) {
      ["label"]: string(13) "Primary Admin"
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
    }
    [4]: array(2) {
      ["label"]: string(8) "Location"
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
    }
    [5]: array(3) {
      ["label"]: string(13) "Last Modified"
      ["field"]: string(21) "endcust_last_modified"
      ["selected"]: string(13) "Last Modified"
    }
    [6]: array(3) {
      ["label"]: string(7) "Actions"
      ["content"]: array(0) {
      }
      ["selected"]: string(7) "Actions"
    }
  }
  ["data_source_type"]: string(9) "hardcoded"
  ["data_source_id"]: NULL
  ["created_at"]: string(19) "2025-09-04 22:33:14"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 32
         <strong>Arguments:</strong>
         0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...

----------------------------------------------------------------------------
-->


<!--
********************************************************************************************************************************************************
tcs_structs: sys_edge_data-table-structure.edge.php > include() 53
array(4) {
  ["items"]: array(5) {
    [0]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(17) "Conrad Energy Ltd"
      ["endcust_email"]: string(30) "<EMAIL>"
      ["endcust_first_name"]: string(2) "IT"
      ["endcust_last_name"]: string(8) "Services"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(6) "Oxford"
      ["endcust_postal_code"]: string(7) "OX4 4GP"
    }
    [1]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(7) "POQ LTD"
      ["endcust_email"]: string(16) "<EMAIL>"
      ["endcust_first_name"]: string(6) "Sanjay"
      ["endcust_last_name"]: string(6) "Odedra"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(8) "Stanmore"
      ["endcust_postal_code"]: string(7) "HA7 3DA"
    }
    [2]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(20) "YOUR GOLF TRAVEL Ltd"
      ["endcust_email"]: string(33) "<EMAIL>"
      ["endcust_first_name"]: string(6) "Antony"
      ["endcust_last_name"]: string(7) "Puncher"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: string(6) "Antony"
      ["endcust_primary_admin_last_name"]: string(7) "Puncher"
      ["endcust_city"]: string(6) "London"
      ["endcust_postal_code"]: string(8) "EC1R 3AU"
    }
    [3]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(26) "STRATEGIC PM SOLUTIONS Ltd"
      ["endcust_email"]: string(28) "<EMAIL>"
      ["endcust_first_name"]: string(5) "Sarah"
      ["endcust_last_name"]: string(11) "Park-Murray"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: string(5) "Sarah"
      ["endcust_primary_admin_last_name"]: string(11) "Park-Murray"
      ["endcust_city"]: string(9) "Isleworth"
      ["endcust_postal_code"]: string(7) "TW7 5LF"
    }
    [4]: array(10) {
      ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
      ["endcust_name"]: string(20) "MS CAD Solutions Ltd"
      ["endcust_email"]: string(19) "<EMAIL>"
      ["endcust_first_name"]: string(4) "Mark"
      ["endcust_last_name"]: string(7) "SINNETT"
      ["endcust_account_csn"]: int(**********)
      ["endcust_primary_admin_first_name"]: NULL
      ["endcust_primary_admin_last_name"]: NULL
      ["endcust_city"]: string(8) "Barnsley"
      ["endcust_postal_code"]: string(7) "S72 9HR"
    }
  }
  ["columns"]: array(7) {
    [0]: array(11) {
      ["label"]: string(7) "Company"
      ["fields"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["field"]: array(3) {
        [0]: string(12) "endcust_name"
        [1]: string(13) "endcust_email"
        [2]: string(19) "endcust_account_csn"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [1]: array(11) {
      ["label"]: string(8) "Customer"
      ["fields"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["field"]: array(2) {
        [0]: string(18) "endcust_first_name"
        [1]: string(17) "endcust_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [2]: array(11) {
      ["label"]: string(3) "CSN"
      ["fields"]: array(0) {
      }
      ["field"]: array(0) {
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: object(Closure)#6 (1) {
        ["parameter"]: array(1) {
          ["$item"]: string(10) "<required>"
        }
      }
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [3]: array(11) {
      ["label"]: string(13) "Primary Admin"
      ["fields"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["field"]: array(2) {
        [0]: string(32) "endcust_primary_admin_first_name"
        [1]: string(31) "endcust_primary_admin_last_name"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [4]: array(11) {
      ["label"]: string(8) "Location"
      ["fields"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["field"]: array(2) {
        [0]: string(12) "endcust_city"
        [1]: string(19) "endcust_postal_code"
      }
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [5]: array(11) {
      ["label"]: string(13) "Last Modified"
      ["fields"]: array(1) {
        [0]: string(21) "endcust_last_modified"
      }
      ["field"]: string(21) "endcust_last_modified"
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: NULL
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
    [6]: array(11) {
      ["label"]: string(7) "Actions"
      ["fields"]: array(1) {
        [0]: NULL
      }
      ["field"]: NULL
      ["filter"]: bool(false)
      ["visible"]: bool(true)
      ["structure_id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
      ["action_buttons"]: array(0) {
      }
      ["replacements"]: NULL
      ["content"]: object(Closure)#6 (1) {
        ["parameter"]: array(1) {
          ["$item"]: string(10) "<required>"
        }
      }
      ["class"]: string(0) ""
      ["extra_parameters"]: string(0) ""
    }
  }
  ["available_fields"]: array(0) {
  }
  ["include_column_manager"]: bool(true)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
         <strong>Arguments:</strong>
         0: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.ph...
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table.class.php, Line: 287
         <strong>Arguments:</strong>
         0: "data-table-structure"
         1: {"title":"subscriptions","description":"","items":[{"endcust_last_modified":"2025-07-07 00:00:00","e...

----------------------------------------------------------------------------
-->

<table class="min-w-full border-collapse search_target data_table ">
    <thead>
    <tr>
        <th scope="col"
            class="relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8"
            style="isolation: isolate;"
            data-column-field="Array">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>Company</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_name_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_name","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="Array">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>Customer</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_first_name_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_first_name","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="Array">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>CSN</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="Array">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>Primary Admin</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_primary_admin_first_name_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_primary_admin_first_name","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="Array">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>Location</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_city_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_city","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="endcust_last_modified">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>Last Modified</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="endcust_last_modified_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"endcust_last_modified","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
        <th scope="col"
            class="relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            style="isolation: isolate;"
            data-column-field="">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php
            -->

            <div class="relative"
                 x-data="{
            showControls_: false,
            open_: false
     }"
                 @keydown.escape="onEscape"
                 @close-popover-group.window="onClosePopoverGroup">

                <div class="group relative inline-flex items-center">
                    <button type="button"
                            class=""
                            @click="open_ = !open_">
                        <span>Actions</span>
                    </button>
                    <div class="flex items-center ml-1 whitespace-nowrap z-30">
                        <button id="_sort"
                                class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-include=".data_table_filter"
                                hx-vals='{"order_by":"","order_direction":"ASC","callback":"autodesk\\generate_customer_table"}'
                        >

                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-up
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-up size-5 hidden" sortUp><path fill-rule="evenodd" d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z" clip-rule="evenodd" /></svg>
                            <!-- icoonier: icons.php > icons\icon() 8: mini-chevron-down
                            -->
                            <svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="group sort-down size-5 invisible group-hover:visible" sortDown><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg>            </button>
                    </div>
                </div>

                <div x-show="open_"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 translate-y-1"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 translate-y-1"
                     class="fixed z-10"
                     style="display: none"
                     x-ref="panel"
                     @click.away="open_ = false">

                    <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
                        <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                        </div>
                        <div class="bg-gray-50 px-8 py-6 flex justify-between">

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-offset-2 rounded-md bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"  @click='' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Clear            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->

                            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                            -->


                            <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-md bg-indigo-600 text-white hover:bg-indigo-500"  hx-include='.data_table_filter' hx-post='/baffletrain/autocadlt/autobooks/api/data_table/data_table_filter' hx-target='.data_table' hx-swap='outerHTML' tag_content='0' _inheritance_execution_state='[]'>
        <span>
                    Apply            </span>
                            </button>
                            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                            -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php

            -->
        </th>
    </tr>
    </thead>
    <tbody class="bg-white data_table_body">

    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-rows.edge.php
    -->


    <!--
    ********************************************************************************************************************************************************
    tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 623
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["fields"]: array(3) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
            [2]: string(19) "endcust_account_csn"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["fields"]: array(0) {
          }
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["field"]: NULL
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-04 22:33:14"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->

    <!--
    ********************************************************************************************************************************************************
    data_table_storage.class.php > prepare_template_data() 664
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["fields"]: array(3) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
            [2]: string(19) "endcust_account_csn"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["fields"]: array(0) {
          }
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["field"]: NULL
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-04 22:33:14"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->

    <!--
    ********************************************************************************************************************************************************
    tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 670
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["fields"]: array(3) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
            [2]: string(19) "endcust_account_csn"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["fields"]: array(0) {
          }
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["field"]: NULL
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-04 22:33:14"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->

    <!--
    ********************************************************************************************************************************************************
    tcs_column_preferences: data_table_storage.class.php > prepare_template_data() 677
    array(6) {
      ["hidden"]: array(0) {
      }
      ["structure"]: array(7) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["label"]: string(7) "Company"
          ["fields"]: array(3) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
            [2]: string(19) "endcust_account_csn"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["label"]: string(8) "Customer"
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["label"]: string(3) "CSN"
          ["fields"]: array(0) {
          }
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["label"]: string(13) "Primary Admin"
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["label"]: string(8) "Location"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["label"]: string(13) "Last Modified"
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["label"]: string(7) "Actions"
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["field"]: NULL
          ["filter"]: bool(false)
          ["visible"]: bool(true)
        }
      }
      ["columns"]: array(7) {
        [0]: array(2) {
          ["label"]: string(7) "Company"
          ["field"]: array(2) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
          }
        }
        [1]: array(2) {
          ["label"]: string(8) "Customer"
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
        }
        [2]: array(3) {
          ["label"]: string(3) "CSN"
          ["field"]: string(19) "endcust_account_csn"
          ["selected"]: string(3) "CSN"
        }
        [3]: array(2) {
          ["label"]: string(13) "Primary Admin"
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
        }
        [4]: array(2) {
          ["label"]: string(8) "Location"
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
        }
        [5]: array(3) {
          ["label"]: string(13) "Last Modified"
          ["field"]: string(21) "endcust_last_modified"
          ["selected"]: string(13) "Last Modified"
        }
        [6]: array(3) {
          ["label"]: string(7) "Actions"
          ["content"]: array(0) {
          }
          ["selected"]: string(7) "Actions"
        }
      }
      ["data_source_type"]: string(9) "hardcoded"
      ["data_source_id"]: NULL
      ["created_at"]: string(19) "2025-09-04 22:33:14"
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-rows.edge.php, Line: 32
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"

    ----------------------------------------------------------------------------
    -->





    <!--
    ********************************************************************************************************************************************************
    sys_edge_data-table-rows.edge.php > include() 61
    array(5) {
      ["items"]: array(5) {
        [0]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(28) "<EMAIL>"
          ["endcust_first_name"]: string(5) "Barry"
          ["endcust_last_name"]: string(8) "Williams"
          ["endcust_account_csn"]: int(********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [1]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(27) "<EMAIL>"
          ["endcust_first_name"]: string(4) "Adam"
          ["endcust_last_name"]: string(9) "Strudwick"
          ["endcust_account_csn"]: int(*********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [2]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(24) "<EMAIL>"
          ["endcust_first_name"]: string(7) "Richard"
          ["endcust_last_name"]: string(7) "Greaves"
          ["endcust_account_csn"]: int(*********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [3]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(27) "<EMAIL>"
          ["endcust_first_name"]: string(6) "Gareth"
          ["endcust_last_name"]: string(6) "Fowler"
          ["endcust_account_csn"]: int(*********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
        [4]: array(10) {
          ["endcust_last_modified"]: string(19) "2025-07-07 00:00:00"
          ["endcust_name"]: NULL
          ["endcust_email"]: string(31) "<EMAIL>"
          ["endcust_first_name"]: string(5) "Moira"
          ["endcust_last_name"]: string(8) "Mcintyre"
          ["endcust_account_csn"]: int(********)
          ["endcust_primary_admin_first_name"]: NULL
          ["endcust_primary_admin_last_name"]: NULL
          ["endcust_city"]: NULL
          ["endcust_postal_code"]: NULL
        }
      }
      ["count"]: int(300)
      ["columns"]: array(7) {
        [0]: array(11) {
          ["label"]: string(7) "Company"
          ["fields"]: array(3) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
            [2]: string(19) "endcust_account_csn"
          }
          ["field"]: array(3) {
            [0]: string(12) "endcust_name"
            [1]: string(13) "endcust_email"
            [2]: string(19) "endcust_account_csn"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_0_1c76cbfe21c6f44c1d1e59d54f3e4420"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [1]: array(11) {
          ["label"]: string(8) "Customer"
          ["fields"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["field"]: array(2) {
            [0]: string(18) "endcust_first_name"
            [1]: string(17) "endcust_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_1_ce26601dac0dea138b7295f02b7620a7"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [2]: array(11) {
          ["label"]: string(3) "CSN"
          ["fields"]: array(0) {
          }
          ["field"]: array(0) {
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_2_adc4365ac462ffb7ae7f47f348acbad4"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: object(Closure)#6 (1) {
            ["parameter"]: array(1) {
              ["$item"]: string(10) "<required>"
            }
          }
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [3]: array(11) {
          ["label"]: string(13) "Primary Admin"
          ["fields"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["field"]: array(2) {
            [0]: string(32) "endcust_primary_admin_first_name"
            [1]: string(31) "endcust_primary_admin_last_name"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [4]: array(11) {
          ["label"]: string(8) "Location"
          ["fields"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["field"]: array(2) {
            [0]: string(12) "endcust_city"
            [1]: string(19) "endcust_postal_code"
          }
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_4_ce5bf551379459c1c61d2a204061c455"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [5]: array(11) {
          ["label"]: string(13) "Last Modified"
          ["fields"]: array(1) {
            [0]: string(21) "endcust_last_modified"
          }
          ["field"]: string(21) "endcust_last_modified"
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_5_84ba4cf46c874b0b5951eb3b75298329"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: NULL
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
        [6]: array(11) {
          ["label"]: string(7) "Actions"
          ["fields"]: array(1) {
            [0]: NULL
          }
          ["field"]: NULL
          ["filter"]: bool(false)
          ["visible"]: bool(true)
          ["structure_id"]: string(38) "col_6_06df33001c1d7187fdd81ea1f5b277aa"
          ["action_buttons"]: array(0) {
          }
          ["replacements"]: NULL
          ["content"]: object(Closure)#6 (1) {
            ["parameter"]: array(1) {
              ["$item"]: string(10) "<required>"
            }
          }
          ["class"]: string(0) ""
          ["extra_parameters"]: string(0) ""
        }
      }
      ["column_count"]: int(7)
      ["available_fields"]: array(0) {
      }
    }

        ----------------------------------------------------------------------------
          <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
             <strong>Arguments:</strong>
             0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"
          <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
             <strong>Arguments:</strong>
             0: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...
             1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-rows.edge.php"
          <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 84
             <strong>Arguments:</strong>
             0: "data-table-rows"
             1: {"items":[{"endcust_last_modified":"2025-07-07 00:00:00","endcust_name":"Conrad Energy Ltd","endcust...

    ----------------------------------------------------------------------------
    -->


    <tr class="border-t border-gray-300 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Conrad Energy Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    IT<br>Services                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Oxford<br>OX4 4GP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    POQ LTD<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sanjay<br>Odedra                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Stanmore<br>HA7 3DA                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    YOUR GOLF TRAVEL Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Antony<br>Puncher                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Antony<br>Puncher                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    London<br>EC1R 3AU                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    STRATEGIC PM SOLUTIONS Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sarah<br>Park-Murray                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sarah<br>Park-Murray                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Isleworth<br>TW7 5LF                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    MS CAD Solutions Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Mark<br>SINNETT                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Barnsley<br>S72 9HR                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Proactive Learning Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Matt<br>Dawson                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    St. Neots<br>PE19 5ZA                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Vida Design Ltd<br><EMAIL>                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Joe<br>Dobson                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":null,"tab_title":"Customer "}' @click='showModal = true' data-tab-title='Customer ' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Joe<br>Dobson                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bristol<br>BS1 6AA                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":null,"tab_title":"Customer "}' @click='showModal = true' data-tab-title='Customer ' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Serena<br>Federico                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Camland Developments Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Adam<br>Owen                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Adam<br>Owen                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Swanscombe<br>DA10 0DF                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    TD SYNNEX UK Limited<br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    ADR Consulting<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Alexis<br>Rouzee                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Alexis<br>Rouzee                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Canterbury<br>CT4 6HD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Coleman Hicks Partnership<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sam<br>Cook                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Kidlington<br>OX5 2DN                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Rosie Wilkins Landscape Design<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Rosie<br>Wilkins                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Rosie<br>Wilkins                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sheffield<br>S5 7DD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    STEEL LINE Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Craig<br>Stanley                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sheffield<br>S13 9NR                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Jonathan Carter Design<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Jonathan<br>Carter                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Jonathan<br>Carter                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    London<br>SE4 1YD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    MACLEAN ARCHITECTURE<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    James<br>Maclean                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    James<br>Maclean                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Knaresborough<br>HG5 0AD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Mark Cheetham<br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Stockton-On-Tees<br>TS17 6AQ                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>*********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Clarice<br>Elliot                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    TCS CAD & BIM Solutions Limited<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Aurangzaib<br>Mahmood                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Aurangzaib<br>Mahmood                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Stockton On Tees<br>TS17 6BX                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    COVENTRY CONSTRUCTION Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Roman<br>Pundyk                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Coventry<br>CV4 9AP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    POQ Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sanjay<br>Odedra                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Sanjay<br>Odedra                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bushey<br>WD23 1NP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    WISBECH GRAMMAR SCHOOL<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Andrew<br>Dighton                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Peterborough<br>PE7 2AX                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    CPI Group<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Wes<br>Dennehy                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Wes<br>Dennehy                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Chatham<br>ME5 8TD                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    BLOCK ARCHITECTS Ltd<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bruce<br>O'Brien                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Bellshill<br>ML4 3NP                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    KELBROOK MILL Co.<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Keith<br>Hendry                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Thirsk<br>YO7 4AZ                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Pace Property Reports & Projec<br><EMAIL><br>**********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Phil<br>Griffiths                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Wallasey<br>CH44 8EE                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":**********,"tab_title":"Customer **********"}' @click='showModal = true' data-tab-title='Customer **********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Kim<br>West                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":********,"tab_title":"Customer ********"}' @click='showModal = true' data-tab-title='Customer ********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":********,"tab_title":"Customer ********"}' @click='showModal = true' data-tab-title='Customer ********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>*********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Susan<br>Fisher                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>*********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Ian<br>Kirkland                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>*********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Peter<br>Crowther                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":*********,"tab_title":"Customer *********"}' @click='showModal = true' data-tab-title='Customer *********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>
    <tr class="border-t border-gray-200 "
        id="" >
        <td class="whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    <EMAIL><br>********                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                    Matt<br>Button                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":********,"tab_title":"Customer ********"}' @click='showModal = true' data-tab-title='Customer ********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="Array">
            <div class="flex items-center gap-2"><div class="flex-1">
                </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="endcust_last_modified">
            <div class="flex items-center gap-2"><div class="flex-1">
                    2025-07-07 00:00:00                                                                                    </div>
            </div>
        </td>
        <td class="whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell" data-column-field="">
            <div class="flex items-center gap-2"><div class="flex-1">

                    <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php
                    -->


                    <button class = "inline-flex items-center font-semibold px-2.5 py-1.5 text-sm shadow-sm focus-visible:outline-indigo-600 rounded-full bg-indigo-600 text-white hover:bg-indigo-500"  type='button' icon='book-open' icon_position='replace' hx-post='/baffletrain/autocadlt/autobooks/api/view' hx-swap='innerHTML' hx-target='#modal_body' hx-vals='{"csn":********,"tab_title":"Customer ********"}' @click='showModal = true' data-tab-title='Customer ********' data_target='#modal_body' data_subscription_number='' tag_content='0' _inheritance_execution_state='[]'>
        <span>

<!-- icoonier: icons.php > icons\icon() 8: book-open
-->
<svg xmlns="http://www.w3.org/2000/svg"  fill="none"  stroke-width="1.5" stroke="currentColor" class="size-6" ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"></path></svg>            </span>
                    </button>
                    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_forms-button.edge.php

                    -->
                </div>
            </div>
        </td>
    </tr>

    <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-rows.edge.php

    -->
    </tbody>
    <tfoot>
    <tr>
        <td colspan="7">

            <!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_pagination-strip.edge.php
            -->


            <div class="flex bottom-0 items-center justify-between border-t border-gray-200 bg-white px-4 py-2 sm:px-6">
                <div class="flex flex-1 justify-between sm:hidden">
                    <a href="#"
                       hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                       hx-target=".search_target"
                       hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                       hx-vals='{"page": 1}'
                       class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
                    <a href="#"
                       hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                       hx-target=".search_target"
                       hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                       hx-vals='{"page": 0}'
                       class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing
                            <span class="font-medium">-29</span>
                            to
                            <span class="font-medium">0</span>
                            of
                            <span class="font-medium">0 </span>
                            results
                        </p>
                    </div>
                    <div>
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <a href="#"
                               hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                               hx-target=".search_target"
                               hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                               hx-vals='{"page": 1}'
                               class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Previous</span>
                                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                    <path fill-rule="evenodd"
                                          d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </a>



                            <a href="#"
                               hx-get="/baffletrain/autocadlt/autobooks/api/data_table/pagination"
                               hx-target=".search_target"
                               hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                               hx-vals='{"page": 0}'
                               class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Next</span>
                                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                    <path fill-rule="evenodd"
                                          d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_pagination-strip.edge.php

            -->
        </td>
    </tr>
    </tfoot>
</table>
{


<!-- edge: edge.class.php > phprender() 170: ================================================================== Loading: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-column-manager-panel.edge.php
-->


<!-- oob_swap: sys_edge_data-table-column-manager-panel.edge.php > include() 17: << empty boolean>>
-->



<div class="p-4 border-b border-gray-200 bg-gray-50">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
        <div class="flex gap-2">

            <button type="button" class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                + Column
            </button>
            <button type="button"
                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_all_columns"
                    hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                Show All
            </button>
            <button type="button"
                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/hide_all_columns"
                    hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                Hide All
            </button>
        </div>
    </div>


    <div class="flex items-center space-x-3 mb-3">
        <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
        <select id="data-source-select"
                hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source_and_columns"
                hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                hx-target=".data_table"
                hx-swap="outerHTML"
                hx-trigger="change"
                name="data_source_selection"
                class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            <option value="hardcoded" >
                Default (Hardcoded Data)
            </option>
        </select>
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Hardcoded
                    </span>
    </div>


    <div class="flex items-center space-x-3 mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
        <div class="flex items-center">
            <input type="checkbox"
                   id="apply_default_config_autodesk_customers"
                   hx-post="/baffletrain/autocadlt/autobooks/api/unified_field_definitions/apply_default_config"
                   hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                   hx-target=".data_table"
                   hx-swap="outerHTML"
                   hx-trigger="change"
                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
            <label for="apply_default_config_autodesk_customers" class="ml-2 text-xs font-medium text-gray-700">
                Apply default field configuration
            </label>
        </div>
        <div class="flex-1 flex items-center">
            <div class="relative" x-data="{ showTooltip: false }">
                <button type="button"
                        @mouseenter="showTooltip = true"
                        @mouseleave="showTooltip = false"
                        class="ml-2 text-gray-400 hover:text-gray-600">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </button>
                <div x-show="showTooltip"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95"
                     class="absolute z-50 w-64 p-2 mt-1 text-xs text-white bg-gray-800 rounded shadow-lg -top-2 left-6">
                    Uses the "Show by default" settings from unified field definitions to configure initial column
                    display.
                    <div class="absolute top-2 -left-1 w-2 h-2 bg-gray-800 transform rotate-45"></div>
                </div>
            </div>
        </div>
    </div>


    <div x-transition class="mb-3 p-2 bg-blue-50 rounded border">
        <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_column"
              hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
              hx-target=".data_table"
              hx-swap="outerHTML"
              class="flex gap-2 items-center">
            <input type="text"
                   name="column_name"
                   placeholder="Column name (e.g., Contact Info)"
                   class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                   required>
            <button type="submit"
                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                Add
            </button>
            <button type="button"
                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                Cancel
            </button>
        </form>
    </div>

    <div class="text-xs text-gray-500">
        Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
    </div>
</div>


<div class="flex-1 overflow-y-auto p-4" >
    <div class="sortable space-y-3"
         data-sortable-group="columns"
         data-sortable-handle=".column-drag-handle"
         hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"
         hx-trigger="sorted"
         hx-target=".data_table"
         hx-swap="outerHTML"
         hx-include=".column-data"
         x-ref="columnList">
        <input type="hidden" name="table_name" class="column-data" value="autodesk_customers">
        <input type="hidden" name="callback" class="column-data" value="autodesk\generate_customer_table">
        <input type="hidden" name="data_source" class="column-data" value="">

    </div>
</div>


<div class="p-4 border-t border-gray-200 bg-gray-50">
    <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center">
            <button type="button"
                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/hide_all_columns"
                    hx-vals='{"table_name": "autodesk_customers", "callback": "autodesk\generate_customer_table"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                Hide All
            </button>
            <span class="text-xs text-gray-500">0 columns, 0 fields</span>
        </div>
        <button type="button"
                @click="open = false"
                class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
            Done
        </button>
    </div>
</div>

<!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-column-manager-panel.edge.php

-->

<!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php

-->


<!-- edge.class.php > phprender() 172: [edge] [2025-09-04 22:33:27] [edge.class.php:172] Complete /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_layout_layout-api.edge.php

-->
