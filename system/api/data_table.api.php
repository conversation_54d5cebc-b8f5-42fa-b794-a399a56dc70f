<?php

namespace api\data_table;

use data_table\data_table;
use system\database;
use edge\edge;

// Include enhanced data callback functions if they exist

// Include logs callback functions if they exist
if (file_exists(FS_SYS_FUNCTIONS . DS . 'logs.fn.php')) {
    require_once FS_SYS_FUNCTIONS . DS . 'logs.fn.php';
}

// Include unified field definitions callback functions if they exist
if (file_exists(FS_SYS_FUNCTIONS . DS . 'unified_field_definitions_callbacks.fn.php')) {
    require_once FS_SYS_FUNCTIONS . DS . 'unified_field_definitions_callbacks.fn.php';
}

/**
 * Generate columns from data array
 * @param array $items Data items to extract columns from
 * @param array $skip_fields Fields to skip (default: system fields)
 * @return array Array of column definitions and available fields
 */
function generate_columns_from_data($items, $skip_fields = ['id', 'data_hash', 'created_at', 'updated_at']) {
    $columns = [];
    $available_fields = [];

    if (!empty($items)) {
        $first_item = reset($items);
        foreach (array_keys($first_item) as $field) {
            if (in_array($field, $skip_fields)) {
                continue;
            }

            $columns[] = [
                'label' => ucwords(str_replace('_', ' ', $field)),
                'field' => $field,
                'filter' => false,
                'extra_parameters' => ''
            ];
            $available_fields[] = $field;
        }
    }

    return ['columns' => $columns, 'available_fields' => $available_fields];
}

/**
 * Generate columns from database table schema
 * @param string $table_name Database table name
 * @param array $skip_fields Fields to skip (default: system fields)
 * @return array Array of column definitions and available fields
 */
function generate_columns_from_schema($table_name, $skip_fields = ['id', 'data_hash', 'created_at', 'updated_at']) {
    $columns = [];
    $available_fields = [];

    try {
        $table_info = \system\data_source_manager::get_table_info($table_name);

        if ($table_info && isset($table_info['columns'])) {
            foreach ($table_info['columns'] as $column) {
                $column_name = $column['Field']; // DESCRIBE returns 'Field' not 'name'

                if (in_array($column_name, $skip_fields)) {
                    continue;
                }

                $columns[] = [
                    'label' => ucwords(str_replace('_', ' ', $column_name)),
                    'field' => $column_name,
                    'filter' => false,
                    'extra_parameters' => ''
                ];
                $available_fields[] = $column_name;
            }
        }
    } catch (Exception $e) {
        tcs_log("Error generating columns from schema for table $table_name: " . $e->getMessage(), 'data_table_saga');
    }

    return ['columns' => $columns, 'available_fields' => $available_fields];
}

/**
 * Setup search columns for a data source if they don't exist
 * @param int $data_source_id Data source ID
 * @return void
 */
function setup_data_source_search_columns($data_source_id) {
    $data_source = \system\data_source_manager::get_data_source($data_source_id);
    if (!$data_source || !empty($data_source['search_columns'])) {
        return; // Already has search columns or doesn't exist
    }

    $table_name = $data_source['table_name'];
    $table_info = \system\data_source_manager::get_table_info($table_name);

    if (!$table_info || !isset($table_info['columns'])) {
        return;
    }

    $searchable_columns = [];
    foreach ($table_info['columns'] as $column) {
        $column_name = $column['Field'];
        $column_type = strtolower($column['Type']);

        // Skip system columns
        if (in_array($column_name, ['id', 'created_at', 'updated_at', 'data_hash'])) {
            continue;
        }

        // Include text-based columns for search
        if (strpos($column_type, 'varchar') !== false ||
            strpos($column_type, 'text') !== false ||
            strpos($column_type, 'char') !== false) {
            $searchable_columns[] = $table_name . '.' . $column_name;
        }
    }

    if (empty($searchable_columns)) {
        return;
    }

    try {
        // Add search_columns field if it doesn't exist
        $check_column_sql = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources' AND COLUMN_NAME = 'search_columns'";
        $stmt = database::rawQuery($check_column_sql);
        $column_exists = $stmt->fetchColumn();

        if (!$column_exists) {
            $add_column_sql = "ALTER TABLE `autobooks_data_sources` ADD COLUMN `search_columns` longtext DEFAULT NULL COMMENT 'JSON array of searchable column names for full-text search'";
            database::rawQuery($add_column_sql);
        }

        // Update the data source
        $update_sql = "UPDATE `autobooks_data_sources` SET `search_columns` = ? WHERE `id` = ?";
        database::rawQuery($update_sql, [json_encode($searchable_columns), $data_source_id]);

        tcs_log("Updated data source $data_source_id with search columns: " . json_encode($searchable_columns), 'data_table_saga');
    } catch (Exception $e) {
        tcs_log("Error updating data source search columns: " . $e->getMessage(), 'data_table_saga');
    }
}

/**
 * Get cached data sources to avoid repeated database queries
 * @return array Array of available data sources
 */
function get_cached_data_sources() {
    static $cached_data_sources = null;

    if ($cached_data_sources === null) {
        $available_data_sources = [];
        try {
            $data_sources = database::table('autobooks_data_sources')
                ->where('status', '=', 'active')
                ->get();

            foreach ($data_sources as $source) {
                $available_data_sources[] = [
                    'id' => $source['id'],
                    'name' => $source['name'],
                    'description' => $source['description'],
                    'category' => $source['category'] ?? 'other'
                ];
            }
            $cached_data_sources = $available_data_sources;
        } catch (Exception $e) {
            $cached_data_sources = [];
        }
    }

    return $cached_data_sources;
}

/**
 * Get cached database columns to avoid repeated queries
 * @param string $db_table Database table name
 * @param array $available_fields Available fields from data
 * @return array Array of available field list
 */
function get_available_field_list($db_table, $available_fields = []) {
    static $cached_db_columns = [];

    if (!empty($available_fields)) {
        return $available_fields;
    }

    if (empty($db_table)) {
        return [];
    }

    $cache_key = $db_table;
    if (!isset($cached_db_columns[$cache_key])) {
        try {
            $cached_db_columns[$cache_key] = database::table($db_table)->getColumns();
        } catch (Exception $e) {
            $cached_db_columns[$cache_key] = [];
        }
    }

    return $cached_db_columns[$cache_key];
}

/**
 * Process column configuration from data_table_storage
 * @param array $config Configuration from data_table_storage
 * @param array $columns Original columns
 * @param array $column_preferences User preferences (if any)
 * @return array Processed columns structure
 */
function process_column_configuration($config, $columns, $column_preferences = []) {
    $column_structure = $column_preferences['structure'] ?? [];
    $hidden_columns = $column_preferences['hidden'] ?? [];

    // Use structure from configuration if it exists
    if ($config && isset($config['configuration']['structure']) && !empty($config['configuration']['structure'])) {
        $processed_columns = [];
        foreach ($config['configuration']['structure'] as $col_config) {
            if ($col_config['visible'] ?? true) {
                $processed_columns[] = [
                    'id' => $col_config['id'] ?? 'col_' . md5($col_config['field']),
                    'label' => $col_config['label'],
                    'field' => $col_config['field'],
                    'filter' => $col_config['filter'] ?? false,
                    'fields' => is_array($col_config['field']) ? $col_config['field'] : [$col_config['field']],
                    'visible' => true
                ];
            }
        }
        return $processed_columns;
    }

    // Use structure from preferences if it exists
    if (!empty($column_structure)) {
        return $column_structure;
    }

    // Generate structure from original columns
    $processed_columns = [];
    foreach ($columns as $index => $col) {
        $column_id = 'col_' . $index . '_' . md5($col['label']);
        $processed_columns[] = [
            'id' => $column_id,
            'label' => $col['label'],
            'field' => $col['field'],
            'filter' => $col['filter'] ?? false,
            'fields' => is_array($col['field']) ? $col['field'] : [$col['field']],
            'visible' => !in_array($column_id, $hidden_columns)
        ];
    }

    return $processed_columns;
}

/**
 * Render column manager panel with data table structure
 * @param string $table_name Table name
 * @param int $data_source_id Data source ID
 * @param array $processed_columns Processed column structure
 * @param array $hidden_columns Hidden columns
 * @param array $available_field_list Available fields
 * @param array $items Data items
 * @param array $columns Column definitions
 * @param array $available_fields Available fields from data
 * @param array $p Additional parameters
 * @return string HTML content
 */
function render_column_manager_with_table($table_name, $data_source_id, $processed_columns, $hidden_columns, $available_field_list, $items, $columns, $available_fields, $p = []):string {
    $available_data_sources = get_cached_data_sources();

    tcs_log("Column manager props: table=$table_name, type=data_source, id=$data_source_id", 'data_table_saga');
    print_rr([
        'data_table_column_manager_start' => [
            'table_name' => $table_name,
            'available_fields' => $available_fields,
            'current_data_source_type' => 'data_source',
            'current_data_source_id' => $data_source_id
        ]
    ], 'data_table_column_manager_start');

    // Column manager panel as OOB swap
    $column_manager_panel = '<div id="column_manager_panel_' . $table_name . '" hx-swap-oob="innerHTML">' .
        edge::render('data-table-column-manager-panel', [
            'table_name' => $table_name,
            'current_data_source_type' => 'data_source',
            'current_data_source_id' => $data_source_id,
            'available_data_sources' => $available_data_sources,
            'processed_columns' => $processed_columns,
            'hidden_columns' => $hidden_columns,
            'available_field_list' => $available_field_list
        ]) . '</div>';

    // Data table structure (not full data-table)
    $data_table_structure = edge::render('data-table-structure', [
        'columns' => $columns,
        'column_preferences' => [], // Could be passed if needed
        'sort_column' => $p['sort_column'] ?? '',
        'sort_direction' => $p['sort_direction'] ?? '',
        'callback' => $p['callback'] ?? null,
        'items_per_page' => $p['items_per_page'] ?? 30,
        'current_page_num' => $p['current_page_num'] ?? 1,
        'total_count' => $p['total_count'] ?? count($items),
        'items' => $items,
        'class' => $p['class'] ?? ''
    ]);

    return $column_manager_panel . $data_table_structure;
}

function refresh_table($params): false|string {
    return data_table::reload_table($params);
}

function data_table_filter($p) {
    $criteria = data_table::api_process_criteria($p);

    // Handle callback functions first
    if (isset($p['callback']) && function_exists($p['callback'])) {
        print_rr("calling " . $p['callback'] . " criteria: " . print_r($criteria, true));
        return $p['callback'](criteria: $criteria,just_table: true);
    }
    print_rr("no callback found for " . $p['callback'] . " criteria: " . print_r($criteria, true));

    // Handle data source access
    if (isset($p['data_source_id']) && !empty($p['data_source_id'])) {
        return handle_data_source_request($p, $criteria);
    }

    // Handle table name access
    if (isset($p['table_name'])) {
        return handle_table_request($p, $criteria);
    }

    // Default fallback
    return edge::render('data-table-structure', [
        'columns' => [],
        'column_preferences' => [],
        'sort_column' => '',
        'sort_direction' => '',
        'callback' => null,
        'items_per_page' => 30,
        'current_page_num' => 1,
        'total_count' => 0,
        'items' => [],
        'class' => '',
        'external_call' => true
    ]);
}

/**
 * Handle data source requests
 * @param array $p Parameters
 * @param array $criteria Search criteria
 * @return string HTML content
 */
function handle_data_source_request($p, $criteria) {
    $data_source_id = (int)$p['data_source_id'];
    $table_name = $p['table_name'] ?? 'data_source_' . $data_source_id;
    $user_id = $_SESSION['user_id'] ?? null;

    // Try data_table_storage first (handles configuration and data)
    $data_result = \system\data_table_storage::get_table_data($table_name, [], $criteria, $user_id, $data_source_id);

    if ($data_result['success'] && $data_result['source'] === 'data_source') {
        return handle_data_table_storage_result($data_result, $table_name, $data_source_id, $user_id, $p);
    }

    // Fallback to direct data source access
    return handle_direct_data_source_access($data_source_id, $criteria, $p);
}

/**
 * Handle data_table_storage results with column management
 * @param array $data_result Result from data_table_storage
 * @param string $table_name Table name
 * @param int $data_source_id Data source ID
 * @param int|null $user_id User ID
 * @param array $p Parameters
 * @return string HTML content
 */
function handle_data_table_storage_result($data_result, $table_name, $data_source_id, $user_id, $p) {
    $items = $data_result['data'];
    $available_fields = [];

    // Get configuration to determine column structure
    $config = \system\data_table_storage::get_configuration($table_name, $user_id, $data_source_id);

    if ($config && isset($config['configuration']['structure']) && !empty($config['configuration']['structure'])) {
        // Use configured column structure
        $columns = [];
        foreach ($config['configuration']['structure'] as $col_config) {
            if ($col_config['visible'] ?? true) {
                $columns[] = [
                    'label' => $col_config['label'],
                    'field' => $col_config['field'],
                    'filter' => $col_config['filter'] ?? false,
                    'extra_parameters' => ''
                ];
                $available_fields[] = $col_config['field'];
            }
        }
    } else {
        // Auto-generate columns from data
        $column_result = generate_columns_from_data($items);
        $columns = $column_result['columns'];
        $available_fields = $column_result['available_fields'];
    }

    // Process column configuration for column manager
    $processed_columns = process_column_configuration($config, $columns);
    $hidden_columns = []; // Could be extracted from preferences if needed
    $available_field_list = get_available_field_list('', $available_fields);

    return render_column_manager_with_table(
        $table_name,
        $data_source_id,
        $processed_columns,
        $hidden_columns,
        $available_field_list,
        $items,
        $columns,
        $available_fields,
        $p
    );
}

/**
 * Handle direct data source access
 * @param int $data_source_id Data source ID
 * @param array $criteria Search criteria
 * @param array $p Parameters
 * @return string HTML content
 */
function handle_direct_data_source_access($data_source_id, $criteria, $p) {
    // Setup search columns if needed
    setup_data_source_search_columns($data_source_id);

    // Get data from data source
    $data_result = \system\data_source_manager::get_data_source_data($data_source_id, $criteria);

    if (!$data_result['success']) {
        return edge::render('data-table-structure', [
            'columns' => [],
            'column_preferences' => [],
            'sort_column' => '',
            'sort_direction' => '',
            'callback' => null,
            'items_per_page' => 30,
            'current_page_num' => 1,
            'total_count' => 0,
            'items' => [],
            'class' => ''
        ]);
    }

    $items = $data_result['data'];

    if (!empty($items)) {
        // Generate columns from data
        $column_result = generate_columns_from_data($items);
        $columns = $column_result['columns'];
        $available_fields = $column_result['available_fields'];
    } else {
        // Generate columns from schema
        $data_source = \system\data_source_manager::get_data_source($data_source_id);
        if ($data_source && !empty($data_source['table_name'])) {
            $column_result = generate_columns_from_schema($data_source['table_name']);
            $columns = $column_result['columns'];
            $available_fields = $column_result['available_fields'];
        } else {
            $columns = [];
            $available_fields = [];
        }
    }

    return edge::render('data-table-structure', [
        'columns' => $columns,
        'column_preferences' => [],
        'sort_column' => $p['sort_column'] ?? '',
        'sort_direction' => $p['sort_direction'] ?? '',
        'callback' => $p['callback'] ?? null,
        'items_per_page' => $p['items_per_page'] ?? 30,
        'current_page_num' => $p['current_page_num'] ?? 1,
        'total_count' => $p['total_count'] ?? count($items),
        'items' => $items,
        'class' => $p['class'] ?? ''
    ]);
}

/**
 * Handle table name requests
 * @param array $p Parameters
 * @param array $criteria Search criteria
 * @return string HTML content
 */
function handle_table_request($p, $criteria) {
    $user_id = $_SESSION['user_id'] ?? null;
    $data_result = \system\data_table_storage::get_table_data($p['table_name'], [], $criteria, $user_id);

    if (!$data_result['success']) {
        return edge::render('data-table-structure', [
            'columns' => [],
            'column_preferences' => [],
            'sort_column' => '',
            'sort_direction' => '',
            'callback' => null,
            'items_per_page' => 30,
            'current_page_num' => 1,
            'total_count' => 0,
            'items' => [],
            'class' => ''
        ]);
    }

    $items = $data_result['data'];
    $columns = $data_result['columns'] ?? [];

    // Generate columns from data if we have data
    if (!empty($items)) {
        $column_result = generate_columns_from_data($items);
        $columns = $column_result['columns'];
        $available_fields = $column_result['available_fields'];
    } else {
        $available_fields = [];
    }

    return edge::render('data-table-structure', [
        'columns' => $columns,
        'column_preferences' => [],
        'sort_column' => $p['sort_column'] ?? '',
        'sort_direction' => $p['sort_direction'] ?? '',
        'callback' => $p['callback'] ?? null,
        'items_per_page' => $p['items_per_page'] ?? 30,
        'current_page_num' => $p['current_page_num'] ?? 1,
        'total_count' => $p['total_count'] ?? count($items),
        'items' => $items,
        'class' => $p['class'] ?? ''
    ]);
}

/**
 * Handle data table pagination requests
 * This function processes pagination requests from data tables
 *
 * @param array $p Parameters from the request including page, callback, etc.
 * @return string HTML content of the updated data table
 */
function pagination($p)
{
    // Extract pagination parameters
    $page = (int)($p['page'] ?? 1);
    $per_page = (int)($p['per_page'] ?? 30);
    $callback = $p['callback'] ?? '';
    $table_name = $p['table_name'] ?? '';

    // Build criteria for the callback function
    $criteria = [
        'limit' => $per_page,
        'offset' => ($page - 1) * $per_page,
        'page' => $page,
        'pagination_mode' => true  // Flag to indicate this is a pagination request
    ];

    // Add search terms if provided
    if (isset($p['search_terms']) && !empty($p['search_terms'])) {
        $criteria['search'] = $p['search_terms'];
    }

    // Add sorting if provided
    if (isset($p['sort_column']) && !empty($p['sort_column'])) {
        $criteria['order_by'] = $p['sort_column'];
        $criteria['order_direction'] = $p['sort_direction'] ?? 'asc';
    }

    // Call the appropriate callback function
    if (!empty($callback) && function_exists($callback)) {
        return $callback($criteria);
    } elseif (!empty($table_name)) {
        // Use enhanced data API for table-based requests
        return \api\enhanced_data\get_table_data(array_merge($p, [
            'page' => $page,
            'per_page' => $per_page,
            'just_body' => true
        ]));
    }

    return '<div class="error">Invalid pagination request</div>';
}