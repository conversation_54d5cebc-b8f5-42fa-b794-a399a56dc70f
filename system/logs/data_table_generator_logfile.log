[data_table_generator] [2025-09-05 13:33:48] [data_table_generator.class.php:169] Applying unified field-based intelligent column selection for table: autobooks_import_sketchup_data
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:182] Unified field suggestions for 59 columns: 48 matches found
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'id' has no unified field match, showing as essential
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'sold_to_name' -> 'sold_to_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'sold_to_number' -> 'sold_to_number' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'vendor_name' -> 'vendor_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'reseller_number' -> 'reseller_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'reseller_vendor_id' -> 'vendor_id' (confidence: 85, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_vendor_id' -> 'vendor_id' (confidence: 85, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_name' -> 'company_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_address_1' -> 'address' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_address_2' -> 'end_customer_address_2' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_address_3' -> 'end_customer_address_3' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_city' -> 'city' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_state' -> 'state' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_zip_code' -> 'postal_code' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_country' -> 'country' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_account_type' -> 'end_customer_account_type' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_contact_name' -> 'contact_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_contact_email' -> 'email' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_contact_phone' -> 'end_customer_contact_phone' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'end_customer_industry_segment' -> 'end_customer_industry_segment' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'agreement_program_name' -> 'product_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'agreement_number' -> 'subscription_reference' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'agreement_start_date' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'agreement_end_date' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'agreement_terms' -> 'agreement_terms' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'agreement_type' -> 'agreement_type' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'agreement_status' -> 'agreement_status' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'agreement_support_level' -> 'agreement_support_level' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'agreement_days_due' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'agreement_autorenew' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_name' -> 'product_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_family' -> 'product_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_market_segment' -> 'product_market_segment' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_release' -> 'product_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_type' -> 'product_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_deployment' -> 'product_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_sku' -> 'product_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_sku_description' -> 'product_sku_description' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_part' -> 'product_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_list_price' -> 'product_list_price' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'product_list_price_currency' -> 'product_name' (confidence: 90, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_id' -> 'subscription_reference' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_serial_number' -> 'subscription_reference' (confidence: 85, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_status' -> 'status' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_quantity' -> 'quantity' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'subscription_start_date' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'subscription_end_date' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_contact_name' -> 'contact_name' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_contact_email' -> 'email' (confidence: 100, show_by_default: true)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'subscription_level' -> 'subscription_level' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'subscription_days_due' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'quotation_id' -> 'quotation_id' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'quotation_type' -> 'quotation_type' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'quotation_vendor_id' -> 'vendor_id' (confidence: 85, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'quotation_deal_registration_number' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'quotation_status' -> 'quotation_status' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'quotation_resellerpo_previous' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'quotation_due_date' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:213] Column 'flaer_phase' -> 'flaer_phase' (confidence: 100, show_by_default: false)
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:220] Column 'updated' has no unified field match, hiding by default
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:262] Applied max visible columns limit: showing 8, moved 18 to available
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:269] Unified field-based column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-09-05 13:33:49] [data_table_generator.class.php:63]  Using available fields from unified field matching: 52 fields
