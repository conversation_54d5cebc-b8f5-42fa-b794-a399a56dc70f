[data_table_saga] [2025-09-09 14:06:02] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:06:05] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:32] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:32] [sys_edge_data-table-rows.edge.php:30]  Data table template sys_edge_data-table-rows.edge: table=test_template_inheritance.php_test_template_inheritance.php, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:32] [sys_edge_data-table.edge.php:37]  Data table template sys_edge_data-table.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:32] [sys_edge_data-table-column-manager.edge.php:33]  Data table template sys_edge_data-table-column-manager.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:32] [data_table_storage.class.php:306] get_table_data failed: Array\n(\n    [success] => \n    [configuration] => Array\n        (\n        )\n\n    [data_source_id] => \n    [table_name] => \n    [updated_at] => \n    [error] => \n<!--\n********************************************************************************************************************************************************\ndata_table_storage_get_error: data_table_storage.class.php > get_configuration() 51\narray(2) {\n  ["result"]: NULL\n  ["query"]: string(185) "SELECT configuration, data_source_id, updated_at, table_name, user_id FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 AND `table_name` = :where_table_name_1 LIMIT 1"\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> get_configuration, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php, Line: 305\n         <strong>Arguments:</strong> \n         0: "test_users"\n         1: 2\n         2: null\n      <strong>Function:</strong> get_table_data, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php, Line: 627\n         <strong>Arguments:</strong> \n         0: "test_users"\n         1: []\n         2: []\n         3: 2\n         4: null\n      <strong>Function:</strong> prepare_template_data, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-column-manager.edge.php, Line: 46\n         <strong>Arguments:</strong> \n         0: {"items":[],"columns":[{"label":"ID","field":"id"},{"label":"Name","field":"name"},{"label":"Email",...\n\n----------------------------------------------------------------------------\n-->\n\n)\n
[data_table_saga] [2025-09-09 14:13:32] [data_table_storage.class.php:323] Getting table data: table=test_users, type=, id=
[data_table_saga] [2025-09-09 14:13:32] [data_table_storage.class.php:358] Using hardcoded data
[data_table_saga] [2025-09-09 14:13:32] [data_table_storage.class.php:637] Auto-loading data: table=test_users, success=true, source=hardcoded id=n/a, count=0
[data_table_saga] [2025-09-09 14:13:32] [sys_edge_data-table-column-manager.edge.php:101] Column manager props: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=test_template_inheritance.php_test_template_inheritance.php, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-rows.edge.php:30]  Data table template sys_edge_data-table-rows.edge: table=test_template_inheritance.php_test_template_inheritance.php, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-rows.edge.php:30]  Data table template sys_edge_data-table-rows.edge: table=test_template_inheritance.php_test_template_inheritance.php, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-rows.edge.php:30]  Data table template sys_edge_data-table-rows.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-column-manager.edge.php:33]  Data table template sys_edge_data-table-column-manager.edge: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:13:33] [sys_edge_data-table-column-manager.edge.php:101] Column manager props: table=test_users, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:21:31] [sys_edge_data-table.edge.php:37]  Data table template sys_edge_data-table.edge: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:328] Using data source: 51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=data_source id=51, count=3772
[data_table_saga] [2025-09-09 14:21:31] [sys_edge_data-table-column-manager.edge.php:33]  Data table template sys_edge_data-table-column-manager.edge: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=, id=
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:358] Using hardcoded data
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=hardcoded id=n/a, count=0
[data_table_saga] [2025-09-09 14:21:31] [sys_edge_data-table-column-manager.edge.php:101] Column manager props: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:21:31] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:328] Using data source: 51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:772] Overriding hardcoded data with data source: table=autodesk_subscriptions, success=true, source=data_source, count=3772
[data_table_saga] [2025-09-09 14:21:31] [sys_edge_data-table-rows.edge.php:30]  Data table template sys_edge_data-table-rows.edge: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:328] Using data source: 51
[data_table_saga] [2025-09-09 14:21:31] [data_table_storage.class.php:772] Overriding hardcoded data with data source: table=autodesk_subscriptions, success=true, source=data_source, count=3772
[data_table_saga] [2025-09-09 14:32:22] [sys_edge_data-table-structure.edge.php:30]  Data table template sys_edge_data-table-structure.edge: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:32:26] [sys_edge_data-table.edge.php:37]  Data table template sys_edge_data-table.edge: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:32:26] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:32:26] [data_table_storage.class.php:328] Using data source: 51
[data_table_saga] [2025-09-09 14:32:26] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=data_source id=51, count=3772
[data_table_saga] [2025-09-09 14:32:26] [sys_edge_data-table-column-manager.edge.php:33]  Data table template sys_edge_data-table-column-manager.edge: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:32:26] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=, id=
[data_table_saga] [2025-09-09 14:32:26] [data_table_storage.class.php:358] Using hardcoded data
[data_table_saga] [2025-09-09 14:32:26] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=hardcoded id=n/a, count=0
[data_table_saga] [2025-09-09 14:32:26] [sys_edge_data-table-column-manager.edge.php:101] Column manager props: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:37:11] [sys_edge_data-table.edge.php:37]  Data table template sys_edge_data-table.edge: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:37:11] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:37:11] [data_table_storage.class.php:328] Using data source: 51
[data_table_saga] [2025-09-09 14:37:11] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=data_source id=51, count=3772
[data_table_saga] [2025-09-09 14:37:11] [sys_edge_data-table-column-manager.edge.php:33]  Data table template sys_edge_data-table-column-manager.edge: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:37:11] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=, id=
[data_table_saga] [2025-09-09 14:37:11] [data_table_storage.class.php:358] Using hardcoded data
[data_table_saga] [2025-09-09 14:37:11] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=hardcoded id=n/a, count=0
[data_table_saga] [2025-09-09 14:37:11] [sys_edge_data-table-column-manager.edge.php:101] Column manager props: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:38:51] [sys_edge_data-table.edge.php:37]  Data table template sys_edge_data-table.edge: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:38:51] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=data_source, id=51
[data_table_saga] [2025-09-09 14:38:52] [data_table_storage.class.php:328] Using data source: 51
[data_table_saga] [2025-09-09 14:38:52] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=data_source id=51, count=3772
[data_table_saga] [2025-09-09 14:38:52] [sys_edge_data-table-column-manager.edge.php:33]  Data table template sys_edge_data-table-column-manager.edge: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-09-09 14:38:52] [data_table_storage.class.php:323] Getting table data: table=autodesk_subscriptions, type=, id=
[data_table_saga] [2025-09-09 14:38:52] [data_table_storage.class.php:358] Using hardcoded data
[data_table_saga] [2025-09-09 14:38:52] [data_table_storage.class.php:637] Auto-loading data: table=autodesk_subscriptions, success=true, source=hardcoded id=n/a, count=0
[data_table_saga] [2025-09-09 14:38:52] [sys_edge_data-table-column-manager.edge.php:101] Column manager props: table=autodesk_subscriptions, type=hardcoded, id=
