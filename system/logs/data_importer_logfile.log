[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TANDEM ARCHITECTS Ltd\n    [:up_name] => TANDEM ARCHITECTS Ltd\n    [:address1] => 5 East Street\n    [:up_address1] => 5 East Street\n    [:address2] => Drayton\n    [:up_address2] => Drayton\n    [:city] => Langport\n    [:up_city] => Langport\n    [:postal_code] => TA10 0LB\n    [:up_postal_code] => TA10 0LB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SOMERSET\n    [:up_state_province] => SOMERSET\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Paul\n    [:up_first_name] => Paul\n    [:last_name] => Milton\n    [:up_last_name] => Milton\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068046\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T09:13:09+01:00\n    [:up_quote_created_time] => 2025-09-08T09:13:09+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2861\n    [:up_end_customer] => 2861\n    [:quote_contact] => 14462\n    [:up_quote_contact] => 14462\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-10-08\n    [:up_start_date] => 2025-10-08\n    [:end_date] => 2026-10-07\n    [:up_end_date] => 2026-10-07\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 56080812530913\n    [:up_subscription_id] => 56080812530913\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-10-07\n    [:up_subscription_endDate] => 2025-10-07\n    [:quote_id] => 2665\n    [:up_quote_id] => 2665\n)\n
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-08 08:13:15] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => ARDERN & DRUGGAN Ltd\n    [:up_name] => ARDERN & DRUGGAN Ltd\n    [:address1] => The Powerhouse Unit C6 Cooil Road\n    [:up_address1] => The Powerhouse Unit C6 Cooil Road\n    [:address2] => Douglas\n    [:up_address2] => Douglas\n    [:city] => Isle Of Man\n    [:up_city] => Isle Of Man\n    [:postal_code] => IM4 2AY\n    [:up_postal_code] => IM4 2AY\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => kevin\n    [:up_first_name] => kevin\n    [:last_name] => Druggan\n    [:up_last_name] => Druggan\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068070\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T09:16:36+01:00\n    [:up_quote_created_time] => 2025-09-08T09:16:36+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 820\n    [:up_total_list_amount] => 820\n    [:total_net_amount] => 820\n    [:up_total_net_amount] => 820\n    [:total_amount] => 820\n    [:up_total_amount] => 820\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 5494\n    [:up_end_customer] => 5494\n    [:quote_contact] => 276620\n    [:up_quote_contact] => 276620\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-28\n    [:up_start_date] => 2025-08-28\n    [:end_date] => 2026-08-27\n    [:up_end_date] => 2026-08-27\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 820\n    [:up_extended_srp] => 820\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 820\n    [:up_end_user_price] => 820\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72483951997396\n    [:up_subscription_id] => 72483951997396\n    [:subscription_quantity] => 2\n    [:up_subscription_quantity] => 2\n    [:subscription_endDate] => 2025-08-27\n    [:up_subscription_endDate] => 2025-08-27\n    [:quote_id] => 2669\n    [:up_quote_id] => 2669\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => ARDERN & DRUGGAN Ltd\n    [:up_name] => ARDERN & DRUGGAN Ltd\n    [:address1] => The Powerhouse Unit C6 Cooil Road\n    [:up_address1] => The Powerhouse Unit C6 Cooil Road\n    [:address2] => Douglas\n    [:up_address2] => Douglas\n    [:city] => Isle Of Man\n    [:up_city] => Isle Of Man\n    [:postal_code] => IM4 2AY\n    [:up_postal_code] => IM4 2AY\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => kevin\n    [:up_first_name] => kevin\n    [:last_name] => Druggan\n    [:up_last_name] => Druggan\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068070\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T09:16:36+01:00\n    [:up_quote_created_time] => 2025-09-08T09:16:36+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 820\n    [:up_total_list_amount] => 820\n    [:total_net_amount] => 820\n    [:up_total_net_amount] => 820\n    [:total_amount] => 820\n    [:up_total_amount] => 820\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 5494\n    [:up_end_customer] => 5494\n    [:quote_contact] => 276620\n    [:up_quote_contact] => 276620\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-28\n    [:up_start_date] => 2025-08-28\n    [:end_date] => 2026-08-27\n    [:up_end_date] => 2026-08-27\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 820\n    [:up_extended_srp] => 820\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 820\n    [:up_end_user_price] => 820\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72483951997396\n    [:up_subscription_id] => 72483951997396\n    [:subscription_quantity] => 2\n    [:up_subscription_quantity] => 2\n    [:subscription_endDate] => 2025-08-27\n    [:up_subscription_endDate] => 2025-08-27\n    [:quote_id] => 2669\n    [:up_quote_id] => 2669\n)\n
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-08 08:16:42] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TIM HB Ltd\n    [:up_name] => TIM HB Ltd\n    [:address1] => 6 Charterhouse Close\n    [:up_address1] => 6 Charterhouse Close\n    [:city] => Cheddar\n    [:up_city] => Cheddar\n    [:postal_code] => BS27 3XT\n    [:up_postal_code] => BS27 3XT\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SOMERSET\n    [:up_state_province] => SOMERSET\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Tim\n    [:up_first_name] => Tim\n    [:last_name] => Brigstocke\n    [:up_last_name] => Brigstocke\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068908\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T12:11:00+01:00\n    [:up_quote_created_time] => 2025-09-08T12:11:00+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2843\n    [:up_end_customer] => 2843\n    [:quote_contact] => 275533\n    [:up_quote_contact] => 275533\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-14\n    [:up_start_date] => 2025-09-14\n    [:end_date] => 2026-09-13\n    [:up_end_date] => 2026-09-13\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 60008357566478\n    [:up_subscription_id] => 60008357566478\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-13\n    [:up_subscription_endDate] => 2025-09-13\n    [:quote_id] => 2677\n    [:up_quote_id] => 2677\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TIM HB Ltd\n    [:up_name] => TIM HB Ltd\n    [:address1] => 6 Charterhouse Close\n    [:up_address1] => 6 Charterhouse Close\n    [:city] => Cheddar\n    [:up_city] => Cheddar\n    [:postal_code] => BS27 3XT\n    [:up_postal_code] => BS27 3XT\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SOMERSET\n    [:up_state_province] => SOMERSET\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Tim\n    [:up_first_name] => Tim\n    [:last_name] => Brigstocke\n    [:up_last_name] => Brigstocke\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068908\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T12:11:00+01:00\n    [:up_quote_created_time] => 2025-09-08T12:11:00+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2843\n    [:up_end_customer] => 2843\n    [:quote_contact] => 275533\n    [:up_quote_contact] => 275533\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-14\n    [:up_start_date] => 2025-09-14\n    [:end_date] => 2026-09-13\n    [:up_end_date] => 2026-09-13\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 60008357566478\n    [:up_subscription_id] => 60008357566478\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-13\n    [:up_subscription_endDate] => 2025-09-13\n    [:quote_id] => 2677\n    [:up_quote_id] => 2677\n)\n
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-08 11:11:06] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Aurangzaib\n    [:up_first_name] => Aurangzaib\n    [:last_name] => Mahmood\n    [:up_last_name] => Mahmood\n    [:phone] => +***********\n    [:up_phone] => +***********\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Alu-Fix (Facades) Ltd\n    [:up_name] => Alu-Fix (Facades) Ltd\n    [:address1] => Unit 3 Bellsbridge Office Park 100c\n    [:up_address1] => Unit 3 Bellsbridge Office Park 100c\n    [:address2] => Ladas Drive\n    [:up_address2] => Ladas Drive\n    [:city] => Belfast\n    [:up_city] => Belfast\n    [:postal_code] => BT6 9FH\n    [:up_postal_code] => BT6 9FH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => County Antrim\n    [:up_state_province] => County Antrim\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Ben\n    [:up_first_name] => Ben\n    [:last_name] => Wilson\n    [:up_last_name] => Wilson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068980\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T12:28:25+01:00\n    [:up_quote_created_time] => 2025-09-08T12:28:25+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3786\n    [:up_end_customer] => 3786\n    [:quote_contact] => 14838\n    [:up_quote_contact] => 14838\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Aurangzaib\n    [:up_first_name] => Aurangzaib\n    [:last_name] => Mahmood\n    [:up_last_name] => Mahmood\n    [:phone] => +***********\n    [:up_phone] => +***********\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Alu-Fix (Facades) Ltd\n    [:up_name] => Alu-Fix (Facades) Ltd\n    [:address1] => Unit 3 Bellsbridge Office Park 100c\n    [:up_address1] => Unit 3 Bellsbridge Office Park 100c\n    [:address2] => Ladas Drive\n    [:up_address2] => Ladas Drive\n    [:city] => Belfast\n    [:up_city] => Belfast\n    [:postal_code] => BT6 9FH\n    [:up_postal_code] => BT6 9FH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => County Antrim\n    [:up_state_province] => County Antrim\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Ben\n    [:up_first_name] => Ben\n    [:last_name] => Wilson\n    [:up_last_name] => Wilson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1068980\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-08T12:28:25+01:00\n    [:up_quote_created_time] => 2025-09-08T12:28:25+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3786\n    [:up_end_customer] => 3786\n    [:quote_contact] => 14838\n    [:up_quote_contact] => 14838\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-08 11:28:31] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
