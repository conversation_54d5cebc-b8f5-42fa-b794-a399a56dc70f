[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TURNBULL SURVEYING\n    [:up_name] => TURNBULL SURVEYING\n    [:address1] => 1 Tankersley Lane\n    [:up_address1] => 1 Tankersley Lane\n    [:address2] => Hoyland\n    [:up_address2] => Hoyland\n    [:city] => Barnsley\n    [:up_city] => Barnsley\n    [:postal_code] => S74 0DR\n    [:up_postal_code] => S74 0DR\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SOUTH YORKSHIRE\n    [:up_state_province] => SOUTH YORKSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Nicolas\n    [:up_first_name] => Nicolas\n    [:last_name] => Turnbull\n    [:up_last_name] => Turnbull\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1059389\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T08:58:50+01:00\n    [:up_quote_created_time] => 2025-09-04T08:58:50+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1849\n    [:up_end_customer] => 1849\n    [:quote_contact] => 276492\n    [:up_quote_contact] => 276492\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-17\n    [:up_start_date] => 2025-09-17\n    [:end_date] => 2026-09-16\n    [:up_end_date] => 2026-09-16\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55146233514144\n    [:up_subscription_id] => 55146233514144\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-16\n    [:up_subscription_endDate] => 2025-09-16\n    [:quote_id] => 2583\n    [:up_quote_id] => 2583\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TURNBULL SURVEYING\n    [:up_name] => TURNBULL SURVEYING\n    [:address1] => 1 Tankersley Lane\n    [:up_address1] => 1 Tankersley Lane\n    [:address2] => Hoyland\n    [:up_address2] => Hoyland\n    [:city] => Barnsley\n    [:up_city] => Barnsley\n    [:postal_code] => S74 0DR\n    [:up_postal_code] => S74 0DR\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SOUTH YORKSHIRE\n    [:up_state_province] => SOUTH YORKSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Nicolas\n    [:up_first_name] => Nicolas\n    [:last_name] => Turnbull\n    [:up_last_name] => Turnbull\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1059389\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T08:58:50+01:00\n    [:up_quote_created_time] => 2025-09-04T08:58:50+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1849\n    [:up_end_customer] => 1849\n    [:quote_contact] => 276492\n    [:up_quote_contact] => 276492\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-17\n    [:up_start_date] => 2025-09-17\n    [:end_date] => 2026-09-16\n    [:up_end_date] => 2026-09-16\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55146233514144\n    [:up_subscription_id] => 55146233514144\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-16\n    [:up_subscription_endDate] => 2025-09-16\n    [:quote_id] => 2583\n    [:up_quote_id] => 2583\n)\n
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 07:58:57] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => RE-FORM LANDSCAPE ARCHITECTURE\n    [:up_name] => RE-FORM LANDSCAPE ARCHITECTURE\n    [:address1] => Tower Works 2 Globe Road\n    [:up_address1] => Tower Works 2 Globe Road\n    [:city] => Leeds\n    [:up_city] => Leeds\n    [:postal_code] => LS11 5QG\n    [:up_postal_code] => LS11 5QG\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Sroboshi\n    [:up_first_name] => Sroboshi\n    [:last_name] => Das\n    [:up_last_name] => Das\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1059462\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T09:11:15+01:00\n    [:up_quote_created_time] => 2025-09-04T09:11:15+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 323\n    [:up_end_customer] => 323\n    [:quote_contact] => 18290\n    [:up_quote_contact] => 18290\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => RE-FORM LANDSCAPE ARCHITECTURE\n    [:up_name] => RE-FORM LANDSCAPE ARCHITECTURE\n    [:address1] => Tower Works 2 Globe Road\n    [:up_address1] => Tower Works 2 Globe Road\n    [:city] => Leeds\n    [:up_city] => Leeds\n    [:postal_code] => LS11 5QG\n    [:up_postal_code] => LS11 5QG\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Sroboshi\n    [:up_first_name] => Sroboshi\n    [:last_name] => Das\n    [:up_last_name] => Das\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1059462\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T09:11:15+01:00\n    [:up_quote_created_time] => 2025-09-04T09:11:15+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 323\n    [:up_end_customer] => 323\n    [:quote_contact] => 18290\n    [:up_quote_contact] => 18290\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 08:11:21] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Broad Planning and Architecture\n    [:up_name] => Broad Planning and Architecture\n    [:address1] => Sandon Road\n    [:up_address1] => Sandon Road\n    [:city] => Wolverhampton\n    [:up_city] => Wolverhampton\n    [:postal_code] => WV10 6EL\n    [:up_postal_code] => WV10 6EL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => WEST MIDLANDS\n    [:up_state_province] => WEST MIDLANDS\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Sheila\n    [:up_first_name] => Sheila\n    [:last_name] => Porter\n    [:up_last_name] => Porter\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1059754\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T10:07:18+01:00\n    [:up_quote_created_time] => 2025-09-04T10:07:18+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3635\n    [:up_end_customer] => 3635\n    [:quote_contact] => 19162\n    [:up_quote_contact] => 19162\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-29\n    [:up_start_date] => 2025-09-29\n    [:end_date] => 2026-09-28\n    [:up_end_date] => 2026-09-28\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 69600139405590\n    [:up_subscription_id] => 69600139405590\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-28\n    [:up_subscription_endDate] => 2025-09-28\n    [:quote_id] => 2591\n    [:up_quote_id] => 2591\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Broad Planning and Architecture\n    [:up_name] => Broad Planning and Architecture\n    [:address1] => Sandon Road\n    [:up_address1] => Sandon Road\n    [:city] => Wolverhampton\n    [:up_city] => Wolverhampton\n    [:postal_code] => WV10 6EL\n    [:up_postal_code] => WV10 6EL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => WEST MIDLANDS\n    [:up_state_province] => WEST MIDLANDS\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Sheila\n    [:up_first_name] => Sheila\n    [:last_name] => Porter\n    [:up_last_name] => Porter\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1059754\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T10:07:18+01:00\n    [:up_quote_created_time] => 2025-09-04T10:07:18+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3635\n    [:up_end_customer] => 3635\n    [:quote_contact] => 19162\n    [:up_quote_contact] => 19162\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-29\n    [:up_start_date] => 2025-09-29\n    [:end_date] => 2026-09-28\n    [:up_end_date] => 2026-09-28\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 69600139405590\n    [:up_subscription_id] => 69600139405590\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-28\n    [:up_subscription_endDate] => 2025-09-28\n    [:quote_id] => 2591\n    [:up_quote_id] => 2591\n)\n
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 09:07:25] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Northumbria Healthcare NHS Foundation Trust\n    [:up_name] => Northumbria Healthcare NHS Foundation Trust\n    [:address1] => Unit 7 Northumbria House 7-8 Silver\n    [:up_address1] => Unit 7 Northumbria House 7-8 Silver\n    [:address2] => Fox Way Cobalt Business Park\n    [:up_address2] => Fox Way Cobalt Business Park\n    [:city] => Newcastle Upon Tyne\n    [:up_city] => Newcastle Upon Tyne\n    [:postal_code] => NE27 0QJ\n    [:up_postal_code] => NE27 0QJ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => TYNE AND WEAR\n    [:up_state_province] => TYNE AND WEAR\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Gillian\n    [:up_first_name] => Gillian\n    [:last_name] => Finn\n    [:up_last_name] => Finn\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060325\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T11:49:52+01:00\n    [:up_quote_created_time] => 2025-09-04T11:49:52+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 275952\n    [:up_end_customer] => 275952\n    [:quote_contact] => 275953\n    [:up_quote_contact] => 275953\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 10:49:58] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n    [:address1] => 34-36 Rose Street North Lane\n    [:up_address1] => 34-36 Rose Street North Lane\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH2 2NP\n    [:up_postal_code] => EH2 2NP\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => C\n    [:up_first_name] => C\n    [:last_name] => Sutherland\n    [:up_last_name] => Sutherland\n    [:phone] => +*********\n    [:up_phone] => +*********\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060637\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:24:05+01:00\n    [:up_quote_created_time] => 2025-09-04T13:24:05+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1917\n    [:up_end_customer] => 1917\n    [:quote_contact] => 276520\n    [:up_quote_contact] => 276520\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-17\n    [:up_start_date] => 2025-09-17\n    [:end_date] => 2026-09-16\n    [:up_end_date] => 2026-09-16\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55536230799844\n    [:up_subscription_id] => 55536230799844\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-16\n    [:up_subscription_endDate] => 2025-09-16\n    [:quote_id] => 2599\n    [:up_quote_id] => 2599\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n    [:address1] => 34-36 Rose Street North Lane\n    [:up_address1] => 34-36 Rose Street North Lane\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH2 2NP\n    [:up_postal_code] => EH2 2NP\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => C\n    [:up_first_name] => C\n    [:last_name] => Sutherland\n    [:up_last_name] => Sutherland\n    [:phone] => +*********\n    [:up_phone] => +*********\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060637\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:24:05+01:00\n    [:up_quote_created_time] => 2025-09-04T13:24:05+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1917\n    [:up_end_customer] => 1917\n    [:quote_contact] => 276520\n    [:up_quote_contact] => 276520\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-17\n    [:up_start_date] => 2025-09-17\n    [:end_date] => 2026-09-16\n    [:up_end_date] => 2026-09-16\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55536230799844\n    [:up_subscription_id] => 55536230799844\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-16\n    [:up_subscription_endDate] => 2025-09-16\n    [:quote_id] => 2599\n    [:up_quote_id] => 2599\n)\n
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 12:24:12] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => CRS Engineering Ltd\n    [:up_name] => CRS Engineering Ltd\n    [:address1] => Hatfield Drive\n    [:up_address1] => Hatfield Drive\n    [:city] => Cramlington\n    [:up_city] => Cramlington\n    [:postal_code] => NE23 7TU\n    [:up_postal_code] => NE23 7TU\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => NORTHUMBERLAND\n    [:up_state_province] => NORTHUMBERLAND\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Simpson\n    [:up_last_name] => Simpson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060665\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:30:10+01:00\n    [:up_quote_created_time] => 2025-09-04T13:30:10+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3275\n    [:up_end_customer] => 3275\n    [:quote_contact] => 276528\n    [:up_quote_contact] => 276528\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-11\n    [:up_start_date] => 2025-09-11\n    [:end_date] => 2026-09-10\n    [:up_end_date] => 2026-09-10\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55536338787291\n    [:up_subscription_id] => 55536338787291\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-10\n    [:up_subscription_endDate] => 2025-09-10\n    [:quote_id] => 2603\n    [:up_quote_id] => 2603\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => CRS Engineering Ltd\n    [:up_name] => CRS Engineering Ltd\n    [:address1] => Hatfield Drive\n    [:up_address1] => Hatfield Drive\n    [:city] => Cramlington\n    [:up_city] => Cramlington\n    [:postal_code] => NE23 7TU\n    [:up_postal_code] => NE23 7TU\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => NORTHUMBERLAND\n    [:up_state_province] => NORTHUMBERLAND\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Simpson\n    [:up_last_name] => Simpson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060665\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:30:10+01:00\n    [:up_quote_created_time] => 2025-09-04T13:30:10+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3275\n    [:up_end_customer] => 3275\n    [:quote_contact] => 276528\n    [:up_quote_contact] => 276528\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-11\n    [:up_start_date] => 2025-09-11\n    [:end_date] => 2026-09-10\n    [:up_end_date] => 2026-09-10\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55536338787291\n    [:up_subscription_id] => 55536338787291\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-10\n    [:up_subscription_endDate] => 2025-09-10\n    [:quote_id] => 2603\n    [:up_quote_id] => 2603\n)\n
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-09-04 12:30:16] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => BLACKFRIARS STAGING Ltd\n    [:up_name] => BLACKFRIARS STAGING Ltd\n    [:address1] => 39-69 Westmoor Street\n    [:up_address1] => 39-69 Westmoor Street\n    [:city] => London\n    [:up_city] => London\n    [:postal_code] => SE7 8NR\n    [:up_postal_code] => SE7 8NR\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Beau\n    [:up_first_name] => Beau\n    [:last_name] => Oladigbolu\n    [:up_last_name] => Oladigbolu\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060704\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:37:45+01:00\n    [:up_quote_created_time] => 2025-09-04T13:37:45+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3691\n    [:up_end_customer] => 3691\n    [:quote_contact] => 276342\n    [:up_quote_contact] => 276342\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => BLACKFRIARS STAGING Ltd\n    [:up_name] => BLACKFRIARS STAGING Ltd\n    [:address1] => 39-69 Westmoor Street\n    [:up_address1] => 39-69 Westmoor Street\n    [:city] => London\n    [:up_city] => London\n    [:postal_code] => SE7 8NR\n    [:up_postal_code] => SE7 8NR\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Beau\n    [:up_first_name] => Beau\n    [:last_name] => Oladigbolu\n    [:up_last_name] => Oladigbolu\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060704\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:37:45+01:00\n    [:up_quote_created_time] => 2025-09-04T13:37:45+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3691\n    [:up_end_customer] => 3691\n    [:quote_contact] => 276342\n    [:up_quote_contact] => 276342\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 12:37:51] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Zotefoams PLC\n    [:up_name] => Zotefoams PLC\n    [:address1] => 675 Mitcham Road\n    [:up_address1] => 675 Mitcham Road\n    [:city] => Croydon\n    [:up_city] => Croydon\n    [:postal_code] => CR9 3AL\n    [:up_postal_code] => CR9 3AL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Vijay\n    [:up_first_name] => Vijay\n    [:last_name] => Latchman\n    [:up_last_name] => Latchman\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060745\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:47:09+01:00\n    [:up_quote_created_time] => 2025-09-04T13:47:09+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 276543\n    [:up_end_customer] => 276543\n    [:quote_contact] => 276544\n    [:up_quote_contact] => 276544\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Zotefoams PLC\n    [:up_name] => Zotefoams PLC\n    [:address1] => 675 Mitcham Road\n    [:up_address1] => 675 Mitcham Road\n    [:city] => Croydon\n    [:up_city] => Croydon\n    [:postal_code] => CR9 3AL\n    [:up_postal_code] => CR9 3AL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Vijay\n    [:up_first_name] => Vijay\n    [:last_name] => Latchman\n    [:up_last_name] => Latchman\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060745\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:47:09+01:00\n    [:up_quote_created_time] => 2025-09-04T13:47:09+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 276543\n    [:up_end_customer] => 276543\n    [:quote_contact] => 276544\n    [:up_quote_contact] => 276544\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 12:47:15] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Zotefoams PLC\n    [:up_name] => Zotefoams PLC\n    [:address1] => 675 Mitcham Road\n    [:up_address1] => 675 Mitcham Road\n    [:city] => Croydon\n    [:up_city] => Croydon\n    [:postal_code] => CR9 3AL\n    [:up_postal_code] => CR9 3AL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Vijay\n    [:up_first_name] => Vijay\n    [:last_name] => Latchman\n    [:up_last_name] => Latchman\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060752\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:48:42+01:00\n    [:up_quote_created_time] => 2025-09-04T13:48:42+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 276543\n    [:up_end_customer] => 276543\n    [:quote_contact] => 276544\n    [:up_quote_contact] => 276544\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Zotefoams PLC\n    [:up_name] => Zotefoams PLC\n    [:address1] => 675 Mitcham Road\n    [:up_address1] => 675 Mitcham Road\n    [:city] => Croydon\n    [:up_city] => Croydon\n    [:postal_code] => CR9 3AL\n    [:up_postal_code] => CR9 3AL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Vijay\n    [:up_first_name] => Vijay\n    [:last_name] => Latchman\n    [:up_last_name] => Latchman\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1060752\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T13:48:42+01:00\n    [:up_quote_created_time] => 2025-09-04T13:48:42+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 276543\n    [:up_end_customer] => 276543\n    [:quote_contact] => 276544\n    [:up_quote_contact] => 276544\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 12:48:48] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n)\n
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Willen Works Willen Road\n    [:up_address1] => Willen Works Willen Road\n    [:city] => Newport Pagnell\n    [:up_city] => Newport Pagnell\n    [:state_province] => BUCKINGHAMSHIRE\n    [:up_state_province] => BUCKINGHAMSHIRE\n    [:postal_code] => MK16 0DG\n    [:up_postal_code] => MK16 0DG\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Oil & Gas\n    [:up_parent_industry_segment] => Oil & Gas\n    [:primary_admin_first_name] => Sharron\n    [:up_primary_admin_first_name] => Sharron\n    [:primary_admin_last_name] => Clegg\n    [:up_primary_admin_last_name] => Clegg\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 6917954\n    [:up_team_id] => 6917954\n    [:team_name] => Sharron Clegg - 7954\n    [:up_team_name] => Sharron Clegg - 7954\n    [:first_name] => Sharron\n    [:up_first_name] => Sharron\n    [:last_name] => Clegg\n    [:up_last_name] => Clegg\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 13:09:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55388049199615\n    [:subscriptionReferenceNumber] => 561-82001311\n    [:up_subscriptionReferenceNumber] => 561-82001311\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-03-31\n    [:up_startDate] => 2019-03-31\n    [:endDate] => 2026-09-06\n    [:up_endDate] => 2026-09-06\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276559\n    [:up_endCustomer_id] => 276559\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 5793\n    [:up_soldTo_id] => 5793\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n)\n
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Willen Works Willen Road\n    [:up_address1] => Willen Works Willen Road\n    [:city] => Newport Pagnell\n    [:up_city] => Newport Pagnell\n    [:state_province] => BUCKINGHAMSHIRE\n    [:up_state_province] => BUCKINGHAMSHIRE\n    [:postal_code] => MK16 0DG\n    [:up_postal_code] => MK16 0DG\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Oil & Gas\n    [:up_parent_industry_segment] => Oil & Gas\n    [:primary_admin_first_name] => Sharron\n    [:up_primary_admin_first_name] => Sharron\n    [:primary_admin_last_name] => Clegg\n    [:up_primary_admin_last_name] => Clegg\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 6917954\n    [:up_team_id] => 6917954\n    [:team_name] => Sharron Clegg - 7954\n    [:up_team_name] => Sharron Clegg - 7954\n    [:first_name] => Sharron\n    [:up_first_name] => Sharron\n    [:last_name] => Clegg\n    [:up_last_name] => Clegg\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 13:09:20] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55388049199615\n    [:subscriptionReferenceNumber] => 561-82001311\n    [:up_subscriptionReferenceNumber] => 561-82001311\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-03-31\n    [:up_startDate] => 2019-03-31\n    [:endDate] => 2026-09-06\n    [:up_endDate] => 2026-09-06\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276562\n    [:up_endCustomer_id] => 276562\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 5793\n    [:up_soldTo_id] => 5793\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1061147\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T15:00:44+01:00\n    [:up_quote_created_time] => 2025-09-04T15:00:44+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1061147\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T15:00:44+01:00\n    [:up_quote_created_time] => 2025-09-04T15:00:44+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 14:00:50] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1061156\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T15:02:29+01:00\n    [:up_quote_created_time] => 2025-09-04T15:02:29+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1061156\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T15:02:29+01:00\n    [:up_quote_created_time] => 2025-09-04T15:02:29+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 14:02:35] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1061163\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T15:04:29+01:00\n    [:up_quote_created_time] => 2025-09-04T15:04:29+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1061163\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-04T15:04:29+01:00\n    [:up_quote_created_time] => 2025-09-04T15:04:29+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-04 14:04:35] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n)\n
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Willen Works Willen Road\n    [:up_address1] => Willen Works Willen Road\n    [:city] => Newport Pagnell\n    [:up_city] => Newport Pagnell\n    [:state_province] => BUCKINGHAMSHIRE\n    [:up_state_province] => BUCKINGHAMSHIRE\n    [:postal_code] => MK16 0DG\n    [:up_postal_code] => MK16 0DG\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Oil & Gas\n    [:up_parent_industry_segment] => Oil & Gas\n    [:primary_admin_first_name] => Sharron\n    [:up_primary_admin_first_name] => Sharron\n    [:primary_admin_last_name] => Clegg\n    [:up_primary_admin_last_name] => Clegg\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 6917954\n    [:up_team_id] => 6917954\n    [:team_name] => Sharron Clegg - 7954\n    [:up_team_name] => Sharron Clegg - 7954\n    [:first_name] => Sharron\n    [:up_first_name] => Sharron\n    [:last_name] => Clegg\n    [:up_last_name] => Clegg\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 15:39:56] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55388049199615\n    [:subscriptionReferenceNumber] => 561-82001311\n    [:up_subscriptionReferenceNumber] => 561-82001311\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-03-31\n    [:up_startDate] => 2019-03-31\n    [:endDate] => 2026-09-06\n    [:up_endDate] => 2026-09-06\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276589\n    [:up_endCustomer_id] => 276589\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 5793\n    [:up_soldTo_id] => 5793\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n)\n
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => FENCO ALDRIDGE\n    [:up_name] => FENCO ALDRIDGE\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Willen Works Willen Road\n    [:up_address1] => Willen Works Willen Road\n    [:city] => Newport Pagnell\n    [:up_city] => Newport Pagnell\n    [:state_province] => BUCKINGHAMSHIRE\n    [:up_state_province] => BUCKINGHAMSHIRE\n    [:postal_code] => MK16 0DG\n    [:up_postal_code] => MK16 0DG\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Oil & Gas\n    [:up_parent_industry_segment] => Oil & Gas\n    [:primary_admin_first_name] => Sharron\n    [:up_primary_admin_first_name] => Sharron\n    [:primary_admin_last_name] => Clegg\n    [:up_primary_admin_last_name] => Clegg\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 6917954\n    [:up_team_id] => 6917954\n    [:team_name] => Sharron Clegg - 7954\n    [:up_team_name] => Sharron Clegg - 7954\n    [:first_name] => Sharron\n    [:up_first_name] => Sharron\n    [:last_name] => Clegg\n    [:up_last_name] => Clegg\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 15:40:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55388049199615\n    [:subscriptionReferenceNumber] => 561-82001311\n    [:up_subscriptionReferenceNumber] => 561-82001311\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-03-31\n    [:up_startDate] => 2019-03-31\n    [:endDate] => 2026-09-06\n    [:up_endDate] => 2026-09-06\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276592\n    [:up_endCustomer_id] => 276592\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 5793\n    [:up_soldTo_id] => 5793\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n)\n
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => 34-36 Rose Street North Lane\n    [:up_address1] => 34-36 Rose Street North Lane\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH2 2NP\n    [:up_postal_code] => EH2 2NP\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Engineering Service Providers\n    [:up_parent_industry_segment] => Engineering Service Providers\n    [:primary_admin_first_name] => C\n    [:up_primary_admin_first_name] => C\n    [:primary_admin_last_name] => Sutherland\n    [:up_primary_admin_last_name] => Sutherland\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 7073614\n    [:up_team_id] => 7073614\n    [:team_name] => C Sutherland - 3614\n    [:up_team_name] => C Sutherland - 3614\n    [:first_name] => C\n    [:up_first_name] => C\n    [:last_name] => Sutherland\n    [:up_last_name] => Sutherland\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => 1\n    [:up_do_not_email] => 1\n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 15:41:54] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55536230799844\n    [:subscriptionReferenceNumber] => 564-79369855\n    [:up_subscriptionReferenceNumber] => 564-79369855\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-04-17\n    [:up_startDate] => 2019-04-17\n    [:endDate] => 2026-09-16\n    [:up_endDate] => 2026-09-16\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276595\n    [:up_endCustomer_id] => 276595\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 1917\n    [:up_soldTo_id] => 1917\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n)\n
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => 34-36 Rose Street North Lane\n    [:up_address1] => 34-36 Rose Street North Lane\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH2 2NP\n    [:up_postal_code] => EH2 2NP\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Engineering Service Providers\n    [:up_parent_industry_segment] => Engineering Service Providers\n    [:primary_admin_first_name] => C\n    [:up_primary_admin_first_name] => C\n    [:primary_admin_last_name] => Sutherland\n    [:up_primary_admin_last_name] => Sutherland\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 7073614\n    [:up_team_id] => 7073614\n    [:team_name] => C Sutherland - 3614\n    [:up_team_name] => C Sutherland - 3614\n    [:first_name] => C\n    [:up_first_name] => C\n    [:last_name] => Sutherland\n    [:up_last_name] => Sutherland\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => 1\n    [:up_do_not_email] => 1\n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 15:41:55] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55536230799844\n    [:subscriptionReferenceNumber] => 564-79369855\n    [:up_subscriptionReferenceNumber] => 564-79369855\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-04-17\n    [:up_startDate] => 2019-04-17\n    [:endDate] => 2026-09-16\n    [:up_endDate] => 2026-09-16\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276598\n    [:up_endCustomer_id] => 276598\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 1917\n    [:up_soldTo_id] => 1917\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => 34-36 Rose Street North Lane\n    [:up_address1] => 34-36 Rose Street North Lane\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH2 2NP\n    [:up_postal_code] => EH2 2NP\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Engineering Service Providers\n    [:up_parent_industry_segment] => Engineering Service Providers\n    [:primary_admin_first_name] => C\n    [:up_primary_admin_first_name] => C\n    [:primary_admin_last_name] => Sutherland\n    [:up_primary_admin_last_name] => Sutherland\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 7073614\n    [:up_team_id] => 7073614\n    [:team_name] => C Sutherland - 3614\n    [:up_team_name] => C Sutherland - 3614\n    [:first_name] => C\n    [:up_first_name] => C\n    [:last_name] => Sutherland\n    [:up_last_name] => Sutherland\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => 1\n    [:up_do_not_email] => 1\n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55536230799844\n    [:subscriptionReferenceNumber] => 564-79369855\n    [:up_subscriptionReferenceNumber] => 564-79369855\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-04-17\n    [:up_startDate] => 2019-04-17\n    [:endDate] => 2026-09-16\n    [:up_endDate] => 2026-09-16\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276601\n    [:up_endCustomer_id] => 276601\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 1917\n    [:up_soldTo_id] => 1917\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => Surveying Solutions Ltd\n    [:up_name] => Surveying Solutions Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => 34-36 Rose Street North Lane\n    [:up_address1] => 34-36 Rose Street North Lane\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH2 2NP\n    [:up_postal_code] => EH2 2NP\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Engineering Service Providers\n    [:up_parent_industry_segment] => Engineering Service Providers\n    [:primary_admin_first_name] => C\n    [:up_primary_admin_first_name] => C\n    [:primary_admin_last_name] => Sutherland\n    [:up_primary_admin_last_name] => Sutherland\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 7073614\n    [:up_team_id] => 7073614\n    [:team_name] => C Sutherland - 3614\n    [:up_team_name] => C Sutherland - 3614\n    [:first_name] => C\n    [:up_first_name] => C\n    [:last_name] => Sutherland\n    [:up_last_name] => Sutherland\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => 1\n    [:up_do_not_email] => 1\n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-09-04 19:36:02] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55536230799844\n    [:subscriptionReferenceNumber] => 564-79369855\n    [:up_subscriptionReferenceNumber] => 564-79369855\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-04-17\n    [:up_startDate] => 2019-04-17\n    [:endDate] => 2026-09-16\n    [:up_endDate] => 2026-09-16\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 276604\n    [:up_endCustomer_id] => 276604\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 1917\n    [:up_soldTo_id] => 1917\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => ETCH ASSOCIATES\n    [:up_name] => ETCH ASSOCIATES\n    [:address1] => 1 Union Way\n    [:up_address1] => 1 Union Way\n    [:city] => Witney\n    [:up_city] => Witney\n    [:postal_code] => OX28 6HD\n    [:up_postal_code] => OX28 6HD\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => OXFORDSHIRE\n    [:up_state_province] => OXFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Etch\n    [:up_first_name] => Etch\n    [:last_name] => Associates\n    [:up_last_name] => Associates\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1064179\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-05T11:07:17+01:00\n    [:up_quote_created_time] => 2025-09-05T11:07:17+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 5\n    [:up_end_customer] => 5\n    [:quote_contact] => 15722\n    [:up_quote_contact] => 15722\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => ETCH ASSOCIATES\n    [:up_name] => ETCH ASSOCIATES\n    [:address1] => 1 Union Way\n    [:up_address1] => 1 Union Way\n    [:city] => Witney\n    [:up_city] => Witney\n    [:postal_code] => OX28 6HD\n    [:up_postal_code] => OX28 6HD\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => OXFORDSHIRE\n    [:up_state_province] => OXFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Etch\n    [:up_first_name] => Etch\n    [:last_name] => Associates\n    [:up_last_name] => Associates\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1064179\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-09-05T11:07:17+01:00\n    [:up_quote_created_time] => 2025-09-05T11:07:17+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 5\n    [:up_end_customer] => 5\n    [:quote_contact] => 15722\n    [:up_quote_contact] => 15722\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-09-05 10:07:23] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1381] import_csv_to_hilt_table called with table: autobooks_import_sketchup_data, is_file_path: true
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1393] Using file path: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1757079227.csv
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1397] Calling import_csv_with_auto_schema for table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1432] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1757079227.csv
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1757079227.csv
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1438] CSV analysis completed successfully
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1441] Generating enhanced schema for table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1447] Schema generation completed successfully
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1299] Successfully created table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1757079227.csv
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1520] Starting typed column import for table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1557] Typed column import completed: 77 success, 0 failed
[data_importer] [2025-09-05 13:33:48] [data_importer.class.php:1475] Generating and storing table configuration for: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1670] ATTEMPTING TO CREATE UNIFIED FIELD DATA SOURCE for table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1705] === CREATE_UNIFIED_FIELD_DATA_SOURCE CALLED ===
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1706] Table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1707] Schema keys: table_name, primary_key, columns, mapping
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1708] Analysis keys: success, headers, data_types, sample_data, total_rows, analyzed_rows
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1722] Extracted column names for unified field mapping: ["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"]
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1729] Generating field mapping suggestions for columns: ["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"]
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1734] Field mapping suggestions generated: {"sold_to_name":{"category":"sold_to_name","field_name":"sold_to_name","label":"Sold To Name","normalized_fields":["sold_to_name","sold_to"],"confidence":100,"contextual_score":90,"final_score":77,"priority":2,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":58,"final_score":63.4,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":58,"final_score":59.4,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]}]},"sold_to_number":{"category":"sold_to_number","field_name":"sold_to_number","label":"Sold To Number","normalized_fields":["sold_to_number","sold_to_id"],"confidence":100,"contextual_score":80,"final_score":67.33,"priority":8,"alternatives":[{"field_name":"sold_to_name","label":"Sold To Name","confidence":80,"contextual_score":74,"final_score":64.2,"priority":2,"normalized_fields":["sold_to_name","sold_to"]}]},"vendor_name":{"category":"vendor_name","field_name":"vendor_name","label":"Vendor Name","normalized_fields":["vendor_name"],"confidence":100,"contextual_score":80,"final_score":68.29,"priority":6,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":58,"final_score":63.4,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":58,"final_score":59.4,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]}]},"reseller_number":{"category":"reseller_name","field_name":"reseller_name","label":"Reseller Name","normalized_fields":["reseller_name","partner_name","account_primary_reseller_name"],"confidence":90,"contextual_score":66,"final_score":63.3,"priority":3,"alternatives":[]},"reseller_vendor_id":{"category":"vendor_id","field_name":"vendor_id","label":"Vendor Id","normalized_fields":["vendor_id"],"confidence":85,"contextual_score":78,"final_score":67.4,"priority":2,"alternatives":[{"field_name":"reseller_name","label":"Reseller Name","confidence":90,"contextual_score":66,"final_score":63.3,"priority":3,"normalized_fields":["reseller_name","partner_name","account_primary_reseller_name"]},{"field_name":"vendor_name","label":"Vendor Name","confidence":90,"contextual_score":62,"final_score":58.89,"priority":6,"normalized_fields":["vendor_name"]}]},"end_customer_vendor_id":{"category":"vendor_id","field_name":"vendor_id","label":"Vendor Id","normalized_fields":["vendor_id"],"confidence":85,"contextual_score":78,"final_score":67.4,"priority":2,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"vendor_name","label":"Vendor Name","confidence":90,"contextual_score":62,"final_score":58.89,"priority":6,"normalized_fields":["vendor_name"]}]},"end_customer_name":{"category":"company_name","field_name":"company_name","label":"Company Name","normalized_fields":["company_name","endcust_name","end_customer_name"],"confidence":100,"contextual_score":90,"final_score":77,"priority":2,"alternatives":[{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":58,"final_score":59.4,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]}]},"end_customer_address_1":{"category":"address","field_name":"address","label":"Address","normalized_fields":["address","end_customer_address_1"],"confidence":100,"contextual_score":88,"final_score":81.4,"priority":1,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_address_2":{"category":"end_customer_address_2","field_name":"end_customer_address_2","label":"Address Line 2","normalized_fields":["end_customer_address_2","address_2"],"confidence":100,"contextual_score":90,"final_score":70.33,"priority":8,"alternatives":[{"field_name":"address","label":"Address","confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"normalized_fields":["address","end_customer_address_1"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_address_3":{"category":"end_customer_address_3","field_name":"end_customer_address_3","label":"Address Line 3","normalized_fields":["end_customer_address_3","address_3"],"confidence":100,"contextual_score":90,"final_score":70.33,"priority":8,"alternatives":[{"field_name":"address","label":"Address","confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"normalized_fields":["address","end_customer_address_1"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_city":{"category":"city","field_name":"city","label":"City","normalized_fields":["city","end_customer_city"],"confidence":100,"contextual_score":80,"final_score":70,"priority":4,"alternatives":[{"field_name":"end_customer_city","label":"City","confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"normalized_fields":["end_customer_city","city"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_state":{"category":"state","field_name":"state","label":"State\/Province","normalized_fields":["state","end_customer_state"],"confidence":100,"contextual_score":80,"final_score":70,"priority":4,"alternatives":[{"field_name":"end_customer_state","label":"State\/Province","confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"normalized_fields":["end_customer_state","state"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_zip_code":{"category":"postal_code","field_name":"postal_code","label":"Postal Code","normalized_fields":["postal_code","end_customer_zip_code"],"confidence":100,"contextual_score":86,"final_score":71.8,"priority":4,"alternatives":[{"field_name":"end_customer_zip_code","label":"ZIP\/Postal Code","confidence":100,"contextual_score":90,"final_score":70.75,"priority":7,"normalized_fields":["end_customer_zip_code","zip_code","postal_code"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_country":{"category":"country","field_name":"country","label":"Country","normalized_fields":["country","end_customer_country"],"confidence":100,"contextual_score":80,"final_score":70,"priority":4,"alternatives":[{"field_name":"end_customer_country","label":"Country","confidence":100,"contextual_score":80,"final_score":68.29,"priority":6,"normalized_fields":["end_customer_country","country"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"quantity","label":"Quantity","confidence":56,"contextual_score":60,"final_score":44.69,"priority":6,"normalized_fields":["quantity","subs_quantity","subscription_quantity"]}]},"end_customer_account_type":{"category":"end_customer_account_type","field_name":"end_customer_account_type","label":"Account Type","normalized_fields":["end_customer_account_type","account_type"],"confidence":100,"contextual_score":90,"final_score":70.75,"priority":7,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"quantity","label":"Quantity","confidence":55,"contextual_score":60,"final_score":44.29,"priority":6,"normalized_fields":["quantity","subs_quantity","subscription_quantity"]}]},"end_customer_contact_name":{"category":"contact_name","field_name":"contact_name","label":"Contact Name","normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"],"confidence":100,"contextual_score":100,"final_score":76,"priority":4,"alternatives":[{"field_name":"end_customer_contact_name","label":"Contact Name","confidence":100,"contextual_score":100,"final_score":75,"priority":5,"normalized_fields":["end_customer_contact_name","contact_name","subscription_contact_name"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"end_customer_contact_email":{"category":"email","field_name":"email","label":"Email Address","normalized_fields":["email_address","endcust_primary_admin_email","end_customer_contact_email","subscription_contact_email"],"confidence":100,"contextual_score":90,"final_score":82,"priority":1,"alternatives":[{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":80,"final_score":66,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"end_customer_contact_name","label":"Contact Name","confidence":65,"contextual_score":80,"final_score":55,"priority":5,"normalized_fields":["end_customer_contact_name","contact_name","subscription_contact_name"]}]},"end_customer_contact_phone":{"category":"end_customer_contact_phone","field_name":"end_customer_contact_phone","label":"Contact Phone","normalized_fields":["end_customer_contact_phone","contact_phone","phone"],"confidence":100,"contextual_score":90,"final_score":71.29,"priority":6,"alternatives":[{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":80,"final_score":66,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"end_customer_contact_name","label":"Contact Name","confidence":65,"contextual_score":80,"final_score":55,"priority":5,"normalized_fields":["end_customer_contact_name","contact_name","subscription_contact_name"]}]},"end_customer_industry_segment":{"category":"end_customer_industry_segment","field_name":"end_customer_industry_segment","label":"Industry Segment","normalized_fields":["end_customer_industry_segment","industry"],"confidence":100,"contextual_score":90,"final_score":70.33,"priority":8,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":66,"final_score":65.8,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"product_market_segment","label":"Product Market Segment","confidence":90,"contextual_score":64,"final_score":58.53,"priority":8,"normalized_fields":["product_market_segment","market_segment"]}]},"agreement_program_name":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":100,"contextual_score":80,"final_score":79,"priority":1,"alternatives":[{"field_name":"agreement_program_name","label":"Agreement Program Name","confidence":100,"contextual_score":90,"final_score":70.75,"priority":7,"normalized_fields":["agreement_program_name","program_name"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":58,"final_score":63.4,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":58,"final_score":59.4,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]}]},"agreement_number":{"category":"subscription_reference","field_name":"subscription_reference","label":"Subscription Reference","normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"],"confidence":100,"contextual_score":80,"final_score":94,"priority":0,"alternatives":[]},"agreement_terms":{"category":"agreement_terms","field_name":"agreement_terms","label":"Agreement Terms","normalized_fields":["agreement_terms","terms"],"confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"alternatives":[]},"agreement_type":{"category":"agreement_type","field_name":"agreement_type","label":"Agreement Type","normalized_fields":["agreement_type","contract_type"],"confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"alternatives":[]},"agreement_status":{"category":"agreement_status","field_name":"agreement_status","label":"Agreement Status","normalized_fields":["agreement_status","contract_status"],"confidence":100,"contextual_score":80,"final_score":68.29,"priority":6,"alternatives":[{"field_name":"status","label":"Status","confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"normalized_fields":["status","subs_status","subscription_status"]}]},"agreement_support_level":{"category":"agreement_support_level","field_name":"agreement_support_level","label":"Agreement Support Level","normalized_fields":["agreement_support_level","support_level"],"confidence":100,"contextual_score":90,"final_score":70.33,"priority":8,"alternatives":[{"field_name":"subscription_level","label":"Subscription Level","confidence":90,"contextual_score":60,"final_score":57.75,"priority":7,"normalized_fields":["subscription_level","service_level"]}]},"product_name":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":100,"contextual_score":80,"final_score":79,"priority":1,"alternatives":[{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":58,"final_score":63.4,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]},{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":58,"final_score":59.4,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]}]},"product_family":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":100,"contextual_score":80,"final_score":79,"priority":1,"alternatives":[{"field_name":"product_family","label":"Product Family","confidence":100,"contextual_score":80,"final_score":68.29,"priority":6,"normalized_fields":["product_family","product_category"]}]},"product_market_segment":{"category":"product_market_segment","field_name":"product_market_segment","label":"Product Market Segment","normalized_fields":["product_market_segment","market_segment"],"confidence":100,"contextual_score":90,"final_score":70.33,"priority":8,"alternatives":[{"field_name":"product_name","label":"Product Name","confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]}]},"product_release":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"alternatives":[]},"product_type":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"alternatives":[{"field_name":"product_type","label":"Product Type","confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"normalized_fields":["product_type","license_type"]}]},"product_deployment":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"alternatives":[{"field_name":"product_deployment","label":"Product Deployment","confidence":100,"contextual_score":80,"final_score":67.33,"priority":8,"normalized_fields":["product_deployment","deployment"]}]},"product_sku":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"alternatives":[{"field_name":"product_sku","label":"Product SKU","confidence":100,"contextual_score":80,"final_score":69,"priority":5,"normalized_fields":["product_sku","sku"]},{"field_name":"product_sku_description","label":"Product SKU Description","confidence":59,"contextual_score":50,"final_score":41.93,"priority":8,"normalized_fields":["product_sku_description","product_description"]}]},"product_sku_description":{"category":"product_sku_description","field_name":"product_sku_description","label":"Product SKU Description","normalized_fields":["product_sku_description","product_description"],"confidence":100,"contextual_score":90,"final_score":70.33,"priority":8,"alternatives":[{"field_name":"product_name","label":"Product Name","confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},{"field_name":"product_sku","label":"Product SKU","confidence":90,"contextual_score":80,"final_score":65,"priority":5,"normalized_fields":["product_sku","sku"]}]},"product_part":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"alternatives":[{"field_name":"product_part","label":"Product Part","confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"normalized_fields":["product_part","part_number"]}]},"product_list_price":{"category":"product_list_price","field_name":"product_list_price","label":"Product List Price","normalized_fields":["product_list_price","list_price","price"],"confidence":100,"contextual_score":90,"final_score":71.29,"priority":6,"alternatives":[{"field_name":"product_name","label":"Product Name","confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]}]},"product_list_price_currency":{"category":"product_name","field_name":"product_name","label":"Product Name","normalized_fields":["product_name","subs_offeringName","agreement_program_name"],"confidence":90,"contextual_score":64,"final_score":70.2,"priority":1,"alternatives":[{"field_name":"product_list_price","label":"Product List Price","confidence":90,"contextual_score":90,"final_score":67.29,"priority":6,"normalized_fields":["product_list_price","list_price","price"]}]},"subscription_id":{"category":"subscription_reference","field_name":"subscription_reference","label":"Subscription Reference","normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"],"confidence":100,"contextual_score":80,"final_score":94,"priority":0,"alternatives":[]},"subscription_serial_number":{"category":"subscription_reference","field_name":"subscription_reference","label":"Subscription Reference","normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"],"confidence":85,"contextual_score":80,"final_score":88,"priority":0,"alternatives":[{"field_name":"subscription_serial_number","label":"Subscription Serial Number","confidence":100,"contextual_score":100,"final_score":76,"priority":4,"normalized_fields":["subscription_serial_number","serial_number"]}]},"subscription_status":{"category":"status","field_name":"status","label":"Status","normalized_fields":["status","subs_status","subscription_status"],"confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"alternatives":[]},"subscription_quantity":{"category":"quantity","field_name":"quantity","label":"Quantity","normalized_fields":["quantity","subs_quantity","subscription_quantity"],"confidence":100,"contextual_score":80,"final_score":68.29,"priority":6,"alternatives":[{"field_name":"subscription_quantity","label":"Subscription Quantity","confidence":100,"contextual_score":80,"final_score":68.29,"priority":6,"normalized_fields":["subscription_quantity","quantity"]}]},"subscription_contact_name":{"category":"contact_name","field_name":"contact_name","label":"Contact Name","normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"],"confidence":100,"contextual_score":90,"final_score":73,"priority":4,"alternatives":[{"field_name":"end_customer_contact_name","label":"Contact Name","confidence":100,"contextual_score":90,"final_score":72,"priority":5,"normalized_fields":["end_customer_contact_name","contact_name","subscription_contact_name"]},{"field_name":"company_name","label":"Company Name","confidence":90,"contextual_score":58,"final_score":63.4,"priority":2,"normalized_fields":["company_name","endcust_name","end_customer_name"]}]},"subscription_contact_email":{"category":"email","field_name":"email","label":"Email Address","normalized_fields":["email_address","endcust_primary_admin_email","end_customer_contact_email","subscription_contact_email"],"confidence":100,"contextual_score":90,"final_score":82,"priority":1,"alternatives":[{"field_name":"contact_name","label":"Contact Name","confidence":90,"contextual_score":64,"final_score":61.2,"priority":4,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]}]},"subscription_level":{"category":"subscription_level","field_name":"subscription_level","label":"Subscription Level","normalized_fields":["subscription_level","service_level"],"confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"alternatives":[]},"quotation_id":{"category":"quotation_id","field_name":"quotation_id","label":"Quotation ID","normalized_fields":["quotation_id","quote_id"],"confidence":100,"contextual_score":80,"final_score":69,"priority":5,"alternatives":[]},"quotation_type":{"category":"quotation_type","field_name":"quotation_type","label":"Quotation Type","normalized_fields":["quotation_type","quote_type"],"confidence":100,"contextual_score":80,"final_score":67.33,"priority":8,"alternatives":[]},"quotation_vendor_id":{"category":"vendor_id","field_name":"vendor_id","label":"Vendor Id","normalized_fields":["vendor_id"],"confidence":85,"contextual_score":78,"final_score":67.4,"priority":2,"alternatives":[{"field_name":"vendor_name","label":"Vendor Name","confidence":90,"contextual_score":62,"final_score":58.89,"priority":6,"normalized_fields":["vendor_name"]}]},"quotation_status":{"category":"quotation_status","field_name":"quotation_status","label":"Quotation Status","normalized_fields":["quotation_status","quote_status"],"confidence":100,"contextual_score":80,"final_score":67.75,"priority":7,"alternatives":[{"field_name":"status","label":"Status","confidence":90,"contextual_score":62,"final_score":58.35,"priority":7,"normalized_fields":["status","subs_status","subscription_status"]}]},"flaer_phase":{"category":"flaer_phase","field_name":"flaer_phase","label":"FLAER Phase","normalized_fields":["flaer_phase","phase"],"confidence":100,"contextual_score":80,"final_score":67.33,"priority":8,"alternatives":[]}}
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1767] Built selected_columns configuration: {"autobooks_import_sketchup_data":["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"]}
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1768] Built table_aliases configuration: {"autobooks_import_sketchup_data":"sketchup"}
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1787] Creating data source with config: {"name":"CSV Import: Sketchup","table_name":"autobooks_import_sketchup_data","description":"Auto-generated data source for CSV import (77 rows, 59 columns)","category":"csv_import","data_source_type":"standard","tables":["autobooks_import_sketchup_data"],"table_aliases":{"autobooks_import_sketchup_data":"sketchup"},"selected_columns":{"autobooks_import_sketchup_data":["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"]},"status":"active","mapping_method":"unified_field_mapper","resolved_tables":["autobooks_import_sketchup_data"],"unified_mappings":{"min_confidence":75,"applied":{"sold_to_name":{"category":"sold_to_name","field_name":"sold_to_name","confidence":100,"final_score":77,"normalized_fields":["sold_to_name","sold_to"]},"sold_to_number":{"category":"sold_to_number","field_name":"sold_to_number","confidence":100,"final_score":67.33,"normalized_fields":["sold_to_number","sold_to_id"]},"vendor_name":{"category":"vendor_name","field_name":"vendor_name","confidence":100,"final_score":68.29,"normalized_fields":["vendor_name"]},"reseller_number":{"category":"reseller_name","field_name":"reseller_name","confidence":90,"final_score":63.3,"normalized_fields":["reseller_name","partner_name","account_primary_reseller_name"]},"reseller_vendor_id":{"category":"vendor_id","field_name":"vendor_id","confidence":85,"final_score":67.4,"normalized_fields":["vendor_id"]},"end_customer_vendor_id":{"category":"vendor_id","field_name":"vendor_id","confidence":85,"final_score":67.4,"normalized_fields":["vendor_id"]},"end_customer_name":{"category":"company_name","field_name":"company_name","confidence":100,"final_score":77,"normalized_fields":["company_name","endcust_name","end_customer_name"]},"end_customer_address_1":{"category":"address","field_name":"address","confidence":100,"final_score":81.4,"normalized_fields":["address","end_customer_address_1"]},"end_customer_address_2":{"category":"end_customer_address_2","field_name":"end_customer_address_2","confidence":100,"final_score":70.33,"normalized_fields":["end_customer_address_2","address_2"]},"end_customer_address_3":{"category":"end_customer_address_3","field_name":"end_customer_address_3","confidence":100,"final_score":70.33,"normalized_fields":["end_customer_address_3","address_3"]},"end_customer_city":{"category":"city","field_name":"city","confidence":100,"final_score":70,"normalized_fields":["city","end_customer_city"]},"end_customer_state":{"category":"state","field_name":"state","confidence":100,"final_score":70,"normalized_fields":["state","end_customer_state"]},"end_customer_zip_code":{"category":"postal_code","field_name":"postal_code","confidence":100,"final_score":71.8,"normalized_fields":["postal_code","end_customer_zip_code"]},"end_customer_country":{"category":"country","field_name":"country","confidence":100,"final_score":70,"normalized_fields":["country","end_customer_country"]},"end_customer_account_type":{"category":"end_customer_account_type","field_name":"end_customer_account_type","confidence":100,"final_score":70.75,"normalized_fields":["end_customer_account_type","account_type"]},"end_customer_contact_name":{"category":"contact_name","field_name":"contact_name","confidence":100,"final_score":76,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]},"end_customer_contact_email":{"category":"email","field_name":"email","confidence":100,"final_score":82,"normalized_fields":["email_address","endcust_primary_admin_email","end_customer_contact_email","subscription_contact_email"]},"end_customer_contact_phone":{"category":"end_customer_contact_phone","field_name":"end_customer_contact_phone","confidence":100,"final_score":71.29,"normalized_fields":["end_customer_contact_phone","contact_phone","phone"]},"end_customer_industry_segment":{"category":"end_customer_industry_segment","field_name":"end_customer_industry_segment","confidence":100,"final_score":70.33,"normalized_fields":["end_customer_industry_segment","industry"]},"agreement_program_name":{"category":"product_name","field_name":"product_name","confidence":100,"final_score":79,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"agreement_number":{"category":"subscription_reference","field_name":"subscription_reference","confidence":100,"final_score":94,"normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"]},"agreement_terms":{"category":"agreement_terms","field_name":"agreement_terms","confidence":100,"final_score":67.75,"normalized_fields":["agreement_terms","terms"]},"agreement_type":{"category":"agreement_type","field_name":"agreement_type","confidence":100,"final_score":67.75,"normalized_fields":["agreement_type","contract_type"]},"agreement_status":{"category":"agreement_status","field_name":"agreement_status","confidence":100,"final_score":68.29,"normalized_fields":["agreement_status","contract_status"]},"agreement_support_level":{"category":"agreement_support_level","field_name":"agreement_support_level","confidence":100,"final_score":70.33,"normalized_fields":["agreement_support_level","support_level"]},"product_name":{"category":"product_name","field_name":"product_name","confidence":100,"final_score":79,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_family":{"category":"product_name","field_name":"product_name","confidence":100,"final_score":79,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_market_segment":{"category":"product_market_segment","field_name":"product_market_segment","confidence":100,"final_score":70.33,"normalized_fields":["product_market_segment","market_segment"]},"product_release":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_type":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_deployment":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_sku":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_sku_description":{"category":"product_sku_description","field_name":"product_sku_description","confidence":100,"final_score":70.33,"normalized_fields":["product_sku_description","product_description"]},"product_part":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_list_price":{"category":"product_list_price","field_name":"product_list_price","confidence":100,"final_score":71.29,"normalized_fields":["product_list_price","list_price","price"]},"product_list_price_currency":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"subscription_id":{"category":"subscription_reference","field_name":"subscription_reference","confidence":100,"final_score":94,"normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"]},"subscription_serial_number":{"category":"subscription_reference","field_name":"subscription_reference","confidence":85,"final_score":88,"normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"]},"subscription_status":{"category":"status","field_name":"status","confidence":100,"final_score":67.75,"normalized_fields":["status","subs_status","subscription_status"]},"subscription_quantity":{"category":"quantity","field_name":"quantity","confidence":100,"final_score":68.29,"normalized_fields":["quantity","subs_quantity","subscription_quantity"]},"subscription_contact_name":{"category":"contact_name","field_name":"contact_name","confidence":100,"final_score":73,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]},"subscription_contact_email":{"category":"email","field_name":"email","confidence":100,"final_score":82,"normalized_fields":["email_address","endcust_primary_admin_email","end_customer_contact_email","subscription_contact_email"]},"subscription_level":{"category":"subscription_level","field_name":"subscription_level","confidence":100,"final_score":67.75,"normalized_fields":["subscription_level","service_level"]},"quotation_id":{"category":"quotation_id","field_name":"quotation_id","confidence":100,"final_score":69,"normalized_fields":["quotation_id","quote_id"]},"quotation_type":{"category":"quotation_type","field_name":"quotation_type","confidence":100,"final_score":67.33,"normalized_fields":["quotation_type","quote_type"]},"quotation_vendor_id":{"category":"vendor_id","field_name":"vendor_id","confidence":85,"final_score":67.4,"normalized_fields":["vendor_id"]},"quotation_status":{"category":"quotation_status","field_name":"quotation_status","confidence":100,"final_score":67.75,"normalized_fields":["quotation_status","quote_status"]},"flaer_phase":{"category":"flaer_phase","field_name":"flaer_phase","confidence":100,"final_score":67.33,"normalized_fields":["flaer_phase","phase"]}},"overrides":[]}}
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1792] Successfully created unified field data source ID: 80 for table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1819] Updating data table storage entries for autobooks_import_sketchup_data with data_source_id: 80
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1835] Updated data table storage entries for autobooks_import_sketchup_data. Total entries for this table: 1
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1672] Data source creation result: {"success":true,"data_source_id":80,"unified_mappings":{"min_confidence":75,"applied":{"sold_to_name":{"category":"sold_to_name","field_name":"sold_to_name","confidence":100,"final_score":77,"normalized_fields":["sold_to_name","sold_to"]},"sold_to_number":{"category":"sold_to_number","field_name":"sold_to_number","confidence":100,"final_score":67.33,"normalized_fields":["sold_to_number","sold_to_id"]},"vendor_name":{"category":"vendor_name","field_name":"vendor_name","confidence":100,"final_score":68.29,"normalized_fields":["vendor_name"]},"reseller_number":{"category":"reseller_name","field_name":"reseller_name","confidence":90,"final_score":63.3,"normalized_fields":["reseller_name","partner_name","account_primary_reseller_name"]},"reseller_vendor_id":{"category":"vendor_id","field_name":"vendor_id","confidence":85,"final_score":67.4,"normalized_fields":["vendor_id"]},"end_customer_vendor_id":{"category":"vendor_id","field_name":"vendor_id","confidence":85,"final_score":67.4,"normalized_fields":["vendor_id"]},"end_customer_name":{"category":"company_name","field_name":"company_name","confidence":100,"final_score":77,"normalized_fields":["company_name","endcust_name","end_customer_name"]},"end_customer_address_1":{"category":"address","field_name":"address","confidence":100,"final_score":81.4,"normalized_fields":["address","end_customer_address_1"]},"end_customer_address_2":{"category":"end_customer_address_2","field_name":"end_customer_address_2","confidence":100,"final_score":70.33,"normalized_fields":["end_customer_address_2","address_2"]},"end_customer_address_3":{"category":"end_customer_address_3","field_name":"end_customer_address_3","confidence":100,"final_score":70.33,"normalized_fields":["end_customer_address_3","address_3"]},"end_customer_city":{"category":"city","field_name":"city","confidence":100,"final_score":70,"normalized_fields":["city","end_customer_city"]},"end_customer_state":{"category":"state","field_name":"state","confidence":100,"final_score":70,"normalized_fields":["state","end_customer_state"]},"end_customer_zip_code":{"category":"postal_code","field_name":"postal_code","confidence":100,"final_score":71.8,"normalized_fields":["postal_code","end_customer_zip_code"]},"end_customer_country":{"category":"country","field_name":"country","confidence":100,"final_score":70,"normalized_fields":["country","end_customer_country"]},"end_customer_account_type":{"category":"end_customer_account_type","field_name":"end_customer_account_type","confidence":100,"final_score":70.75,"normalized_fields":["end_customer_account_type","account_type"]},"end_customer_contact_name":{"category":"contact_name","field_name":"contact_name","confidence":100,"final_score":76,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]},"end_customer_contact_email":{"category":"email","field_name":"email","confidence":100,"final_score":82,"normalized_fields":["email_address","endcust_primary_admin_email","end_customer_contact_email","subscription_contact_email"]},"end_customer_contact_phone":{"category":"end_customer_contact_phone","field_name":"end_customer_contact_phone","confidence":100,"final_score":71.29,"normalized_fields":["end_customer_contact_phone","contact_phone","phone"]},"end_customer_industry_segment":{"category":"end_customer_industry_segment","field_name":"end_customer_industry_segment","confidence":100,"final_score":70.33,"normalized_fields":["end_customer_industry_segment","industry"]},"agreement_program_name":{"category":"product_name","field_name":"product_name","confidence":100,"final_score":79,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"agreement_number":{"category":"subscription_reference","field_name":"subscription_reference","confidence":100,"final_score":94,"normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"]},"agreement_terms":{"category":"agreement_terms","field_name":"agreement_terms","confidence":100,"final_score":67.75,"normalized_fields":["agreement_terms","terms"]},"agreement_type":{"category":"agreement_type","field_name":"agreement_type","confidence":100,"final_score":67.75,"normalized_fields":["agreement_type","contract_type"]},"agreement_status":{"category":"agreement_status","field_name":"agreement_status","confidence":100,"final_score":68.29,"normalized_fields":["agreement_status","contract_status"]},"agreement_support_level":{"category":"agreement_support_level","field_name":"agreement_support_level","confidence":100,"final_score":70.33,"normalized_fields":["agreement_support_level","support_level"]},"product_name":{"category":"product_name","field_name":"product_name","confidence":100,"final_score":79,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_family":{"category":"product_name","field_name":"product_name","confidence":100,"final_score":79,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_market_segment":{"category":"product_market_segment","field_name":"product_market_segment","confidence":100,"final_score":70.33,"normalized_fields":["product_market_segment","market_segment"]},"product_release":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_type":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_deployment":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_sku":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_sku_description":{"category":"product_sku_description","field_name":"product_sku_description","confidence":100,"final_score":70.33,"normalized_fields":["product_sku_description","product_description"]},"product_part":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"product_list_price":{"category":"product_list_price","field_name":"product_list_price","confidence":100,"final_score":71.29,"normalized_fields":["product_list_price","list_price","price"]},"product_list_price_currency":{"category":"product_name","field_name":"product_name","confidence":90,"final_score":70.2,"normalized_fields":["product_name","subs_offeringName","agreement_program_name"]},"subscription_id":{"category":"subscription_reference","field_name":"subscription_reference","confidence":100,"final_score":94,"normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"]},"subscription_serial_number":{"category":"subscription_reference","field_name":"subscription_reference","confidence":85,"final_score":88,"normalized_fields":["subscription_reference","subs_subscriptionReferenceNumber","subscription_id"]},"subscription_status":{"category":"status","field_name":"status","confidence":100,"final_score":67.75,"normalized_fields":["status","subs_status","subscription_status"]},"subscription_quantity":{"category":"quantity","field_name":"quantity","confidence":100,"final_score":68.29,"normalized_fields":["quantity","subs_quantity","subscription_quantity"]},"subscription_contact_name":{"category":"contact_name","field_name":"contact_name","confidence":100,"final_score":73,"normalized_fields":["contact_name","end_customer_contact_name","subscription_contact_name"]},"subscription_contact_email":{"category":"email","field_name":"email","confidence":100,"final_score":82,"normalized_fields":["email_address","endcust_primary_admin_email","end_customer_contact_email","subscription_contact_email"]},"subscription_level":{"category":"subscription_level","field_name":"subscription_level","confidence":100,"final_score":67.75,"normalized_fields":["subscription_level","service_level"]},"quotation_id":{"category":"quotation_id","field_name":"quotation_id","confidence":100,"final_score":69,"normalized_fields":["quotation_id","quote_id"]},"quotation_type":{"category":"quotation_type","field_name":"quotation_type","confidence":100,"final_score":67.33,"normalized_fields":["quotation_type","quote_type"]},"quotation_vendor_id":{"category":"vendor_id","field_name":"vendor_id","confidence":85,"final_score":67.4,"normalized_fields":["vendor_id"]},"quotation_status":{"category":"quotation_status","field_name":"quotation_status","confidence":100,"final_score":67.75,"normalized_fields":["quotation_status","quote_status"]},"flaer_phase":{"category":"flaer_phase","field_name":"flaer_phase","confidence":100,"final_score":67.33,"normalized_fields":["flaer_phase","phase"]}},"overrides":[]},"suggestions_count":48,"applied_count":48}
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1678] Created data source ID: 80 for table: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1681] Successfully generated and stored table configuration for: autobooks_import_sketchup_data
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1477] Table configuration result: {"success":true,"message":"Table configuration generated and stored successfully","config":{"title":"Autobooks import sketchup data","description":"Auto-generated table from CSV import (77 rows, 77 analyzed)","items":[{"id":1,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SECUREBASE LTD","end_customer_address_1":"112 High Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"EAST FINCHLEY","end_customer_state":null,"end_customer_zip_code":"N2 9EB","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Ho Hek","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2009914,"agreement_start_date":"30\/05\/2024","agreement_end_date":"29\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":23,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2124836EU","subscription_serial_number":"ERFC2124836EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"30\/05\/2024","subscription_end_date":"29\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":23,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2009914,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"08\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":2,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"RUSSELL JONES LIMITED","end_customer_address_1":"5 Wembury Mews","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"N6 5XJ","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Russell Jones","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":1988749,"agreement_start_date":"03\/05\/2024","agreement_end_date":"02\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"EXPIRED","agreement_support_level":"Tier 2","agreement_days_due":-4,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2098644EU","subscription_serial_number":"ERFC2098644EU","subscription_status":"EXPIRED","subscription_quantity":1,"subscription_start_date":"03\/05\/2024","subscription_end_date":"02\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":-4,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":1988749,"quotation_deal_registration_number":null,"quotation_status":"Urgent Renewal","quotation_resellerpo_previous":null,"quotation_due_date":"12\/05\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":3,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"JOEL TRINDADE DESIGNS","end_customer_address_1":"2 Holford Way","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"SW15 5EY","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Joel Trindade","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2000131,"agreement_start_date":"18\/05\/2024","agreement_end_date":"17\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":11,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2111317EU","subscription_serial_number":"ERFC2111317EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"18\/05\/2024","subscription_end_date":"17\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":11,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2000131,"quotation_deal_registration_number":null,"quotation_status":"Urgent Renewal","quotation_resellerpo_previous":null,"quotation_due_date":"27\/05\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":4,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DANDELION SEEDS ARCHITECTS","end_customer_address_1":"2 Addison Grove","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"TAUNTON","end_customer_state":null,"end_customer_zip_code":"TA2 6JF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Phillip Bristow","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2003680,"agreement_start_date":"26\/05\/2024","agreement_end_date":"25\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":19,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2115556EU","subscription_serial_number":"ERFC2115556EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"26\/05\/2024","subscription_end_date":"25\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":19,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2003680,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":5,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MATT BARWELL","end_customer_address_1":"1 Corn Exchange","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CHICHESTER","end_customer_state":null,"end_customer_zip_code":"PO19 1BF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Matt Barwell","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2024470,"agreement_start_date":"29\/06\/2024","agreement_end_date":"28\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":53,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2145582EU","subscription_serial_number":"ERFC2145582EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"29\/06\/2024","subscription_end_date":"28\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":53,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2024470,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"08\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":6,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"PHILIP BINGHAM ASSOCIATES","end_customer_address_1":"14a Market Place","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"HORNSEA","end_customer_state":null,"end_customer_zip_code":"HU18 1AW","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Phil Bingham","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2015264,"agreement_start_date":"08\/06\/2024","agreement_end_date":"07\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":32,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2131814EU","subscription_serial_number":"ERFC2131814EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"08\/06\/2024","subscription_end_date":"07\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":32,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2015264,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"17\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":7,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"INNOSCAPE LTD","end_customer_address_1":"2 Denby Hill","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"KEIGHLEY","end_customer_state":null,"end_customer_zip_code":"BD22 7QB","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Ben Howarth","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2024472,"agreement_start_date":"19\/06\/2024","agreement_end_date":"18\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":43,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2145592EU","subscription_serial_number":"ERFC2145592EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"19\/06\/2024","subscription_end_date":"18\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":43,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2024472,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"28\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":8,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"CRAIG SHEPPARD","end_customer_address_1":"15 Frampton Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"HERTFORD","end_customer_state":null,"end_customer_zip_code":"SG14 1QG","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Craig Sheppard","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2026168,"agreement_start_date":"21\/06\/2024","agreement_end_date":"20\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":45,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2148470EU","subscription_serial_number":"ERFC2148470EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"21\/06\/2024","subscription_end_date":"20\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":45,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2026168,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"30\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":9,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DESIGNS TO BUILD ON LIMITED","end_customer_address_1":"15 The Hawthorns","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CHARVIL","end_customer_state":null,"end_customer_zip_code":"RG10 9TS","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Steven Ellam","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2021796,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2141345EU","subscription_serial_number":"ERFC2141345EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2021796,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":10,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"HOMES UNDER THE HANNAH","end_customer_address_1":"14 Roseland Avenue","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"MANCHESTER","end_customer_state":null,"end_customer_zip_code":"M20 3QY","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Hannah Bridge","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2022880,"agreement_start_date":"18\/06\/2024","agreement_end_date":"17\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":42,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2143323EU","subscription_serial_number":"ERFC2143323EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"18\/06\/2024","subscription_end_date":"17\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":42,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2022880,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"27\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":11,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SAPPHIRE SPACES","end_customer_address_1":"3 Dart Business Park","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"TOPSHAM","end_customer_state":null,"end_customer_zip_code":"EX3 0QH","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Mark Newbery","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2020066,"agreement_start_date":"13\/06\/2024","agreement_end_date":"12\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":37,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":602,"product_list_price_currency":"GBP","subscription_id":"ERFC2139201EU","subscription_serial_number":"ERFC2139201EU","subscription_status":"ACTIVE","subscription_quantity":2,"subscription_start_date":"13\/06\/2024","subscription_end_date":"12\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":37,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2020066,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"22\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":12,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MJCT LIMITED","end_customer_address_1":"Hardwick Business","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"BANBURY","end_customer_state":null,"end_customer_zip_code":"OX16 2AF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jon Courtney-Thompson","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2017299,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2134858EU","subscription_serial_number":"ERFC2134858EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2017299,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":13,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MJCT LIMITED","end_customer_address_1":"Hardwick Business","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"BANBURY","end_customer_state":null,"end_customer_zip_code":"OX16 2AF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jon Courtney-Thompson","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2017300,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2134857EU","subscription_serial_number":"ERFC2134857EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2017300,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":14,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MJCT LIMITED","end_customer_address_1":"Hardwick Business","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"BANBURY","end_customer_state":null,"end_customer_zip_code":"OX16 2AF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jon Courtney-Thompson","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2017947,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2135872EU","subscription_serial_number":"ERFC2135872EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2017947,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":15,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SENSUS ARCHITECTURE LTD","end_customer_address_1":"7 Main Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"KINGS LYNN","end_customer_state":null,"end_customer_zip_code":"PE31 8BB","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martin Stuart","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2009740,"agreement_start_date":"15\/06\/2024","agreement_end_date":"14\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":39,"agreement_autorenew":0,"product_name":"Sketchup Studio","product_family":"Sketchup Studio","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-STDO-YR-CNL-02","product_sku_description":"Our premium, Windows-only offering, annual termed contract, gives you everything you need to steer your projects ahead with confidence including a professional 3D modeler, design research insights, project management, point cloud data interoperability, re","product_part":null,"product_list_price":645,"product_list_price_currency":"GBP","subscription_id":"ERFC2124453EU","subscription_serial_number":"ERFC2124453EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"15\/06\/2024","subscription_end_date":"14\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":39,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2009740,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"24\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":16,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"17 - 19 Leicester Square","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"WC2H 7LE","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2027626,"agreement_start_date":"24\/06\/2024","agreement_end_date":"23\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":48,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":3612,"product_list_price_currency":"GBP","subscription_id":"ERFC2150610EU","subscription_serial_number":"ERFC2150610EU","subscription_status":"ACTIVE","subscription_quantity":12,"subscription_start_date":"24\/06\/2024","subscription_end_date":"23\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":48,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2027626,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"03\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":17,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"17 - 19 Leicester Square","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"WC2H 7LE","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028255,"agreement_start_date":"25\/06\/2024","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2151519EU","subscription_serial_number":"ERFC2151519EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"25\/06\/2024","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028255,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":18,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"17 - 19 Leicester Square","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"WC2H 7LE","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028277,"agreement_start_date":"25\/06\/2024","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2151549EU","subscription_serial_number":"ERFC2151549EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"25\/06\/2024","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028277,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":19,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028285,"agreement_start_date":"25\/06\/2024","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2151554EU","subscription_serial_number":"ERFC2151554EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"25\/06\/2024","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028285,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":20,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Ryan Holland","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2239553,"agreement_start_date":"06\/03\/2025","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2438089EU","subscription_serial_number":"ERFC2438089EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"06\/03\/2025","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2239553,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":21,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Soda Studio","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2215113,"agreement_start_date":"07\/02\/2025","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2403193EU","subscription_serial_number":"ERFC2403193EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"07\/02\/2025","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2215113,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":22,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Sukey Clark","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2241251,"agreement_start_date":"07\/03\/2025","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2440281EU","subscription_serial_number":"ERFC2440281EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"07\/03\/2025","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2241251,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":23,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"ACORN PARTITIONS & STORAGE SYSTEMS LIMITED","end_customer_address_1":"Kingsley Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LINCOLN","end_customer_state":null,"end_customer_zip_code":"LN6 3TA","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Stuart Wall","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2004996,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2117597EU","subscription_serial_number":"ERFC2117597EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2004996,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":24,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"ACORN PARTITIONS & STORAGE SYSTEMS LIMITED","end_customer_address_1":"Kingsley Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LINCOLN","end_customer_state":null,"end_customer_zip_code":"LN6 3TA","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Stuart Wall","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2005021,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":602,"product_list_price_currency":"GBP","subscription_id":"ERFC2117636EU","subscription_serial_number":"ERFC2117636EU","subscription_status":"ACTIVE","subscription_quantity":2,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2005021,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":25,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"ACORN PARTITIONS & STORAGE SYSTEMS LIMITED","end_customer_address_1":"Kingsley Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LINCOLN","end_customer_state":null,"end_customer_zip_code":"LN6 3TA","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Stuart Wall","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2005023,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2117635EU","subscription_serial_number":"ERFC2117635EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2005023,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":26,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DI ORO INTERIORS LTD","end_customer_address_1":"78 Whitchurch Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CARDIFF","end_customer_state":null,"end_customer_zip_code":"CF14 3LX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Esther Milardi","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2027632,"agreement_start_date":"24\/06\/2024","agreement_end_date":"23\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":48,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2150628EU","subscription_serial_number":"ERFC2150628EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"24\/06\/2024","subscription_end_date":"23\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":48,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2027632,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"03\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":27,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DLG ARCHITECTS LEEDS","end_customer_address_1":"One Brewery Wharf Waterloo Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LEEDS","end_customer_state":null,"end_customer_zip_code":"LS10 1GX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Allen Norris","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028194,"agreement_start_date":"30\/06\/2024","agreement_end_date":"29\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":54,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":2408,"product_list_price_currency":"GBP","subscription_id":"ERFC2151460EU","subscription_serial_number":"ERFC2151460EU","subscription_status":"ACTIVE","subscription_quantity":8,"subscription_start_date":"30\/06\/2024","subscription_end_date":"29\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":54,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028194,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"09\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":28,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DAY CUMMINS LIMITED","end_customer_address_1":"4A Lakeland Business Park","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"COCKERMOUTH","end_customer_state":null,"end_customer_zip_code":"CA13 9QZ","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jen Metcalf","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2009940,"agreement_start_date":"30\/05\/2024","agreement_end_date":"29\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":23,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2124860EU","subscription_serial_number":"ERFC2124860EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"30\/05\/2024","subscription_end_date":"29\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":23,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2009940,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"08\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":29,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"JAMES MACKINTOSH ARCHITECTS LIMITED","end_customer_address_1":"First Floor","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CHIPPING NORTON","end_customer_state":null,"end_customer_zip_code":"OX7 5AD","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"James Mackintosh","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2018948,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2137412EU","subscription_serial_number":"ERFC2137412EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2018948,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":30,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"TREESAURUS","end_customer_address_1":"39 Braxted Park","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"SW16 3DU","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Charles Mitchell","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2002386,"agreement_start_date":"21\/05\/2024","agreement_end_date":"20\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":14,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2113858EU","subscription_serial_number":"ERFC2113858EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"21\/05\/2024","subscription_end_date":"20\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":14,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2002386,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"30\/05\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"}],"columns":[{"label":"Sold To Name","fields":["sold_to_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"sold_to_name","confidence":100},{"label":"Vendor Name","fields":["vendor_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"vendor_name","confidence":100},{"label":"Company Name","fields":["end_customer_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"company_name","confidence":100},{"label":"End Customer Address 1","fields":["end_customer_address_1"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"address","confidence":100},{"label":"End Customer State","fields":["end_customer_state"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"state","confidence":100},{"label":"Uk Postcode","fields":["end_customer_zip_code"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"postal_code","confidence":100},{"label":"End Customer Country","fields":["end_customer_country"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"country","confidence":100},{"label":"End Customer Contact Name","fields":["end_customer_contact_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"contact_name","confidence":100}],"available_fields":["sold_to_number","reseller_vendor_id","end_customer_vendor_id","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_account_type","end_customer_contact_phone","end_customer_industry_segment","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_market_segment","product_sku_description","product_list_price","subscription_quantity","subscription_start_date","subscription_end_date","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","end_customer_contact_email","agreement_program_name","agreement_number","product_name","product_family","subscription_id","subscription_status","subscription_contact_name","subscription_contact_email","reseller_number","product_release","product_type","product_deployment","product_sku","product_part","product_list_price_currency","subscription_serial_number","id"],"rows":{"id_prefix":"row_","id_field":"id","class_postfix":"","extra_parameters":""},"just_body":false,"just_rows":false,"items_per_page":30,"current_page_num":1,"sort_column":"","sort_direction":"asc","callback":"","class":""},"route_key":"enhanced_autobooks_import_sketchup_data"}
[data_importer] [2025-09-05 13:33:49] [data_importer.class.php:1399] import_csv_with_auto_schema returned: {"success":true,"message":"CSV imported with auto-generated schema and stored configuration","table_name":"autobooks_import_sketchup_data","analysis":{"success":true,"headers":["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"],"data_types":{"sold_to_name":"string","sold_to_number":"integer","vendor_name":"string","reseller_number":"integer","reseller_vendor_id":"string","end_customer_vendor_id":"integer","end_customer_name":"string","end_customer_address_1":"string","end_customer_address_2":"string","end_customer_address_3":"string","end_customer_city":"string","end_customer_state":"string","end_customer_zip_code":"string","end_customer_country":"string","end_customer_account_type":"string","end_customer_contact_name":"string","end_customer_contact_email":"string","end_customer_contact_phone":"string","end_customer_industry_segment":"string","agreement_program_name":"string","agreement_number":"integer","agreement_start_date":"string","agreement_end_date":"string","agreement_terms":"string","agreement_type":"string","agreement_status":"string","agreement_support_level":"string","agreement_days_due":"integer","agreement_autorenew":"integer","product_name":"string","product_family":"string","product_market_segment":"string","product_release":"string","product_type":"string","product_deployment":"string","product_sku":"string","product_sku_description":"string","product_part":"string","product_list_price":"integer","product_list_price_currency":"string","subscription_id":"string","subscription_serial_number":"string","subscription_status":"string","subscription_quantity":"integer","subscription_start_date":"string","subscription_end_date":"string","subscription_contact_name":"string","subscription_contact_email":"string","subscription_level":"string","subscription_days_due":"integer","quotation_id":"string","quotation_type":"string","quotation_vendor_id":"integer","quotation_deal_registration_number":"string","quotation_status":"string","quotation_resellerpo_previous":"string","quotation_due_date":"string","flaer_phase":"string","updated":"string"},"sample_data":{"sold_to_name":["TD SYNNEX United Kingdom","TD SYNNEX United Kingdom","TD SYNNEX United Kingdom","TD SYNNEX United Kingdom","TD SYNNEX United Kingdom"],"sold_to_number":["5070000697","5070000697","5070000697","5070000697","5070000697"],"vendor_name":["Trimble","Trimble","Trimble","Trimble","Trimble"],"reseller_number":["3E+14","3E+14","3E+14","3E+14","3E+14"],"reseller_vendor_id":["0014-317383","0014-317383","0014-317383","0014-317383","0014-317383"],"end_customer_vendor_id":["1.58816E+15","1.58834E+15","1.58981E+15","1.59049E+15","1.59128E+15"],"end_customer_name":["SECUREBASE LTD","RUSSELL JONES LIMITED","JOEL TRINDADE DESIGNS","DANDELION SEEDS ARCHITECTS","MATT BARWELL"],"end_customer_address_1":["112 High Road","5 Wembury Mews","2 Holford Way","2 Addison Grove","1 Corn Exchange"],"end_customer_address_2":["","","","",""],"end_customer_address_3":["","","","",""],"end_customer_city":["EAST FINCHLEY","LONDON","LONDON","TAUNTON","CHICHESTER"],"end_customer_state":["","","","",""],"end_customer_zip_code":["N2 9EB","N6 5XJ","SW15 5EY","TA2 6JF","PO19 1BF"],"end_customer_country":["United Kingdom","United Kingdom","United Kingdom","United Kingdom","United Kingdom"],"end_customer_account_type":["Commercial","Commercial","Commercial","Commercial","Commercial"],"end_customer_contact_name":["Ho Hek","Russell Jones","Joel Trindade","Phillip Bristow","Matt Barwell"],"end_customer_contact_email":["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],"end_customer_contact_phone":["","","","",""],"end_customer_industry_segment":["","","","",""],"agreement_program_name":["Trimble Subscriptions","Trimble Subscriptions","Trimble Subscriptions","Trimble Subscriptions","Trimble Subscriptions"],"agreement_number":["2009914","1988749","2000131","2003680","2024470"],"agreement_start_date":["30\/05\/2024","03\/05\/2024","18\/05\/2024","26\/05\/2024","29\/06\/2024"],"agreement_end_date":["29\/05\/2025","02\/05\/2025","17\/05\/2025","25\/05\/2025","28\/06\/2025"],"agreement_terms":["Annual","Annual","Annual","Annual","Annual"],"agreement_type":["Commercial","Commercial","Commercial","Commercial","Commercial"],"agreement_status":["ACTIVE","EXPIRED","ACTIVE","ACTIVE","ACTIVE"],"agreement_support_level":["Tier 2","Tier 2","Tier 2","Tier 2","Tier 2"],"agreement_days_due":["23","-4","11","19","53"],"agreement_autorenew":["0","0","0","0","0"],"product_name":["Sketchup Pro","Sketchup Pro","Sketchup Pro","Sketchup Pro","Sketchup Pro"],"product_family":["Sketchup Pro","Sketchup Pro","Sketchup Pro","Sketchup Pro","Sketchup Pro"],"product_market_segment":["","","","",""],"product_release":["","","","",""],"product_type":["Subscription","Subscription","Subscription","Subscription","Subscription"],"product_deployment":["S","S","S","S","S"],"product_sku":["SKP-PRO-YR-CNL","SKP-PRO-YR-CNL","SKP-PRO-YR-CNL","SKP-PRO-YR-CNL","SKP-PRO-YR-CNL"],"product_sku_description":["Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su"],"product_part":["","","","",""],"product_list_price":["301","301","301","301","301"],"product_list_price_currency":["GBP","GBP","GBP","GBP","GBP"],"subscription_id":["ERFC2124836EU","ERFC2098644EU","ERFC2111317EU","ERFC2115556EU","ERFC2145582EU"],"subscription_serial_number":["ERFC2124836EU","ERFC2098644EU","ERFC2111317EU","ERFC2115556EU","ERFC2145582EU"],"subscription_status":["ACTIVE","EXPIRED","ACTIVE","ACTIVE","ACTIVE"],"subscription_quantity":["1","1","1","1","1"],"subscription_start_date":["30\/05\/2024","03\/05\/2024","18\/05\/2024","26\/05\/2024","29\/06\/2024"],"subscription_end_date":["29\/05\/2025","02\/05\/2025","17\/05\/2025","25\/05\/2025","28\/06\/2025"],"subscription_contact_name":["","","","",""],"subscription_contact_email":["","","","",""],"subscription_level":["","","","",""],"subscription_days_due":["23","-4","11","19","53"],"quotation_id":["","","","",""],"quotation_type":["","","","",""],"quotation_vendor_id":["2009914","1988749","2000131","2003680","2024470"],"quotation_deal_registration_number":["","","","",""],"quotation_status":["","Urgent Renewal","Urgent Renewal","",""],"quotation_resellerpo_previous":["","","","",""],"quotation_due_date":["08\/06\/2025","12\/05\/2025","27\/05\/2025","04\/06\/2025","08\/07\/2025"],"flaer_phase":["Renew","Renew","Renew","Renew","Renew"],"updated":["07\/05\/2025 07:21","07\/05\/2025 07:21","07\/05\/2025 07:21","07\/05\/2025 07:21","07\/05\/2025 07:21"]},"total_rows":77,"analyzed_rows":77},"schema":{"table_name":"autobooks_import_sketchup_data","primary_key":"id","columns":{"id":{"type":"increments","nullable":false},"sold_to_name":{"type":"string","length":255,"nullable":true},"sold_to_number":{"type":"integer","nullable":true},"vendor_name":{"type":"string","length":255,"nullable":true},"reseller_number":{"type":"integer","nullable":true},"reseller_vendor_id":{"type":"string","length":255,"nullable":true},"end_customer_vendor_id":{"type":"integer","nullable":true},"end_customer_name":{"type":"string","length":255,"nullable":true},"end_customer_address_1":{"type":"string","length":255,"nullable":true},"end_customer_address_2":{"type":"string","length":255,"nullable":true},"end_customer_address_3":{"type":"string","length":255,"nullable":true},"end_customer_city":{"type":"string","length":255,"nullable":true},"end_customer_state":{"type":"string","length":255,"nullable":true},"end_customer_zip_code":{"type":"string","length":255,"nullable":true},"end_customer_country":{"type":"string","length":255,"nullable":true},"end_customer_account_type":{"type":"string","length":255,"nullable":true},"end_customer_contact_name":{"type":"string","length":255,"nullable":true},"end_customer_contact_email":{"type":"string","length":255,"nullable":true},"end_customer_contact_phone":{"type":"string","length":255,"nullable":true},"end_customer_industry_segment":{"type":"string","length":255,"nullable":true},"agreement_program_name":{"type":"string","length":255,"nullable":true},"agreement_number":{"type":"integer","nullable":true},"agreement_start_date":{"type":"string","length":255,"nullable":true},"agreement_end_date":{"type":"string","length":255,"nullable":true},"agreement_terms":{"type":"string","length":255,"nullable":true},"agreement_type":{"type":"string","length":255,"nullable":true},"agreement_status":{"type":"string","length":255,"nullable":true},"agreement_support_level":{"type":"string","length":255,"nullable":true},"agreement_days_due":{"type":"integer","nullable":true},"agreement_autorenew":{"type":"integer","nullable":true},"product_name":{"type":"string","length":255,"nullable":true},"product_family":{"type":"string","length":255,"nullable":true},"product_market_segment":{"type":"string","length":255,"nullable":true},"product_release":{"type":"string","length":255,"nullable":true},"product_type":{"type":"string","length":255,"nullable":true},"product_deployment":{"type":"string","length":255,"nullable":true},"product_sku":{"type":"string","length":255,"nullable":true},"product_sku_description":{"type":"string","length":255,"nullable":true},"product_part":{"type":"string","length":255,"nullable":true},"product_list_price":{"type":"integer","nullable":true},"product_list_price_currency":{"type":"string","length":255,"nullable":true},"subscription_id":{"type":"string","length":255,"nullable":true},"subscription_serial_number":{"type":"string","length":255,"nullable":true},"subscription_status":{"type":"string","length":255,"nullable":true},"subscription_quantity":{"type":"integer","nullable":true},"subscription_start_date":{"type":"string","length":255,"nullable":true},"subscription_end_date":{"type":"string","length":255,"nullable":true},"subscription_contact_name":{"type":"string","length":255,"nullable":true},"subscription_contact_email":{"type":"string","length":255,"nullable":true},"subscription_level":{"type":"string","length":255,"nullable":true},"subscription_days_due":{"type":"integer","nullable":true},"quotation_id":{"type":"string","length":255,"nullable":true},"quotation_type":{"type":"string","length":255,"nullable":true},"quotation_vendor_id":{"type":"integer","nullable":true},"quotation_deal_registration_number":{"type":"string","length":255,"nullable":true},"quotation_status":{"type":"string","length":255,"nullable":true},"quotation_resellerpo_previous":{"type":"string","length":255,"nullable":true},"quotation_due_date":{"type":"string","length":255,"nullable":true},"flaer_phase":{"type":"string","length":255,"nullable":true},"updated":{"type":"string","length":255,"nullable":true},"created_at":{"type":"timestamp","default":"CURRENT_TIMESTAMP"},"updated_at":{"type":"timestamp","default":"CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"}},"mapping":{"main":{"table":"autobooks_import_sketchup_data","key":"id","columns":{"sold_to_name":"sold_to_name","sold_to_number":"sold_to_number","vendor_name":"vendor_name","reseller_number":"reseller_number","reseller_vendor_id":"reseller_vendor_id","end_customer_vendor_id":"end_customer_vendor_id","end_customer_name":"end_customer_name","end_customer_address_1":"end_customer_address_1","end_customer_address_2":"end_customer_address_2","end_customer_address_3":"end_customer_address_3","end_customer_city":"end_customer_city","end_customer_state":"end_customer_state","end_customer_zip_code":"end_customer_zip_code","end_customer_country":"end_customer_country","end_customer_account_type":"end_customer_account_type","end_customer_contact_name":"end_customer_contact_name","end_customer_contact_email":"end_customer_contact_email","end_customer_contact_phone":"end_customer_contact_phone","end_customer_industry_segment":"end_customer_industry_segment","agreement_program_name":"agreement_program_name","agreement_number":"agreement_number","agreement_start_date":"agreement_start_date","agreement_end_date":"agreement_end_date","agreement_terms":"agreement_terms","agreement_type":"agreement_type","agreement_status":"agreement_status","agreement_support_level":"agreement_support_level","agreement_days_due":"agreement_days_due","agreement_autorenew":"agreement_autorenew","product_name":"product_name","product_family":"product_family","product_market_segment":"product_market_segment","product_release":"product_release","product_type":"product_type","product_deployment":"product_deployment","product_sku":"product_sku","product_sku_description":"product_sku_description","product_part":"product_part","product_list_price":"product_list_price","product_list_price_currency":"product_list_price_currency","subscription_id":"subscription_id","subscription_serial_number":"subscription_serial_number","subscription_status":"subscription_status","subscription_quantity":"subscription_quantity","subscription_start_date":"subscription_start_date","subscription_end_date":"subscription_end_date","subscription_contact_name":"subscription_contact_name","subscription_contact_email":"subscription_contact_email","subscription_level":"subscription_level","subscription_days_due":"subscription_days_due","quotation_id":"quotation_id","quotation_type":"quotation_type","quotation_vendor_id":"quotation_vendor_id","quotation_deal_registration_number":"quotation_deal_registration_number","quotation_status":"quotation_status","quotation_resellerpo_previous":"quotation_resellerpo_previous","quotation_due_date":"quotation_due_date","flaer_phase":"flaer_phase","updated":"updated"},"data_types":{"sold_to_name":"string","sold_to_number":"integer","vendor_name":"string","reseller_number":"integer","reseller_vendor_id":"string","end_customer_vendor_id":"integer","end_customer_name":"string","end_customer_address_1":"string","end_customer_address_2":"string","end_customer_address_3":"string","end_customer_city":"string","end_customer_state":"string","end_customer_zip_code":"string","end_customer_country":"string","end_customer_account_type":"string","end_customer_contact_name":"string","end_customer_contact_email":"string","end_customer_contact_phone":"string","end_customer_industry_segment":"string","agreement_program_name":"string","agreement_number":"integer","agreement_start_date":"string","agreement_end_date":"string","agreement_terms":"string","agreement_type":"string","agreement_status":"string","agreement_support_level":"string","agreement_days_due":"integer","agreement_autorenew":"integer","product_name":"string","product_family":"string","product_market_segment":"string","product_release":"string","product_type":"string","product_deployment":"string","product_sku":"string","product_sku_description":"string","product_part":"string","product_list_price":"integer","product_list_price_currency":"string","subscription_id":"string","subscription_serial_number":"string","subscription_status":"string","subscription_quantity":"integer","subscription_start_date":"string","subscription_end_date":"string","subscription_contact_name":"string","subscription_contact_email":"string","subscription_level":"string","subscription_days_due":"integer","quotation_id":"string","quotation_type":"string","quotation_vendor_id":"integer","quotation_deal_registration_number":"string","quotation_status":"string","quotation_resellerpo_previous":"string","quotation_due_date":"string","flaer_phase":"string","updated":"string"}}}},"import_result":{"success":true,"message":"Successfully imported 77 rows into typed columns","imported_count":77,"failed_count":0,"table_name":"autobooks_import_sketchup_data"},"config_result":{"success":true,"message":"Table configuration generated and stored successfully","config":{"title":"Autobooks import sketchup data","description":"Auto-generated table from CSV import (77 rows, 77 analyzed)","items":[{"id":1,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SECUREBASE LTD","end_customer_address_1":"112 High Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"EAST FINCHLEY","end_customer_state":null,"end_customer_zip_code":"N2 9EB","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Ho Hek","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2009914,"agreement_start_date":"30\/05\/2024","agreement_end_date":"29\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":23,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2124836EU","subscription_serial_number":"ERFC2124836EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"30\/05\/2024","subscription_end_date":"29\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":23,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2009914,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"08\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":2,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"RUSSELL JONES LIMITED","end_customer_address_1":"5 Wembury Mews","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"N6 5XJ","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Russell Jones","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":1988749,"agreement_start_date":"03\/05\/2024","agreement_end_date":"02\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"EXPIRED","agreement_support_level":"Tier 2","agreement_days_due":-4,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2098644EU","subscription_serial_number":"ERFC2098644EU","subscription_status":"EXPIRED","subscription_quantity":1,"subscription_start_date":"03\/05\/2024","subscription_end_date":"02\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":-4,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":1988749,"quotation_deal_registration_number":null,"quotation_status":"Urgent Renewal","quotation_resellerpo_previous":null,"quotation_due_date":"12\/05\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":3,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"JOEL TRINDADE DESIGNS","end_customer_address_1":"2 Holford Way","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"SW15 5EY","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Joel Trindade","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2000131,"agreement_start_date":"18\/05\/2024","agreement_end_date":"17\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":11,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2111317EU","subscription_serial_number":"ERFC2111317EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"18\/05\/2024","subscription_end_date":"17\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":11,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2000131,"quotation_deal_registration_number":null,"quotation_status":"Urgent Renewal","quotation_resellerpo_previous":null,"quotation_due_date":"27\/05\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":4,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DANDELION SEEDS ARCHITECTS","end_customer_address_1":"2 Addison Grove","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"TAUNTON","end_customer_state":null,"end_customer_zip_code":"TA2 6JF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Phillip Bristow","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2003680,"agreement_start_date":"26\/05\/2024","agreement_end_date":"25\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":19,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2115556EU","subscription_serial_number":"ERFC2115556EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"26\/05\/2024","subscription_end_date":"25\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":19,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2003680,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":5,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MATT BARWELL","end_customer_address_1":"1 Corn Exchange","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CHICHESTER","end_customer_state":null,"end_customer_zip_code":"PO19 1BF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Matt Barwell","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2024470,"agreement_start_date":"29\/06\/2024","agreement_end_date":"28\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":53,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2145582EU","subscription_serial_number":"ERFC2145582EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"29\/06\/2024","subscription_end_date":"28\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":53,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2024470,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"08\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":6,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"PHILIP BINGHAM ASSOCIATES","end_customer_address_1":"14a Market Place","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"HORNSEA","end_customer_state":null,"end_customer_zip_code":"HU18 1AW","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Phil Bingham","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2015264,"agreement_start_date":"08\/06\/2024","agreement_end_date":"07\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":32,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2131814EU","subscription_serial_number":"ERFC2131814EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"08\/06\/2024","subscription_end_date":"07\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":32,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2015264,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"17\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":7,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"INNOSCAPE LTD","end_customer_address_1":"2 Denby Hill","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"KEIGHLEY","end_customer_state":null,"end_customer_zip_code":"BD22 7QB","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Ben Howarth","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2024472,"agreement_start_date":"19\/06\/2024","agreement_end_date":"18\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":43,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2145592EU","subscription_serial_number":"ERFC2145592EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"19\/06\/2024","subscription_end_date":"18\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":43,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2024472,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"28\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":8,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"CRAIG SHEPPARD","end_customer_address_1":"15 Frampton Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"HERTFORD","end_customer_state":null,"end_customer_zip_code":"SG14 1QG","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Craig Sheppard","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2026168,"agreement_start_date":"21\/06\/2024","agreement_end_date":"20\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":45,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2148470EU","subscription_serial_number":"ERFC2148470EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"21\/06\/2024","subscription_end_date":"20\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":45,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2026168,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"30\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":9,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DESIGNS TO BUILD ON LIMITED","end_customer_address_1":"15 The Hawthorns","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CHARVIL","end_customer_state":null,"end_customer_zip_code":"RG10 9TS","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Steven Ellam","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2021796,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2141345EU","subscription_serial_number":"ERFC2141345EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2021796,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":10,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"HOMES UNDER THE HANNAH","end_customer_address_1":"14 Roseland Avenue","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"MANCHESTER","end_customer_state":null,"end_customer_zip_code":"M20 3QY","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Hannah Bridge","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2022880,"agreement_start_date":"18\/06\/2024","agreement_end_date":"17\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":42,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2143323EU","subscription_serial_number":"ERFC2143323EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"18\/06\/2024","subscription_end_date":"17\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":42,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2022880,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"27\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":11,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SAPPHIRE SPACES","end_customer_address_1":"3 Dart Business Park","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"TOPSHAM","end_customer_state":null,"end_customer_zip_code":"EX3 0QH","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Mark Newbery","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2020066,"agreement_start_date":"13\/06\/2024","agreement_end_date":"12\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":37,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":602,"product_list_price_currency":"GBP","subscription_id":"ERFC2139201EU","subscription_serial_number":"ERFC2139201EU","subscription_status":"ACTIVE","subscription_quantity":2,"subscription_start_date":"13\/06\/2024","subscription_end_date":"12\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":37,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2020066,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"22\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":12,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MJCT LIMITED","end_customer_address_1":"Hardwick Business","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"BANBURY","end_customer_state":null,"end_customer_zip_code":"OX16 2AF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jon Courtney-Thompson","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2017299,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2134858EU","subscription_serial_number":"ERFC2134858EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2017299,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":13,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MJCT LIMITED","end_customer_address_1":"Hardwick Business","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"BANBURY","end_customer_state":null,"end_customer_zip_code":"OX16 2AF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jon Courtney-Thompson","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2017300,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2134857EU","subscription_serial_number":"ERFC2134857EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2017300,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":14,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"MJCT LIMITED","end_customer_address_1":"Hardwick Business","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"BANBURY","end_customer_state":null,"end_customer_zip_code":"OX16 2AF","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jon Courtney-Thompson","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2017947,"agreement_start_date":"17\/06\/2024","agreement_end_date":"16\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":41,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2135872EU","subscription_serial_number":"ERFC2135872EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"17\/06\/2024","subscription_end_date":"16\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":41,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2017947,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"26\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":15,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SENSUS ARCHITECTURE LTD","end_customer_address_1":"7 Main Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"KINGS LYNN","end_customer_state":null,"end_customer_zip_code":"PE31 8BB","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martin Stuart","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2009740,"agreement_start_date":"15\/06\/2024","agreement_end_date":"14\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":39,"agreement_autorenew":0,"product_name":"Sketchup Studio","product_family":"Sketchup Studio","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-STDO-YR-CNL-02","product_sku_description":"Our premium, Windows-only offering, annual termed contract, gives you everything you need to steer your projects ahead with confidence including a professional 3D modeler, design research insights, project management, point cloud data interoperability, re","product_part":null,"product_list_price":645,"product_list_price_currency":"GBP","subscription_id":"ERFC2124453EU","subscription_serial_number":"ERFC2124453EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"15\/06\/2024","subscription_end_date":"14\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":39,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2009740,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"24\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":16,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"17 - 19 Leicester Square","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"WC2H 7LE","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2027626,"agreement_start_date":"24\/06\/2024","agreement_end_date":"23\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":48,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":3612,"product_list_price_currency":"GBP","subscription_id":"ERFC2150610EU","subscription_serial_number":"ERFC2150610EU","subscription_status":"ACTIVE","subscription_quantity":12,"subscription_start_date":"24\/06\/2024","subscription_end_date":"23\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":48,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2027626,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"03\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":17,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"17 - 19 Leicester Square","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"WC2H 7LE","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028255,"agreement_start_date":"25\/06\/2024","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2151519EU","subscription_serial_number":"ERFC2151519EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"25\/06\/2024","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028255,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":18,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"17 - 19 Leicester Square","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"WC2H 7LE","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028277,"agreement_start_date":"25\/06\/2024","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2151549EU","subscription_serial_number":"ERFC2151549EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"25\/06\/2024","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028277,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":19,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Martha Enthoven","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028285,"agreement_start_date":"25\/06\/2024","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2151554EU","subscription_serial_number":"ERFC2151554EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"25\/06\/2024","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028285,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":20,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Ryan Holland","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2239553,"agreement_start_date":"06\/03\/2025","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2438089EU","subscription_serial_number":"ERFC2438089EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"06\/03\/2025","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2239553,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":21,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Soda Studio","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2215113,"agreement_start_date":"07\/02\/2025","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2403193EU","subscription_serial_number":"ERFC2403193EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"07\/02\/2025","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2215113,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":22,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"SODA","end_customer_address_1":"Soda Studio 1 Bourchier Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"W1D 4HX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Sukey Clark","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2241251,"agreement_start_date":"07\/03\/2025","agreement_end_date":"24\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":49,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2440281EU","subscription_serial_number":"ERFC2440281EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"07\/03\/2025","subscription_end_date":"24\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":49,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2241251,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"04\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":23,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"ACORN PARTITIONS & STORAGE SYSTEMS LIMITED","end_customer_address_1":"Kingsley Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LINCOLN","end_customer_state":null,"end_customer_zip_code":"LN6 3TA","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Stuart Wall","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2004996,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2117597EU","subscription_serial_number":"ERFC2117597EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2004996,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":24,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"ACORN PARTITIONS & STORAGE SYSTEMS LIMITED","end_customer_address_1":"Kingsley Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LINCOLN","end_customer_state":null,"end_customer_zip_code":"LN6 3TA","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Stuart Wall","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2005021,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":602,"product_list_price_currency":"GBP","subscription_id":"ERFC2117636EU","subscription_serial_number":"ERFC2117636EU","subscription_status":"ACTIVE","subscription_quantity":2,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2005021,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":25,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"ACORN PARTITIONS & STORAGE SYSTEMS LIMITED","end_customer_address_1":"Kingsley Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LINCOLN","end_customer_state":null,"end_customer_zip_code":"LN6 3TA","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Stuart Wall","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2005023,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2117635EU","subscription_serial_number":"ERFC2117635EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2005023,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":26,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DI ORO INTERIORS LTD","end_customer_address_1":"78 Whitchurch Road","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CARDIFF","end_customer_state":null,"end_customer_zip_code":"CF14 3LX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Esther Milardi","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2027632,"agreement_start_date":"24\/06\/2024","agreement_end_date":"23\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":48,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2150628EU","subscription_serial_number":"ERFC2150628EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"24\/06\/2024","subscription_end_date":"23\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":48,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2027632,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"03\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":27,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DLG ARCHITECTS LEEDS","end_customer_address_1":"One Brewery Wharf Waterloo Street","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LEEDS","end_customer_state":null,"end_customer_zip_code":"LS10 1GX","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Allen Norris","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2028194,"agreement_start_date":"30\/06\/2024","agreement_end_date":"29\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":54,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":2408,"product_list_price_currency":"GBP","subscription_id":"ERFC2151460EU","subscription_serial_number":"ERFC2151460EU","subscription_status":"ACTIVE","subscription_quantity":8,"subscription_start_date":"30\/06\/2024","subscription_end_date":"29\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":54,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2028194,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"09\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":28,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"DAY CUMMINS LIMITED","end_customer_address_1":"4A Lakeland Business Park","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"COCKERMOUTH","end_customer_state":null,"end_customer_zip_code":"CA13 9QZ","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Jen Metcalf","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2009940,"agreement_start_date":"30\/05\/2024","agreement_end_date":"29\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":23,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2124860EU","subscription_serial_number":"ERFC2124860EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"30\/05\/2024","subscription_end_date":"29\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":23,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2009940,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"08\/06\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":29,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"JAMES MACKINTOSH ARCHITECTS LIMITED","end_customer_address_1":"First Floor","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"CHIPPING NORTON","end_customer_state":null,"end_customer_zip_code":"OX7 5AD","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"James Mackintosh","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2018948,"agreement_start_date":"23\/06\/2024","agreement_end_date":"22\/06\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":47,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2137412EU","subscription_serial_number":"ERFC2137412EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"23\/06\/2024","subscription_end_date":"22\/06\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":47,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2018948,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"02\/07\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"},{"id":30,"sold_to_name":"TD SYNNEX United Kingdom","sold_to_number":**********,"vendor_name":"Trimble","reseller_number":**********,"reseller_vendor_id":"0014-317383","end_customer_vendor_id":**********,"end_customer_name":"TREESAURUS","end_customer_address_1":"39 Braxted Park","end_customer_address_2":null,"end_customer_address_3":null,"end_customer_city":"LONDON","end_customer_state":null,"end_customer_zip_code":"SW16 3DU","end_customer_country":"United Kingdom","end_customer_account_type":"Commercial","end_customer_contact_name":"Charles Mitchell","end_customer_contact_email":"<EMAIL>","end_customer_contact_phone":null,"end_customer_industry_segment":null,"agreement_program_name":"Trimble Subscriptions","agreement_number":2002386,"agreement_start_date":"21\/05\/2024","agreement_end_date":"20\/05\/2025","agreement_terms":"Annual","agreement_type":"Commercial","agreement_status":"ACTIVE","agreement_support_level":"Tier 2","agreement_days_due":14,"agreement_autorenew":0,"product_name":"Sketchup Pro","product_family":"Sketchup Pro","product_market_segment":null,"product_release":null,"product_type":"Subscription","product_deployment":"S","product_sku":"SKP-PRO-YR-CNL","product_sku_description":"Channel SketchUp Pro bundle for professional use, annual termed contract. Includes individual use of: SketchUp desktop, PreDesign, LayOut, Style Builder, SketchUp Viewer for XR apps, SketchUp Viewer for Mobile apps, SketchUp web, SketchUp for iPad, Pro su","product_part":null,"product_list_price":301,"product_list_price_currency":"GBP","subscription_id":"ERFC2113858EU","subscription_serial_number":"ERFC2113858EU","subscription_status":"ACTIVE","subscription_quantity":1,"subscription_start_date":"21\/05\/2024","subscription_end_date":"20\/05\/2025","subscription_contact_name":null,"subscription_contact_email":null,"subscription_level":null,"subscription_days_due":14,"quotation_id":null,"quotation_type":null,"quotation_vendor_id":2002386,"quotation_deal_registration_number":null,"quotation_status":null,"quotation_resellerpo_previous":null,"quotation_due_date":"30\/05\/2025","flaer_phase":"Renew","updated":"07\/05\/2025 07:21","created_at":"2025-09-05 13:33:48","updated_at":"2025-09-05 13:33:48"}],"columns":[{"label":"Sold To Name","fields":["sold_to_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"sold_to_name","confidence":100},{"label":"Vendor Name","fields":["vendor_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"vendor_name","confidence":100},{"label":"Company Name","fields":["end_customer_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"company_name","confidence":100},{"label":"End Customer Address 1","fields":["end_customer_address_1"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"address","confidence":100},{"label":"End Customer State","fields":["end_customer_state"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"state","confidence":100},{"label":"Uk Postcode","fields":["end_customer_zip_code"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"postal_code","confidence":100},{"label":"End Customer Country","fields":["end_customer_country"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"country","confidence":100},{"label":"End Customer Contact Name","fields":["end_customer_contact_name"],"filter":true,"sortable":true,"extra_parameters":"","unified_field":"contact_name","confidence":100}],"available_fields":["sold_to_number","reseller_vendor_id","end_customer_vendor_id","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_account_type","end_customer_contact_phone","end_customer_industry_segment","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_market_segment","product_sku_description","product_list_price","subscription_quantity","subscription_start_date","subscription_end_date","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","end_customer_contact_email","agreement_program_name","agreement_number","product_name","product_family","subscription_id","subscription_status","subscription_contact_name","subscription_contact_email","reseller_number","product_release","product_type","product_deployment","product_sku","product_part","product_list_price_currency","subscription_serial_number","id"],"rows":{"id_prefix":"row_","id_field":"id","class_postfix":"","extra_parameters":""},"just_body":false,"just_rows":false,"items_per_page":30,"current_page_num":1,"sort_column":"","sort_direction":"asc","callback":"","class":""},"route_key":"enhanced_autobooks_import_sketchup_data"}}
