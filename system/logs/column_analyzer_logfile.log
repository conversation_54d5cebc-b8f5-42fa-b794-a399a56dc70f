[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:631] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:642] Using standard formatting for sold_to_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:631] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:642] Using standard formatting for vendor_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:642] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:642] Using standard formatting for end_customer_state
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:627] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:642] Using standard formatting for end_customer_country
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:534] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_sketchup_data
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:538] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:610] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:614] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:617] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:631] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-09-05 13:33:49] [data_table_generator.class.php:642] Using standard formatting for end_customer_contact_name
