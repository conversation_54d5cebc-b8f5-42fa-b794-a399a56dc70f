[database_schema] [2025-09-05 13:33:48] [database.class.php:1718] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VA<PERSON>HA<PERSON>(255) NULL, `sold_to_number` INT NULL, `vendor_name` VA<PERSON>HAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VA<PERSON>HAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARC<PERSON><PERSON>(255) NULL, `end_customer_contact_name` VA<PERSON>HAR(255) NULL, `end_customer_contact_email` VARC<PERSON>R(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-09-05 13:33:48\n)\n
