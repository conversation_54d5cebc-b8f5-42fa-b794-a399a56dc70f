[data_table_inheritance] [2025-09-04 08:37:23] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-04 08:37:23] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-04 08:37:23] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-04 08:37:23] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-04 08:37:26] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_subscriptions
[data_table_inheritance] [2025-09-04 08:37:26] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_subscriptions
[data_table_inheritance] [2025-09-04 08:37:29] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:37:29] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:37:29] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:37:29] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:40:42] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:40:42] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_reorder_columns
[data_table_inheritance] [2025-09-04 08:41:41] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:41:41] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 08:41:56] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:41:56] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:41:56] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:41:56] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:56:53] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:56:53] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:56:53] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 08:56:53] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:35:36] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:35:36] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:35:36] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:35:36] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:37:45] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:37:45] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:37:45] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:37:45] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:38:00] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 09:38:00] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 10:09:44] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:09:44] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:09:44] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:09:44] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:20:05] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:20:05] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:20:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:20:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:26:32] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:26:32] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 10:29:04] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:29:04] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:29:04] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:29:04] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:31:40] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:31:40] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:31:40] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:31:40] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:33:29] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-04 10:33:29] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-04 10:33:29] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_products
[data_table_inheritance] [2025-09-04 10:33:29] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_products
[data_table_inheritance] [2025-09-04 10:33:33] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:33:33] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:33:33] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:33:33] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:33:41] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:33:41] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 10:35:21] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:35:21] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_reorder_columns
[data_table_inheritance] [2025-09-04 10:41:04] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-04 10:41:04] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-04 10:41:04] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_orders
[data_table_inheritance] [2025-09-04 10:41:04] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_orders
[data_table_inheritance] [2025-09-04 10:41:24] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:41:24] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:41:24] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:41:24] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:49:09] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:49:09] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:49:09] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 10:49:09] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:02:20] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:02:20] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 11:04:10] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:10] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:11] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:11] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:23] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:23] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 11:04:54] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:54] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:54] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:04:54] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:05:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:05:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 11:06:00] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:06:00] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:06:00] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:06:00] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:07:12] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:07:12] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:07:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:07:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:07:26] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:07:26] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 11:10:21] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:10:21] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:10:21] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:10:21] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:10:30] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:10:30] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 11:23:33] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:23:33] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 11:23:36] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:23:36] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:23:36] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 11:23:36] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:44:57] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:44:57] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:44:57] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:44:57] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:56:46] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:56:46] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:56:46] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:56:46] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:00] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:00] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:00] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:00] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:23] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:23] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 12:57:33] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 12:57:33] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 13:30:45] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 13:30:45] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 13:30:45] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 13:30:45] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 13:32:16] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 13:32:16] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 13:32:16] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 13:32:16] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:01:01] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:01] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:01] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:01] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:05] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:05] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:01:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:18:19] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:18:19] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:18:19] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:18:19] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:25:45] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 14:25:45] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 14:25:45] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:25:45] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:49:12] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 14:49:12] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 14:49:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:49:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:53:12] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 14:53:12] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 14:53:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:53:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 14:53:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:53:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:53:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:53:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:57:38] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:57:38] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:57:38] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:57:38] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:59:52] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:59:52] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:59:52] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 14:59:52] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:00:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:00:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 15:06:09] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:09] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:09] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:09] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 15:06:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-04 15:06:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 15:06:16] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-04 15:06:19] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:19] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:19] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:19] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:30] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:30] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 15:06:43] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:43] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:43] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:06:43] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:08:44] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:08:44] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:08:44] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:08:44] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:15:55] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:15:55] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:15:55] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:15:55] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 15:16:16] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:16] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:16] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:16] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:27] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:16:27] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 15:32:37] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:32:37] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:32:37] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:32:37] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:35:36] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:35:36] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:35:36] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:35:36] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:35:45] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:35:45] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 15:40:00] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:40:00] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:40:00] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 15:40:00] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:33:56] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:33:56] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:33:56] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:33:56] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:21] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:21] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 20:35:33] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:33] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:33] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:33] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:39] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 20:35:39] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 21:42:46] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:42:46] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:42:46] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:42:46] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:52:02] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:52:02] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:52:02] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:52:02] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:53:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:53:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:53:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:53:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:53:20] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 21:53:20] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-04 22:30:20] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:30:20] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:30:20] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:30:20] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:32:05] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:32:05] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:32:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:32:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:33:14] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:33:14] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:33:14] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:33:14] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:33:27] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-04 22:33:27] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 07:59:17] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 07:59:17] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 07:59:17] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 07:59:17] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:00:03] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:00:03] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:00:03] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:00:03] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:06:58] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:06:58] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:06:58] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 08:06:58] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:03:25] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:03:25] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:03:25] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:03:25] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:04:04] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:04:04] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:04:04] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:04:04] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:48] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:48] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:48] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:48] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:55] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:55] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:55] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:09:55] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:23] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:23] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:23] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:23] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:29] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:29] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:29] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:10:29] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:13:32] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:13:32] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:13:32] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:13:32] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:14:24] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:14:24] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:14:24] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:14:24] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:22:10] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-05 09:22:10] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-05 09:22:10] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_orders
[data_table_inheritance] [2025-09-05 09:22:10] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_orders
[data_table_inheritance] [2025-09-05 09:22:12] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:12] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:22:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:22:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:22:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:22:18] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:18] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:18] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:18] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:22:20] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:22:20] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:22:20] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:22:20] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:22:29] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:22:29] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 09:29:59] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:29:59] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:29:59] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:29:59] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:31] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:31] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:31] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:31] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:34] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:34] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:34] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:30:34] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:31:24] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:31:24] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:31:24] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:31:24] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:31:28] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:31:28] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:31:28] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:31:28] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:31:33] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:31:33] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:31:33] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:31:33] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:31:36] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_logs
[data_table_inheritance] [2025-09-05 09:31:36] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_logs
[data_table_inheritance] [2025-09-05 09:31:36] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_logs
[data_table_inheritance] [2025-09-05 09:31:36] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_logs
[data_table_inheritance] [2025-09-05 09:31:43] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:43] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:43] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:43] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:46] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:46] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:46] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:31:46] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_logs_view
[data_table_inheritance] [2025-09-05 09:32:04] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:32:04] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:32:04] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:32:04] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 09:32:37] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:37] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:37] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:37] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:57] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:57] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:57] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:32:57] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:33:01] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:01] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:01] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:01] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:04] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:04] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:04] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:04] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 09:33:42] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:33:42] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:33:42] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:33:42] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:34:53] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-05 09:34:53] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-05 09:34:53] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_orders
[data_table_inheritance] [2025-09-05 09:34:53] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_orders
[data_table_inheritance] [2025-09-05 09:34:54] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-05 09:34:54] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: 
[data_table_inheritance] [2025-09-05 09:34:54] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_quotes
[data_table_inheritance] [2025-09-05 09:34:54] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_quotes
[data_table_inheritance] [2025-09-05 09:34:56] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_subscriptions
[data_table_inheritance] [2025-09-05 09:34:56] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_subscriptions
[data_table_inheritance] [2025-09-05 09:34:58] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:34:58] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:34:58] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:34:58] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:12] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:12] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:35:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:38:58] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:38:58] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:38:58] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:38:58] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:43] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:43] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:43] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:43] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:56] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:56] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:56] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:41:56] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:25] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:25] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:25] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:25] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:57] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:57] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:57] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:42:57] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:09] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:09] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:09] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:09] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:21] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:21] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:21] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:43:21] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:45:17] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:45:17] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:45:17] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:45:17] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:46:00] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:46:00] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:46:00] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 09:46:00] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:18:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:18:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:18:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:18:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:41:02] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:41:02] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:41:02] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:41:02] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:43:01] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:43:01] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:43:01] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:43:01] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:53:28] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:53:28] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:53:28] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:53:28] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:54:17] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:54:17] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:54:17] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:54:17] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:57:30] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:57:30] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:57:30] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 10:57:30] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:32:21] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:32:21] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:32:21] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:32:21] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:33:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:33:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:33:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:33:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:34:11] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:34:11] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:34:11] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 11:34:11] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:43:00] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:43:00] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:43:00] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:43:00] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:59:16] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:59:16] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:59:16] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 12:59:16] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:01:15] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:01:15] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:01:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:01:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:19:16] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:19:16] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:19:16] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:19:16] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:33] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:33] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:33] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:33] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:42] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:42] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:42] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:42] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:52] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:52] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:52] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:52] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: autodesk_customers
[data_table_inheritance] [2025-09-05 13:28:54] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:28:54] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:28:54] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:28:54] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:28:56] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:28:56] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:28:56] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:28:56] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:34:28] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 13:34:28] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 13:34:28] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 13:34:28] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs
[data_table_inheritance] [2025-09-05 13:34:30] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 13:34:30] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 13:34:30] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 13:34:30] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: system_logs_view
[data_table_inheritance] [2025-09-05 13:38:49] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:38:50] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:38:50] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:38:50] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:39:10] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:39:10] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_toggle_column
[data_table_inheritance] [2025-09-05 13:39:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:39:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_toggle_column
[data_table_inheritance] [2025-09-05 13:39:14] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:39:14] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_toggle_column
[data_table_inheritance] [2025-09-05 13:39:15] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:39:15] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_toggle_column
[data_table_inheritance] [2025-09-05 13:39:18] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:39:18] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:39:18] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:39:18] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:40:05] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:40:05] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 13:40:10] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:40:11] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_reorder_columns
[data_table_inheritance] [2025-09-05 13:40:13] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:40:13] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 13:40:22] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:40:22] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:40:23] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:40:23] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:41:03] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:41:03] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 13:41:09] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:41:10] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_reorder_columns
[data_table_inheritance] [2025-09-05 13:41:12] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:41:12] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:41:12] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:41:12] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:41:25] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:41:26] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 13:56:56] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:56:56] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:56:56] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:56:56] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:57:09] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:57:10] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 13:58:50] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:58:50] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:58:50] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:58:51] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 13:58:56] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 13:58:56] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 14:00:06] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:00:06] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:00:06] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 14:00:07] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 14:00:14] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:00:14] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 14:01:03] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:01:03] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:01:03] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 14:01:03] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: sketchup_sketchup
[data_table_inheritance] [2025-09-05 14:01:10] [sys_edge_data-table-structure.edge.php:47]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:01:11] [sys_edge_data-table-rows.edge.php:47]  Data table master initialization completed for: api_data_table_column_preferences_move_field_simple
[data_table_inheritance] [2025-09-05 14:02:31] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:02:31] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:02:34] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:02:34] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:03:33] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:03:33] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:04:02] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:04:02] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:04:03] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:04:04] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:04:13] [sys_edge_data-table.edge.php:54]  Data table master initialization completed for: autobooks_import_sketchup_data
[data_table_inheritance] [2025-09-05 14:04:13] [sys_edge_data-table-column-manager.edge.php:50]  Data table master initialization completed for: autobooks_import_sketchup_data
