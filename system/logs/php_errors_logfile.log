[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(36) "Undefined variable $available_fields"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(33)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $available_fields","file":"sys_edge_data-table-structure.edge.php","line":33,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 33\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $available_fields"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 33\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(36) "Undefined variable $available_fields"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(46)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $available_fields","file":"sys_edge_data-table-structure.edge.php","line":46,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 46\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $available_fields"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 46\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(31) "Undefined array key "col_field""\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(96)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"col_field\"","file":"sys_edge_data-table-structure.edge.php","line":96,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 96\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"col_field\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 96\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(31) "Undefined variable $sort_column"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(97)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $sort_column","file":"sys_edge_data-table-structure.edge.php","line":97,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 97\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $sort_column"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 97\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(28) "Undefined variable $id_count"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(97)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $id_count","file":"sys_edge_data-table-structure.edge.php","line":97,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 97\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $id_count"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 97\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(28) "Undefined array key "column""\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(26)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"column\"","file":"sys_edge_data-table-filter.edge.php","line":26,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php, Line: 26\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"column\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n         3: 26\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(37) "Trying to access array offset on null"\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(26)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Trying to access array offset on null","file":"sys_edge_data-table-filter.edge.php","line":26,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php, Line: 26\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Trying to access array offset on null"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n         3: 26\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(37) "Trying to access array offset on null"\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(26)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Trying to access array offset on null","file":"sys_edge_data-table-filter.edge.php","line":26,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php, Line: 26\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Trying to access array offset on null"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n         3: 26\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Undefined array key "file""\n  ["file"]: string(13) "functions.php"\n  ["line"]: int(154)\n  ["full_file"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"file\"","file":"functions.php","line":154,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 154\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"file\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php"\n         3: 154\n      <strong>Function:</strong> print_rr, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 210\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Error","message":"Call to undefined function icons\\icon()","file":"sys_edge_data-table-filter.edge.php","line":70,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","trace":"#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\\edge::phprender()\n#5 test_template_inheritance.php(30): edge\\edge::render()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "functions.php > tcs_log() 210"\n         2: true\n         3: true\n         4: false\n         5: true\n         6: true\n         7: null\n         8: true\n         9: null\n         10: false\n         11: false\n         12: ""\n         13: 1\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Undefined array key "line""\n  ["file"]: string(13) "functions.php"\n  ["line"]: int(154)\n  ["full_file"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"line\"","file":"functions.php","line":154,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 154\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"line\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php"\n         3: 154\n      <strong>Function:</strong> print_rr, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 210\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Error","message":"Call to undefined function icons\\icon()","file":"sys_edge_data-table-filter.edge.php","line":70,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","trace":"#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\\edge::phprender()\n#5 test_template_inheritance.php(30): edge\\edge::render()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "functions.php > tcs_log() 210"\n         2: true\n         3: true\n         4: false\n         5: true\n         6: true\n         7: null\n         8: true\n         9: null\n         10: false\n         11: false\n         12: ""\n         13: 1\n-->\n
[php_errors] [2025-09-09 14:06:02] [functions.php:313] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(5) "Error"\n  ["message"]: string(39) "Call to undefined function icons\icon()"\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(70)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["trace"]: string(287) "#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\edge::phprender()\n#5 test_template_inheritance.php(30): edge\edge::render()\n"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 313\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Error","message":"Call to undefined function icons\\icon()","file":"sys_edge_data-table-filter.edge.php","line":70,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","trace":"#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\\edge::phprender()\n#5 test_template_inheritance.php(30): edge\\edge::render()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(36) "Undefined variable $available_fields"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(33)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $available_fields","file":"sys_edge_data-table-structure.edge.php","line":33,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 33\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $available_fields"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 33\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(36) "Undefined variable $available_fields"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(46)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $available_fields","file":"sys_edge_data-table-structure.edge.php","line":46,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 46\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $available_fields"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 46\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(31) "Undefined array key "col_field""\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(96)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"col_field\"","file":"sys_edge_data-table-structure.edge.php","line":96,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 96\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"col_field\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 96\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(31) "Undefined variable $sort_column"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(97)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $sort_column","file":"sys_edge_data-table-structure.edge.php","line":97,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 97\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $sort_column"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 97\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(28) "Undefined variable $id_count"\n  ["file"]: string(38) "sys_edge_data-table-structure.edge.php"\n  ["line"]: int(97)\n  ["full_file"]: string(92) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined variable $id_count","file":"sys_edge_data-table-structure.edge.php","line":97,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-structure.edge.php, Line: 97\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined variable $id_count"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n         3: 97\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-structure.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(28) "Undefined array key "column""\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(26)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"column\"","file":"sys_edge_data-table-filter.edge.php","line":26,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php, Line: 26\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"column\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n         3: 26\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(37) "Trying to access array offset on null"\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(26)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Trying to access array offset on null","file":"sys_edge_data-table-filter.edge.php","line":26,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php, Line: 26\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Trying to access array offset on null"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n         3: 26\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(37) "Trying to access array offset on null"\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(26)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Trying to access array offset on null","file":"sys_edge_data-table-filter.edge.php","line":26,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php, Line: 26\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Trying to access array offset on null"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n         3: 26\n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php"\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Undefined array key "file""\n  ["file"]: string(13) "functions.php"\n  ["line"]: int(154)\n  ["full_file"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"file\"","file":"functions.php","line":154,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 154\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"file\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php"\n         3: 154\n      <strong>Function:</strong> print_rr, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 210\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Error","message":"Call to undefined function icons\\icon()","file":"sys_edge_data-table-filter.edge.php","line":70,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","trace":"#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\\edge::phprender()\n#5 test_template_inheritance.php(30): edge\\edge::render()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "functions.php > tcs_log() 210"\n         2: true\n         3: true\n         4: false\n         5: true\n         6: true\n         7: null\n         8: true\n         9: null\n         10: false\n         11: false\n         12: ""\n         13: 1\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:263] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Undefined array key "line""\n  ["file"]: string(13) "functions.php"\n  ["line"]: int(154)\n  ["full_file"]: string(105) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 263\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Undefined array key \"line\"","file":"functions.php","line":154,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 154\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Undefined array key \"line\""\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php"\n         3: 154\n      <strong>Function:</strong> print_rr, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 210\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Error","message":"Call to undefined function icons\\icon()","file":"sys_edge_data-table-filter.edge.php","line":70,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","trace":"#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\\edge::phprender()\n#5 test_template_inheritance.php(30): edge\\edge::render()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "functions.php > tcs_log() 210"\n         2: true\n         3: true\n         4: false\n         5: true\n         6: true\n         7: null\n         8: true\n         9: null\n         10: false\n         11: false\n         12: ""\n         13: 1\n-->\n
[php_errors] [2025-09-09 14:06:05] [functions.php:313] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(5) "Error"\n  ["message"]: string(39) "Call to undefined function icons\icon()"\n  ["file"]: string(35) "sys_edge_data-table-filter.edge.php"\n  ["line"]: int(70)\n  ["full_file"]: string(89) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/sys_edge_data-table-filter.edge.php"\n  ["trace"]: string(287) "#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\edge::phprender()\n#5 test_template_inheritance.php(30): edge\edge::render()\n"\n  ["request_uri"]: string(62) "/baffletrain/autocadlt/autobooks/test_template_inheritance.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 313\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Error","message":"Call to undefined function icons\\icon()","file":"sys_edge_data-table-filter.edge.php","line":70,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/sys_edge_data-table-filter.edge.php","trace":"#0 edge.class.php(171): include()\n#1 edge.class.php(164): edge\\edge::phprender()\n#2 sys_edge_data-table-structure.edge.php(97): edge\\edge::render()\n#3 edge.class.php(171): include()\n#4 edge.class.php(164): edge\\edge::phprender()\n#5 test_template_inheritance.php(30): edge\\edge::render()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_template_inheritance.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
