[data_source_manager] [2025-09-09 14:21:31] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC
[data_source_manager] [2025-09-09 14:21:31] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC
[data_source_manager] [2025-09-09 14:21:31] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC
[data_source_manager] [2025-09-09 14:32:26] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC
[data_source_manager] [2025-09-09 14:37:11] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC
[data_source_manager] [2025-09-09 14:38:51] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, (DATEDIFF(subs.enddate, NOW())) AS `enddate_diff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN `autodesk_accounts` AS `soldto` ON `subs`.`soldTo_csn` = `soldto`.`account_csn` LEFT JOIN `autodesk_accounts` AS `solpro` ON `subs`.`solutionProvider_csn` = `solpro`.`account_csn` LEFT JOIN `autodesk_accounts` AS `resell` ON `subs`.`nurtureReseller_csn` = `resell`.`account_csn` ORDER BY `enddate_diff` ASC
