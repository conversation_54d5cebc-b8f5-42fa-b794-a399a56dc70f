[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-09 14:20:40
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c9a84bea-7d20-446f-ab0b-e4ea37288664\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 64561338134140\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-09T13:52:39.000+0000\n        )\n\n    [publishedAt] => 2025-09-09T14:20:38.000Z\n    [csn] => 5103159758\n)\n
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '64561338134140', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '64561338134140', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-09 14:20:40
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c9a84bea-7d20-446f-ab0b-e4ea37288664\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 64561338134140\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-09T13:52:39.000+0000\n        )\n\n    [publishedAt] => 2025-09-09T14:20:38.000Z\n    [csn] => 5103159758\n)\n
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-09 14:20:40] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '64561338134140', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '64561338134140', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
