[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:20]  Webhook request received at 2025-09-09 14:20:40
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:36]  Provided signature: sha256=12a199c09c98b4bb8ac654342bcfde65c811504cff8ec55b1db71495f3b2804b
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:37]  Calculated signature: sha256=188f0e342df4aa7aef7c784db7d2676a8f29420760be7710c656d8e2df7f78a5
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f5070699f97d02e7\n    [X-B3-Traceid] => 68c037b65095f7d876faf2c94ab4c2fa\n    [B3] => 68c037b65095f7d876faf2c94ab4c2fa-f5070699f97d02e7-1\n    [Traceparent] => 00-68c037b65095f7d876faf2c94ab4c2fa-f5070699f97d02e7-01\n    [X-Amzn-Trace-Id] => Root=1-68c037b6-5095f7d876faf2c94ab4c2fa;Parent=f5070699f97d02e7;Sampled=1\n    [X-Adsk-Signature] => sha256=12a199c09c98b4bb8ac654342bcfde65c811504cff8ec55b1db71495f3b2804b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c9a84bea-7d20-446f-ab0b-e4ea37288664\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"c9a84bea-7d20-446f-ab0b-e4ea37288664","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"64561338134140","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-09T13:52:39.000+0000"},"publishedAt":"2025-09-09T14:20:38.000Z","csn":"5103159758"}
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:20]  Webhook request received at 2025-09-09 14:20:40
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:36]  Provided signature: sha256=188f0e342df4aa7aef7c784db7d2676a8f29420760be7710c656d8e2df7f78a5
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:37]  Calculated signature: sha256=188f0e342df4aa7aef7c784db7d2676a8f29420760be7710c656d8e2df7f78a5
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b158489933933bdd\n    [X-B3-Traceid] => 68c037b65095f7d876faf2c94ab4c2fa\n    [B3] => 68c037b65095f7d876faf2c94ab4c2fa-b158489933933bdd-1\n    [Traceparent] => 00-68c037b65095f7d876faf2c94ab4c2fa-b158489933933bdd-01\n    [X-Amzn-Trace-Id] => Root=1-68c037b6-5095f7d876faf2c94ab4c2fa;Parent=b158489933933bdd;Sampled=1\n    [X-Adsk-Signature] => sha256=188f0e342df4aa7aef7c784db7d2676a8f29420760be7710c656d8e2df7f78a5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c9a84bea-7d20-446f-ab0b-e4ea37288664\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-09 14:20:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"c9a84bea-7d20-446f-ab0b-e4ea37288664","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"64561338134140","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-09T13:52:39.000+0000"},"publishedAt":"2025-09-09T14:20:38.000Z","csn":"5103159758"}
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:20]  Webhook request received at 2025-09-09 14:24:28
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:36]  Provided signature: sha256=0487bca192580ca805eef5b123707f037861b7882a5873a7c62ea2fe1b4a68c2
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:37]  Calculated signature: sha256=0bdd22c061fc4e12e9448494e8f0c8d3fc9ba196b28640fb430b8ce42fc72a4c
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 921f2645222c98a9\n    [X-B3-Traceid] => 68c0389a70861b4305fb0e2806124408\n    [B3] => 68c0389a70861b4305fb0e2806124408-921f2645222c98a9-1\n    [Traceparent] => 00-68c0389a70861b4305fb0e2806124408-921f2645222c98a9-01\n    [X-Amzn-Trace-Id] => Root=1-68c0389a-70861b4305fb0e2806124408;Parent=921f2645222c98a9;Sampled=1\n    [X-Adsk-Signature] => sha256=0487bca192580ca805eef5b123707f037861b7882a5873a7c62ea2fe1b4a68c2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757427866068-72648721938470-9033841756-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757427866068-72648721938470-9033841756-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72648721938470","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-09T14:24:26.068Z"},"publishedAt":"2025-09-09T14:24:26.000Z","csn":"5103159758"}
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:20]  Webhook request received at 2025-09-09 14:24:28
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:36]  Provided signature: sha256=0bdd22c061fc4e12e9448494e8f0c8d3fc9ba196b28640fb430b8ce42fc72a4c
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:37]  Calculated signature: sha256=0bdd22c061fc4e12e9448494e8f0c8d3fc9ba196b28640fb430b8ce42fc72a4c
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2e3b4e1b4b5b475d\n    [X-B3-Traceid] => 68c0389a70861b4305fb0e2806124408\n    [B3] => 68c0389a70861b4305fb0e2806124408-2e3b4e1b4b5b475d-1\n    [Traceparent] => 00-68c0389a70861b4305fb0e2806124408-2e3b4e1b4b5b475d-01\n    [X-Amzn-Trace-Id] => Root=1-68c0389a-70861b4305fb0e2806124408;Parent=2e3b4e1b4b5b475d;Sampled=1\n    [X-Adsk-Signature] => sha256=0bdd22c061fc4e12e9448494e8f0c8d3fc9ba196b28640fb430b8ce42fc72a4c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757427866068-72648721938470-9033841756-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-09 14:24:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757427866068-72648721938470-9033841756-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72648721938470","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-09T14:24:26.068Z"},"publishedAt":"2025-09-09T14:24:26.000Z","csn":"5103159758"}
