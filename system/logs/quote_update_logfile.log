[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:34
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 6f60671c-349e-4743-a214-286b398e4676\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979451\n            [transactionId] => 86140b3f-8aa6-542a-ae56-6a35e0d76b4b\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979451 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:27.942Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:35
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 6f60671c-349e-4743-a214-286b398e4676\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979451\n            [transactionId] => 86140b3f-8aa6-542a-ae56-6a35e0d76b4b\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979451 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:27.942Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979451', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979451', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:37
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 4eb86490-f9bd-4b17-830c-3ee425b5b84a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979508\n            [transactionId] => 53be095e-f7bc-5167-8f04-68549b3bbc39\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979508 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:31.875Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:34.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:37
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 4eb86490-f9bd-4b17-830c-3ee425b5b84a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979508\n            [transactionId] => 53be095e-f7bc-5167-8f04-68549b3bbc39\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979508 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:31.875Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:34.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:37] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979508', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979508', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:42
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c0d1e570-174c-405c-8972-1056eef53032\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979616\n            [transactionId] => 576afb74-fd9f-556a-ac25-9a7e8d16f607\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979616 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:39.443Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:40.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:42
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c0d1e570-174c-405c-8972-1056eef53032\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979616\n            [transactionId] => 576afb74-fd9f-556a-ac25-9a7e8d16f607\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979616 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:39.443Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:40.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:42] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979616', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979616', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:44
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 4a02768d-08bc-4c9f-8aa5-790465bb2c99\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979541\n            [transactionId] => 6d41fdac-6f14-5bb5-b4fa-9ca60280c767\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979541 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:39.134Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:42.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:44
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 4a02768d-08bc-4c9f-8aa5-790465bb2c99\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979541\n            [transactionId] => 6d41fdac-6f14-5bb5-b4fa-9ca60280c767\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979541 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:39.134Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:42.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:44] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979541', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979541', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:46
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 26d1372d-8149-4d0e-be89-da558b6e2591\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979601\n            [transactionId] => 87e453b3-75e8-5ae5-9b2a-32b905e7205d\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979601 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:39.454Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:46
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => fdf10e79-c870-42e1-a902-70516746da1d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-980363\n            [transactionId] => afbabe9a-6dc7-5fc5-8d17-df90f5a88454\n            [quoteStatus] => Expired\n            [message] => Quote# Q-980363 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:43.621Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:46
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => fdf10e79-c870-42e1-a902-70516746da1d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-980363\n            [transactionId] => afbabe9a-6dc7-5fc5-8d17-df90f5a88454\n            [quoteStatus] => Expired\n            [message] => Quote# Q-980363 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:43.621Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-980363', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-980363', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:46
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 69ab96d5-4cf9-472a-88cc-1324a087ed28\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979487\n            [transactionId] => 0c4a4009-2fe6-507e-a177-210344555a42\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979487 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:43.701Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:46
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 26d1372d-8149-4d0e-be89-da558b6e2591\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979601\n            [transactionId] => 87e453b3-75e8-5ae5-9b2a-32b905e7205d\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979601 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:39.454Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979601', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979601', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:46
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 69ab96d5-4cf9-472a-88cc-1324a087ed28\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979487\n            [transactionId] => 0c4a4009-2fe6-507e-a177-210344555a42\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979487 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:43.701Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979487', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979487', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:47
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 248e670b-20bd-407c-9963-1ded957e1f1b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979608\n            [transactionId] => 19220073-ed75-555b-9159-61799d4006dc\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979608 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:44.626Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:45.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:47] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-03 23:00:48
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 248e670b-20bd-407c-9963-1ded957e1f1b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-979608\n            [transactionId] => 19220073-ed75-555b-9159-61799d4006dc\n            [quoteStatus] => Expired\n            [message] => Quote# Q-979608 status changed to Expired.\n            [modifiedAt] => 2025-09-03T23:00:44.626Z\n        )\n\n    [publishedAt] => 2025-09-03T23:00:45.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-03 23:00:48] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-979608', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-979608', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 07:58:55] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 07:58:55
[quote_update] [2025-09-04 07:58:55] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 07:58:55] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => dca324fa-965c-4bf3-bc1d-69453310e193\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059389\n            [transactionId] => 10d15e62-c469-5d81-aeeb-3f518c9b6f9a\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1059389 status changed to Draft.\n            [modifiedAt] => 2025-09-04T07:58:53.161Z\n        )\n\n    [publishedAt] => 2025-09-04T07:58:53.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 07:58:56] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 07:58:56
[quote_update] [2025-09-04 07:58:56] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 07:58:56] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => dca324fa-965c-4bf3-bc1d-69453310e193\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059389\n            [transactionId] => 10d15e62-c469-5d81-aeeb-3f518c9b6f9a\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1059389 status changed to Draft.\n            [modifiedAt] => 2025-09-04T07:58:53.161Z\n        )\n\n    [publishedAt] => 2025-09-04T07:58:53.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 07:58:57] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 07:58:57 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3723\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 203c90d3-6267-4dd2-a74e-0cd384764445\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QXf-NEIaoAMEtJw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b946c0-194db97575e81dfb2d8d0d72\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1059389\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T08:58:50+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => TURNBULL SURVEYING\n                            [addressLine1] => 1 Tankersley Lane\n                            [addressLine2] => Hoyland\n                            [city] => Barnsley\n                            [stateProvinceCode] => \n                            [stateProvince] => SOUTH YORKSHIRE\n                            [postalCode] => S74 0DR\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Nicolas\n                            [lastName] => Turnbull\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-17\n                                    [endDate] => 2026-09-16\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55146233514144\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-16\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-17\n                                                            [endDate] => 2026-09-16\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1059389\n)\n
[quote_update] [2025-09-04 07:58:57] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 07:58:57 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3723\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 7ab73f1e-b973-4f0b-a046-b34e755b8318\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QXf-MEbMIAMEBWQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b946c0-5641229e1c97b82835b90bed\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1059389\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T08:58:50+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => TURNBULL SURVEYING\n                            [addressLine1] => 1 Tankersley Lane\n                            [addressLine2] => Hoyland\n                            [city] => Barnsley\n                            [stateProvinceCode] => \n                            [stateProvince] => SOUTH YORKSHIRE\n                            [postalCode] => S74 0DR\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Nicolas\n                            [lastName] => Turnbull\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-17\n                                    [endDate] => 2026-09-16\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55146233514144\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-16\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-17\n                                                            [endDate] => 2026-09-16\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1059389\n)\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 08:00:02
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => afc55526-41e1-43d4-8848-d8d1d5bb8018\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059389\n            [transactionId] => 10d15e62-c469-5d81-aeeb-3f518c9b6f9a\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1059389 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T08:00:00.005Z\n        )\n\n    [publishedAt] => 2025-09-04T08:00:00.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 08:00:02
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => afc55526-41e1-43d4-8848-d8d1d5bb8018\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059389\n            [transactionId] => 10d15e62-c469-5d81-aeeb-3f518c9b6f9a\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1059389 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T08:00:00.005Z\n        )\n\n    [publishedAt] => 2025-09-04T08:00:00.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 08:00:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1059389', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059389', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 08:11:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 08:11:19
[quote_update] [2025-09-04 08:11:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 08:11:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d0d93d17-c4e4-41ad-af71-d5484ac3df5b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => c5af7ee9-5dbb-5794-8787-1fda166e9d24\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T08:11:16.919Z\n        )\n\n    [publishedAt] => 2025-09-04T08:11:17.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 08:11:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 08:11:19
[quote_update] [2025-09-04 08:11:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 08:11:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d0d93d17-c4e4-41ad-af71-d5484ac3df5b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => c5af7ee9-5dbb-5794-8787-1fda166e9d24\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T08:11:16.919Z\n        )\n\n    [publishedAt] => 2025-09-04T08:11:17.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 08:11:21] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 08:11:21 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1591\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => acabab35-72ec-4b69-b461-ffe802c24f26\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QXhybHvAoAMELBQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b949a8-4178768112fb533b13d2232a\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T09:11:15+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => RE-FORM LANDSCAPE ARCHITECTURE\n                            [addressLine1] => Tower Works 2 Globe Road\n                            [city] => Leeds\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => LS11 5QG\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Sroboshi\n                            [lastName] => Das\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 08:11:21] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 08:11:21 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1690\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 05b44be8-e0ca-49ba-a224-7ffd0417e648\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QXhybHOkoAMEhJA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b949a8-0203a3104d94c34662db898e\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T09:11:15+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => RE-FORM LANDSCAPE ARCHITECTURE\n                            [addressLine1] => Tower Works 2 Globe Road\n                            [city] => Leeds\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => LS11 5QG\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Sroboshi\n                            [lastName] => Das\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 08:12:58
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 14f9c1dc-620c-419f-a586-af4dbba6a7cc\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => c5af7ee9-5dbb-5794-8787-1fda166e9d24\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T08:12:55.529Z\n        )\n\n    [publishedAt] => 2025-09-04T08:12:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 08:12:58
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 14f9c1dc-620c-419f-a586-af4dbba6a7cc\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => c5af7ee9-5dbb-5794-8787-1fda166e9d24\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T08:12:55.529Z\n        )\n\n    [publishedAt] => 2025-09-04T08:12:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 08:12:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 09:07:23] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 09:07:23
[quote_update] [2025-09-04 09:07:23] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 09:07:23] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ac3a6cc9-0a8b-4ba1-8cd5-f835c63d02ac\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059754\n            [transactionId] => a1aec4fe-0822-5920-8bb7-a5cd5123def4\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1059754 status changed to Draft.\n            [modifiedAt] => 2025-09-04T09:07:20.939Z\n        )\n\n    [publishedAt] => 2025-09-04T09:07:21.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 09:07:23] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 09:07:23
[quote_update] [2025-09-04 09:07:23] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 09:07:23] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ac3a6cc9-0a8b-4ba1-8cd5-f835c63d02ac\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059754\n            [transactionId] => a1aec4fe-0822-5920-8bb7-a5cd5123def4\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1059754 status changed to Draft.\n            [modifiedAt] => 2025-09-04T09:07:20.939Z\n        )\n\n    [publishedAt] => 2025-09-04T09:07:21.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 09:07:25] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 09:07:25 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3597\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c88c3724-12a0-49d4-a733-4edfa72ae7aa\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QXqAAF8ZoAMEV3Q=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b956cc-3379ad3e4f85472b6ae26fea\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1059754\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T10:07:18+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Broad Planning and Architecture\n                            [addressLine1] => Sandon Road\n                            [city] => Wolverhampton\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => WV10 6EL\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Sheila\n                            [lastName] => Porter\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-29\n                                    [endDate] => 2026-09-28\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69600139405590\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-28\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-29\n                                                            [endDate] => 2026-09-28\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1059754\n)\n
[quote_update] [2025-09-04 09:07:25] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 09:07:25 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3597\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 08dd8ccc-3484-4181-b83a-ba5b35c019aa\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QXqAAEieIAMEdng=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b956cc-115801865a8e6844063b4456\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1059754\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T10:07:18+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Broad Planning and Architecture\n                            [addressLine1] => Sandon Road\n                            [city] => Wolverhampton\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => WV10 6EL\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Sheila\n                            [lastName] => Porter\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-29\n                                    [endDate] => 2026-09-28\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69600139405590\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-28\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-29\n                                                            [endDate] => 2026-09-28\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1059754\n)\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 09:08:24
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 718530cb-d9ce-43d7-9981-d0575e3b7bcb\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059754\n            [transactionId] => a1aec4fe-0822-5920-8bb7-a5cd5123def4\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1059754 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T09:08:22.375Z\n        )\n\n    [publishedAt] => 2025-09-04T09:08:22.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 09:08:24
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 718530cb-d9ce-43d7-9981-d0575e3b7bcb\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1059754\n            [transactionId] => a1aec4fe-0822-5920-8bb7-a5cd5123def4\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1059754 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T09:08:22.375Z\n        )\n\n    [publishedAt] => 2025-09-04T09:08:22.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 09:08:24] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1059754', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1059754', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 10:49:57] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 10:49:57
[quote_update] [2025-09-04 10:49:57] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 10:49:57] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 670d4050-efe6-4f5f-876c-169483a3f91b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e43ae601-2a7d-5ec1-a777-87325b6dceb3\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T10:49:54.342Z\n        )\n\n    [publishedAt] => 2025-09-04T10:49:54.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 10:49:58] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 10:49:58 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1697\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 13f8392f-daa7-44ba-a31f-17bc56c2b458\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QX5BgGXQoAMEZLQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b96ed6-6a75e4cf67ee649469e9a6b5\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T11:49:52+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Northumbria Healthcare NHS Foundation Trust\n                            [addressLine1] => Unit 7 Northumbria House 7-8 Silver\n                            [addressLine2] => Fox Way Cobalt Business Park\n                            [city] => Newcastle Upon Tyne\n                            [stateProvinceCode] => \n                            [stateProvince] => TYNE AND WEAR\n                            [postalCode] => NE27 0QJ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Gillian\n                            [lastName] => Finn\n                            [phone] => +447815504919\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 10:50:01
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 670d4050-efe6-4f5f-876c-169483a3f91b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e43ae601-2a7d-5ec1-a777-87325b6dceb3\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T10:49:54.342Z\n        )\n\n    [publishedAt] => 2025-09-04T10:49:54.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Draft';\n
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Draft';\n
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Draft';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 10:50:01] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Draft';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 10:50:51
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7e25d89b-3332-4026-a360-b51743048433\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e43ae601-2a7d-5ec1-a777-87325b6dceb3\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T10:50:48.792Z\n        )\n\n    [publishedAt] => 2025-09-04T10:50:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 10:50:51
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7e25d89b-3332-4026-a360-b51743048433\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e43ae601-2a7d-5ec1-a777-87325b6dceb3\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T10:50:48.792Z\n        )\n\n    [publishedAt] => 2025-09-04T10:50:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 10:50:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 12:24:10] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:24:10
[quote_update] [2025-09-04 12:24:10] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:24:10] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1b50bd72-02a8-4f01-908f-111bd47a3be8\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1060637 status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:24:08.071Z\n        )\n\n    [publishedAt] => 2025-09-04T12:24:08.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:24:10] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:24:10
[quote_update] [2025-09-04 12:24:10] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:24:10] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1b50bd72-02a8-4f01-908f-111bd47a3be8\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1060637 status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:24:08.071Z\n        )\n\n    [publishedAt] => 2025-09-04T12:24:08.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:24:12] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:24:12 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3705\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => b6b53cec-3c2c-4721-b0fe-75feb165f5a4\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYG04HLboAMEYTw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b984eb-690df1282f599b3f38e907a6\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1060637\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:24:05+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Surveying Solutions Ltd\n                            [addressLine1] => 34-36 Rose Street North Lane\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => EH2 2NP\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => C\n                            [lastName] => Sutherland\n                            [phone] => +*********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-17\n                                    [endDate] => 2026-09-16\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55536230799844\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-16\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-17\n                                                            [endDate] => 2026-09-16\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1060637\n)\n
[quote_update] [2025-09-04 12:24:12] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:24:12 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3606\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 3c042d99-a518-4740-93d7-3f0c72970ef8\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYG04EQboAMEpsg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b984eb-02c0ccd40d80a1031d244396\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1060637\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:24:05+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Surveying Solutions Ltd\n                            [addressLine1] => 34-36 Rose Street North Lane\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => EH2 2NP\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => C\n                            [lastName] => Sutherland\n                            [phone] => +*********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-17\n                                    [endDate] => 2026-09-16\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55536230799844\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-16\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-17\n                                                            [endDate] => 2026-09-16\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1060637\n)\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:24:50
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ca4c56e5-2413-481f-a279-1c22ef83af6e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1060637 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:24:47.695Z\n        )\n\n    [publishedAt] => 2025-09-04T12:24:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:24:50
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ca4c56e5-2413-481f-a279-1c22ef83af6e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1060637 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:24:47.695Z\n        )\n\n    [publishedAt] => 2025-09-04T12:24:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 12:24:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 12:30:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:30:15
[quote_update] [2025-09-04 12:30:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:30:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 85f42dc0-3d2e-4757-ad94-7e2b830523cf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1060665 status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:30:12.421Z\n        )\n\n    [publishedAt] => 2025-09-04T12:30:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:30:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:30:15
[quote_update] [2025-09-04 12:30:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:30:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 85f42dc0-3d2e-4757-ad94-7e2b830523cf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1060665 status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:30:12.421Z\n        )\n\n    [publishedAt] => 2025-09-04T12:30:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:30:16] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:30:16 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3598\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 4d567702-1a38-4c62-a609-06056370fe79\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYHt0FvRoAMESIQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b98658-062c6ee84ec368ba1e22b42e\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1060665\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:30:10+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => CRS Engineering Ltd\n                            [addressLine1] => Hatfield Drive\n                            [city] => Cramlington\n                            [stateProvinceCode] => \n                            [stateProvince] => NORTHUMBERLAND\n                            [postalCode] => NE23 7TU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Simpson\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-11\n                                    [endDate] => 2026-09-10\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55536338787291\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-10\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-11\n                                                            [endDate] => 2026-09-10\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1060665\n)\n
[quote_update] [2025-09-04 12:30:16] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:30:16 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3598\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 58321323-b924-48c5-ae80-6a61a935a616\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYHt0GHLoAMEU5g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b98658-414ccb2607e57ccf14943449\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1060665\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:30:10+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => CRS Engineering Ltd\n                            [addressLine1] => Hatfield Drive\n                            [city] => Cramlington\n                            [stateProvinceCode] => \n                            [stateProvince] => NORTHUMBERLAND\n                            [postalCode] => NE23 7TU\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Simpson\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-11\n                                    [endDate] => 2026-09-10\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 55536338787291\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-10\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-04\n                                                            [startDate] => 2025-09-11\n                                                            [endDate] => 2026-09-10\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1060665\n)\n
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:30:50
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 052ef159-18ea-434a-af24-35ed7cab00e8\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1060665 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:30:48.240Z\n        )\n\n    [publishedAt] => 2025-09-04T12:30:48.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 12:30:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:30:51
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 052ef159-18ea-434a-af24-35ed7cab00e8\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1060665 status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:30:48.240Z\n        )\n\n    [publishedAt] => 2025-09-04T12:30:48.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 12:30:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 12:37:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:37:49
[quote_update] [2025-09-04 12:37:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:37:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f8796372-74a9-44cd-ae5d-663a41b06214\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => b9f1d72c-632f-5629-8e4d-b61fe97e7f72\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:37:47.499Z\n        )\n\n    [publishedAt] => 2025-09-04T12:37:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:37:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:37:49
[quote_update] [2025-09-04 12:37:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:37:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f8796372-74a9-44cd-ae5d-663a41b06214\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => b9f1d72c-632f-5629-8e4d-b61fe97e7f72\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:37:47.499Z\n        )\n\n    [publishedAt] => 2025-09-04T12:37:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:37:51] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:37:51 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1568\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 3c251245-e320-4ef4-a0e4-49e48aa99e7f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYI03FPTIAMEOjA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b9881e-57e064a26c371942392cfd83\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:37:45+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => BLACKFRIARS STAGING Ltd\n                            [addressLine1] => 39-69 Westmoor Street\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => SE7 8NR\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Beau\n                            [lastName] => Oladigbolu\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 12:37:51] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:37:51 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1568\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 08985c1d-ec0a-4803-974a-faedf19aa327\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYI04FhuoAMEdTQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b9881e-3f2cb50504b0f37829897f40\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:37:45+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => BLACKFRIARS STAGING Ltd\n                            [addressLine1] => 39-69 Westmoor Street\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => SE7 8NR\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Beau\n                            [lastName] => Oladigbolu\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:39:02
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d901877a-adc0-48ff-9d8e-f94ab5e17b93\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1055070\n            [transactionId] => e49bfa01-7172-5b26-ac83-9b308b32f666\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1055070 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-04T12:38:59.361Z\n        )\n\n    [publishedAt] => 2025-09-04T12:38:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:39:02
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d901877a-adc0-48ff-9d8e-f94ab5e17b93\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1055070\n            [transactionId] => e49bfa01-7172-5b26-ac83-9b308b32f666\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1055070 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-04T12:38:59.361Z\n        )\n\n    [publishedAt] => 2025-09-04T12:38:59.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 12:39:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:39:06
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 4c2ef236-6c67-40aa-9aae-724cd83534a6\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1055070\n            [transactionId] => e49bfa01-7172-5b26-ac83-9b308b32f666\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1055070 status changed to Ordered.\n            [modifiedAt] => 2025-09-04T12:39:04.069Z\n        )\n\n    [publishedAt] => 2025-09-04T12:39:04.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:39:06
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 4c2ef236-6c67-40aa-9aae-724cd83534a6\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1055070\n            [transactionId] => e49bfa01-7172-5b26-ac83-9b308b32f666\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1055070 status changed to Ordered.\n            [modifiedAt] => 2025-09-04T12:39:04.069Z\n        )\n\n    [publishedAt] => 2025-09-04T12:39:04.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 12:39:06] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1055070', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1055070', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 12:47:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:47:14
[quote_update] [2025-09-04 12:47:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:47:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 470bd65c-d765-44e8-a573-4658fa992015\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d420f78a-fd03-5343-9ef9-96b9717f82b8\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:47:11.129Z\n        )\n\n    [publishedAt] => 2025-09-04T12:47:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:47:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:47:14
[quote_update] [2025-09-04 12:47:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:47:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 470bd65c-d765-44e8-a573-4658fa992015\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d420f78a-fd03-5343-9ef9-96b9717f82b8\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:47:11.129Z\n        )\n\n    [publishedAt] => 2025-09-04T12:47:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:47:15] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:47:15 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1558\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => bcd03cdf-109a-466e-8aa7-6600f2bce52e\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYKNBFWuIAMEUPA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b98a52-6d6f64af6e834e596d408aee\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:47:09+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Zotefoams PLC\n                            [addressLine1] => 675 Mitcham Road\n                            [city] => Croydon\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CR9 3AL\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Vijay\n                            [lastName] => Latchman\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 12:47:15] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:47:15 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1657\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 4d6b449c-a66b-43a1-9f66-87700ecfde20\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYKNCGkioAMEdgA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b98a53-557a570c1fa5a4b275d538cd\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:47:09+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Zotefoams PLC\n                            [addressLine1] => 675 Mitcham Road\n                            [city] => Croydon\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CR9 3AL\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Vijay\n                            [lastName] => Latchman\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:48:46
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 929685da-ced2-425f-8797-f1fe122fb692\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d420f78a-fd03-5343-9ef9-96b9717f82b8\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:48:43.899Z\n        )\n\n    [publishedAt] => 2025-09-04T12:48:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:48:46
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 929685da-ced2-425f-8797-f1fe122fb692\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => d420f78a-fd03-5343-9ef9-96b9717f82b8\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:48:43.899Z\n        )\n\n    [publishedAt] => 2025-09-04T12:48:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 12:48:46] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 12:48:47] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:48:47
[quote_update] [2025-09-04 12:48:47] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:48:47] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => bd2e9c70-f682-4e6b-bb16-52caef645be1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 60c26524-bd09-5aa4-a8d4-041731d34258\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:48:44.090Z\n        )\n\n    [publishedAt] => 2025-09-04T12:48:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:48:47] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:48:47
[quote_update] [2025-09-04 12:48:47] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:48:47] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => bd2e9c70-f682-4e6b-bb16-52caef645be1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 60c26524-bd09-5aa4-a8d4-041731d34258\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T12:48:44.090Z\n        )\n\n    [publishedAt] => 2025-09-04T12:48:44.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:48:48] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:48:48 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1558\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 2ff388a5-ed20-460a-825e-28bf67c9d792\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYKbiGJDoAMECKg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b98aaf-428bec2960565d682aa2f09c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:48:42+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Zotefoams PLC\n                            [addressLine1] => 675 Mitcham Road\n                            [city] => Croydon\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CR9 3AL\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Vijay\n                            [lastName] => Latchman\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 12:48:48] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 12:48:48 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1558\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 22c8cc17-2823-49d1-ac1b-0a8cfba48c84\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYKbjEwnoAMEmpw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b98aaf-236ff5fa5ea69f8862f045fd\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T13:48:42+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Zotefoams PLC\n                            [addressLine1] => 675 Mitcham Road\n                            [city] => Croydon\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => CR9 3AL\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Vijay\n                            [lastName] => Latchman\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:50:04
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d6731a12-453a-4133-8c17-a06719c9c570\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 60c26524-bd09-5aa4-a8d4-041731d34258\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:50:01.564Z\n        )\n\n    [publishedAt] => 2025-09-04T12:50:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 12:50:04
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d6731a12-453a-4133-8c17-a06719c9c570\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 60c26524-bd09-5aa4-a8d4-041731d34258\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T12:50:01.564Z\n        )\n\n    [publishedAt] => 2025-09-04T12:50:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 12:50:04] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 13:07:56
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 63e698ff-de20-4b93-b32b-c15e959d9e89\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1018933\n            [transactionId] => b9acd212-0136-5bbb-8197-b30d876e92ec\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1018933 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-04T13:07:54.097Z\n        )\n\n    [publishedAt] => 2025-09-04T13:07:54.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 13:07:56
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 63e698ff-de20-4b93-b32b-c15e959d9e89\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1018933\n            [transactionId] => b9acd212-0136-5bbb-8197-b30d876e92ec\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1018933 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-04T13:07:54.097Z\n        )\n\n    [publishedAt] => 2025-09-04T13:07:54.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 13:07:56] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 13:07:58
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => a0987115-3684-4d86-b0c5-402811622a74\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1018933\n            [transactionId] => b9acd212-0136-5bbb-8197-b30d876e92ec\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1018933 status changed to Ordered.\n            [modifiedAt] => 2025-09-04T13:07:55.849Z\n        )\n\n    [publishedAt] => 2025-09-04T13:07:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 13:07:58
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => a0987115-3684-4d86-b0c5-402811622a74\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1018933\n            [transactionId] => b9acd212-0136-5bbb-8197-b30d876e92ec\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1018933 status changed to Ordered.\n            [modifiedAt] => 2025-09-04T13:07:55.849Z\n        )\n\n    [publishedAt] => 2025-09-04T13:07:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 13:07:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1018933', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1018933', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 14:00:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:00:48
[quote_update] [2025-09-04 14:00:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:00:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ab941a7e-5f9b-451a-9324-ca40aef3d315\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => a9c0b587-94f8-5432-bb86-d4b409451498\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T14:00:46.137Z\n        )\n\n    [publishedAt] => 2025-09-04T14:00:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:00:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:00:48
[quote_update] [2025-09-04 14:00:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:00:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ab941a7e-5f9b-451a-9324-ca40aef3d315\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => a9c0b587-94f8-5432-bb86-d4b409451498\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T14:00:46.137Z\n        )\n\n    [publishedAt] => 2025-09-04T14:00:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:00:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 14:00:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 702d4ad2-c53b-40c4-a35f-36883107a300\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYU-2FkvIAMEttA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b99b91-6191f7a83bb809465e78c237\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T15:00:44+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NGP Architecture Ltd\n                            [addressLine1] => Federation House 222-224\n                            [addressLine2] => Queensferry Road\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => MIDLOTHIAN\n                            [postalCode] => EH4 2BN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Gray\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 14:00:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 14:00:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c0c73f26-a8c2-45d3-904d-12ea2083d52f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYU-2EWnoAMETRQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b99b91-2ec6600e70c6bf6365c0a53c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T15:00:44+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NGP Architecture Ltd\n                            [addressLine1] => Federation House 222-224\n                            [addressLine2] => Queensferry Road\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => MIDLOTHIAN\n                            [postalCode] => EH4 2BN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Gray\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:02:06
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8f23ab3e-a75f-451a-9712-7a8045dccbbf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => a9c0b587-94f8-5432-bb86-d4b409451498\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T14:02:03.525Z\n        )\n\n    [publishedAt] => 2025-09-04T14:02:04.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:02:06
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8f23ab3e-a75f-451a-9712-7a8045dccbbf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => a9c0b587-94f8-5432-bb86-d4b409451498\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T14:02:03.525Z\n        )\n\n    [publishedAt] => 2025-09-04T14:02:04.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 14:02:06] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 14:02:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:02:34
[quote_update] [2025-09-04 14:02:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:02:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ed9f257b-bf21-49a5-98a0-6bb296bfbf39\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bb30285f-8fec-5cce-857c-496c11c3fbb8\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T14:02:31.041Z\n        )\n\n    [publishedAt] => 2025-09-04T14:02:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:02:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:02:34
[quote_update] [2025-09-04 14:02:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:02:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ed9f257b-bf21-49a5-98a0-6bb296bfbf39\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bb30285f-8fec-5cce-857c-496c11c3fbb8\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T14:02:31.041Z\n        )\n\n    [publishedAt] => 2025-09-04T14:02:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:02:35] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 14:02:35 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1714\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => a8eb1d42-49df-4bca-887c-a4f60bb49711\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYVPQGj7IAMEQqg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b99bfa-5d8acc1b454884284399d62f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T15:02:29+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NGP Architecture Ltd\n                            [addressLine1] => Federation House 222-224\n                            [addressLine2] => Queensferry Road\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => MIDLOTHIAN\n                            [postalCode] => EH4 2BN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Gray\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 14:02:35] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 14:02:35 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => f6a54e99-7eed-4a4d-975b-a4ae38f96bde\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYVPREKfIAMEC4A=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b99bfa-71880aca044e8f50065c200c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T15:02:29+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NGP Architecture Ltd\n                            [addressLine1] => Federation House 222-224\n                            [addressLine2] => Queensferry Road\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => MIDLOTHIAN\n                            [postalCode] => EH4 2BN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Gray\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:03:19
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => e7ae52e6-1e68-4be5-8067-3a56e3b65fba\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bb30285f-8fec-5cce-857c-496c11c3fbb8\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T14:03:16.418Z\n        )\n\n    [publishedAt] => 2025-09-04T14:03:16.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:03:19
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => e7ae52e6-1e68-4be5-8067-3a56e3b65fba\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bb30285f-8fec-5cce-857c-496c11c3fbb8\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T14:03:16.418Z\n        )\n\n    [publishedAt] => 2025-09-04T14:03:16.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 14:03:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 14:04:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:04:33
[quote_update] [2025-09-04 14:04:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:04:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c59c0b52-b90a-4cf4-971e-7f8bf3bb97d3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bca8bd6c-c778-535b-a31f-dc5f59e7601c\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T14:04:30.987Z\n        )\n\n    [publishedAt] => 2025-09-04T14:04:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:04:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:04:33
[quote_update] [2025-09-04 14:04:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:04:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c59c0b52-b90a-4cf4-971e-7f8bf3bb97d3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bca8bd6c-c778-535b-a31f-dc5f59e7601c\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-04T14:04:30.987Z\n        )\n\n    [publishedAt] => 2025-09-04T14:04:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:04:35] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 14:04:35 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 4a7c1443-25e3-4362-8ac4-8edb83f3daf7\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYVh8FXsIAMEusA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b99c72-39a4ad56027746bf45905d36\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T15:04:29+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NGP Architecture Ltd\n                            [addressLine1] => Federation House 222-224\n                            [addressLine2] => Queensferry Road\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => MIDLOTHIAN\n                            [postalCode] => EH4 2BN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Gray\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 14:04:35] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Thu, 04 Sep 2025 14:04:35 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => fb3f5eca-43f2-438b-b928-05a2bf57927f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QYVh-H6FIAMEKZg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68b99c72-1ab7f9e45dc255115271c658\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-04T15:04:29+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NGP Architecture Ltd\n                            [addressLine1] => Federation House 222-224\n                            [addressLine2] => Queensferry Road\n                            [city] => Edinburgh\n                            [stateProvinceCode] => \n                            [stateProvince] => MIDLOTHIAN\n                            [postalCode] => EH4 2BN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Chris\n                            [lastName] => Gray\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:05:25
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 31c80ade-aced-4245-a609-f4aa111faa95\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bca8bd6c-c778-535b-a31f-dc5f59e7601c\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T14:05:22.504Z\n        )\n\n    [publishedAt] => 2025-09-04T14:05:22.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 14:05:25
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 31c80ade-aced-4245-a609-f4aa111faa95\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => bca8bd6c-c778-535b-a31f-dc5f59e7601c\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-04T14:05:22.504Z\n        )\n\n    [publishedAt] => 2025-09-04T14:05:22.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 14:05:25] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 15:21:38
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 031535c3-1a3f-4f66-b85f-f7ffd6d74c7e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1060637 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-04T15:21:35.116Z\n        )\n\n    [publishedAt] => 2025-09-04T15:21:35.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 15:21:38
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 031535c3-1a3f-4f66-b85f-f7ffd6d74c7e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1060637 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-04T15:21:35.116Z\n        )\n\n    [publishedAt] => 2025-09-04T15:21:35.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 15:21:38] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 15:21:40
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 76fbafa7-32d5-4765-accc-142c8b2de162\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1060637 status changed to Ordered.\n            [modifiedAt] => 2025-09-04T15:21:38.054Z\n        )\n\n    [publishedAt] => 2025-09-04T15:21:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-04 15:21:40
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 76fbafa7-32d5-4765-accc-142c8b2de162\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060637\n            [transactionId] => d1ec2463-5045-5998-8869-4550d7643639\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1060637 status changed to Ordered.\n            [modifiedAt] => 2025-09-04T15:21:38.054Z\n        )\n\n    [publishedAt] => 2025-09-04T15:21:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-04 15:21:40] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060637', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060637', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 07:00:33
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 201afdcb-f391-4f33-953b-8046c992f95e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-761853\n            [transactionId] => 6d13451d-1fd0-5bef-85b0-258b697b47da\n            [quoteStatus] => Expired\n            [message] => Quote# Q-761853 status changed to Expired.\n            [modifiedAt] => 2025-09-05T07:00:31.300Z\n        )\n\n    [publishedAt] => 2025-09-05T07:00:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 07:00:33
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 201afdcb-f391-4f33-953b-8046c992f95e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-761853\n            [transactionId] => 6d13451d-1fd0-5bef-85b0-258b697b47da\n            [quoteStatus] => Expired\n            [message] => Quote# Q-761853 status changed to Expired.\n            [modifiedAt] => 2025-09-05T07:00:31.300Z\n        )\n\n    [publishedAt] => 2025-09-05T07:00:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 07:00:33] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-761853', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-761853', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 10:07:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 10:07:22
[quote_update] [2025-09-05 10:07:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 10:07:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 83dfc732-8a53-4fd8-a3fd-16730c2b11ed\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 1c823af9-d795-5fd1-ab25-5bcd6f01dbf7\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-05T10:07:19.145Z\n        )\n\n    [publishedAt] => 2025-09-05T10:07:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 10:07:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 10:07:22
[quote_update] [2025-09-05 10:07:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 10:07:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 83dfc732-8a53-4fd8-a3fd-16730c2b11ed\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 1c823af9-d795-5fd1-ab25-5bcd6f01dbf7\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-05T10:07:19.145Z\n        )\n\n    [publishedAt] => 2025-09-05T10:07:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 10:07:23] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 05 Sep 2025 10:07:23 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1665\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d50e43a0-d1a6-42c9-8eb8-bd570e64a46b\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QbFuUHBXoAMEKcw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68bab65b-06adb41a254585a15cbd3f20\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-05T11:07:17+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ETCH ASSOCIATES\n                            [addressLine1] => 1 Union Way\n                            [city] => Witney\n                            [stateProvinceCode] => \n                            [stateProvince] => OXFORDSHIRE\n                            [postalCode] => OX28 6HD\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Etch\n                            [lastName] => Associates\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-05 10:07:23] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Fri, 05 Sep 2025 10:07:23 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1665\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c25f5c7f-b357-4576-a432-927e4caeac2a\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QbFuVEUkIAMEo3g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68bab65b-73206a231489faa74851dbf3\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-05T11:07:17+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ETCH ASSOCIATES\n                            [addressLine1] => 1 Union Way\n                            [city] => Witney\n                            [stateProvinceCode] => \n                            [stateProvince] => OXFORDSHIRE\n                            [postalCode] => OX28 6HD\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Etch\n                            [lastName] => Associates\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 10:09:32
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9f7735b4-50ef-4341-b7cb-293807562cd9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 1c823af9-d795-5fd1-ab25-5bcd6f01dbf7\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-05T10:09:30.413Z\n        )\n\n    [publishedAt] => 2025-09-05T10:09:30.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 10:09:32] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 10:09:33
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9f7735b4-50ef-4341-b7cb-293807562cd9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 1c823af9-d795-5fd1-ab25-5bcd6f01dbf7\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-05T10:09:30.413Z\n        )\n\n    [publishedAt] => 2025-09-05T10:09:30.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 10:09:33] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
