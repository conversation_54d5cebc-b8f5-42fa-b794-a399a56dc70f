@props([
    'columns' => [],
    'table_name' => '',
    'db_table' => '',
    'callback' => '',
    'column_preferences' => [],
    'available_fields' => '',
    'current_data_source_type' => 'hardcoded',
    'current_data_source_id' => null,
    'keep_open' => false
])

@php
    // Process existing preferences to match new structure
    $column_structure = $column_preferences['structure'] ?? [];
    $hidden_columns = $column_preferences['hidden'] ?? [];

    // Debug removed for production

    // Use structure from preferences if it exists, otherwise generate from original columns
    if (!empty($column_structure)) {
        $processed_columns = $column_structure;
    } else {
        // Generate unique column IDs and prepare column structure
        $processed_columns = [];
        foreach ($columns as $index => $col) {
            $column_id = 'col_' . $index . '_' . md5($col['label']);
            $processed_columns[] = [
                'id' => $column_id,
                'label' => $col['label'],
                'field' => $col['field'],
                'filter' => $col['filter'] ?? false,
                'fields' => is_array($col['field']) ? $col['field'] : [$col['field']], // Always array for consistency
                'visible' => !in_array($column_id, $hidden_columns)
            ];
        }

        // Note: We don't automatically save the configuration here anymore to prevent
        // overwriting data source settings during table regeneration. Configuration
        // is only saved when there are explicit user actions (column changes, data source changes, etc.)
    }

    // Use the data source settings passed as props (these are already determined in the parent component)
    tcs_log("Column manager props: table=$table_name, type=$current_data_source_type, id=$current_data_source_id", 'data_table_saga');
    print_rr([
        'data_table_column_manager_start' => [
            'table_name' => $table_name,
            'available_fields' => $available_fields,
            'current_data_source_type' => $current_data_source_type,
            'current_data_source_id' => $current_data_source_id
        ]
    ], 'data_table_column_manager_start');
    // Get available data sources (cached to avoid repeated queries)
    static $cached_data_sources = null;
    if ($cached_data_sources === null) {
        $available_data_sources = [];
        try {
            $data_sources = database::table('autobooks_data_sources')
                ->where('status', '=', 'active')
                ->get();

            foreach ($data_sources as $source) {
                $available_data_sources[] = [
                    'id' => $source['id'],
                    'name' => $source['name'],
                    'description' => $source['description'],
                    'category' => $source['category'] ?? 'other'
                ];
            }
            $cached_data_sources = $available_data_sources;
        } catch (Exception $e) {
            // Handle error gracefully
            $cached_data_sources = [];
        }
    } else {
        $available_data_sources = $cached_data_sources;
    }

    // Cache database column lookups to avoid repeated queries
    static $cached_db_columns = [];
    $cache_key = $db_table ?: 'empty';

    if (!empty($available_fields)) {
        // Handle both array and string formats for available_fields
        if (is_array($available_fields)) {
            $available_field_list = $available_fields;
        } else {
            // Convert comma-separated string to array
            $available_field_list = array_filter(array_map('trim', explode(',', $available_fields)));
        }
    } else if (!empty($db_table)) {
        if (!isset($cached_db_columns[$cache_key])) {
            $cached_db_columns[$cache_key] = database::table($db_table)->getColumns();
        }
        $available_field_list = $cached_db_columns[$cache_key];
    } else {
        $available_field_list = [];
    }

    $column_string = count($available_field_list) > 0 ? '["' . implode('","', $available_field_list) . '"]' : '[]';
@endphp
<div id="column_manager_{{ $table_name }}"
     x-data="{
         open: {{ $keep_open ? 'true' : 'false' }},
         newColumnName: '',
         showAddColumn: false,

         toggleRenameInput(index) {
             const input = this.$refs['renameInput_' + index];
             if (!input) return;

             const isHidden = input.style.display === 'none' || !input.style.display;
             input.style.display = isHidden ? 'block' : 'none';

             if (isHidden) {
                 this.$nextTick(() => {
                     input.querySelector('input').focus();
                     input.querySelector('input').select();
                 });
             }
         },

         hideRenameInput(index) {
             const input = this.$refs['renameInput_' + index];
             if (input) input.style.display = 'none';
         },

         toggleActionForm(index) {
             const form = this.$refs['actionForm_' + index];
             if (!form) return;

             const isHidden = form.style.display === 'none' || !form.style.display;
             form.style.display = isHidden ? 'block' : 'none';
         },

         hideActionForm(index) {
             const form = this.$refs['actionForm_' + index];
             if (form) form.style.display = 'none';
         }

     }"

     class="relative inline-block text-left"
     @click.away="open = false">

    <!-- Column Manager Button -->
    <button type="button"
            @click="open = !open"
            class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            aria-expanded="false"
            aria-haspopup="true">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z"/>
        </svg>
        Columns
        <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5"/>
        </svg>
    </button>
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-20 mt-2 w-126 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
         style="height: 80vh;"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1"
         id="column_manager_panel_{{ $table_name }}"
    >

    <!-- Dropdown Panel -->
        <x-data-table-column-manager-panel
            :table_name="$table_name"
            :callback="$callback"
            :current_data_source_type="$current_data_source_type"
            :current_data_source_id="$current_data_source_id"
            :available_data_sources="$available_data_sources"
            :processed_columns="$processed_columns"
            :hidden_columns="$hidden_columns"
            :available_field_list="$available_field_list"
        />
    </div>
</div>