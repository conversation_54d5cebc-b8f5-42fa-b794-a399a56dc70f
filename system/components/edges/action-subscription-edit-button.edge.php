@props([
    'field_value' => '',
    'item' => [],
    'icon' => '',
    'action' => []
])

@php
    // Check conditional logic
    $show_button = true;
    
    if (isset($action['conditional'])) {
        $conditional = $action['conditional'];
        
        // Check data source condition
        if (isset($conditional['data_source'])) {
            $data_source = $item['data_source'] ?? '';
            if ($data_source !== $conditional['data_source']) {
                $show_button = false;
            }
        }
    }
    
    if (!$show_button) {
        return;
    }
    
    $label = $action['label'] ?? 'Edit';
@endphp

@if($show_button)
<button type="button"
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded hover:bg-indigo-100 hover:text-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1 mr-2"
        title="Edit subscription">
    @if($icon)
        <span class="mr-1">{!! $icon !!}</span>
    @endif
    {{ $label }}
</button>
@endif
