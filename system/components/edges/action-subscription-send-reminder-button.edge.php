@props([
    'field_value' => '',
    'item' => [],
    'icon' => '',
    'action' => []
])

@php
    // Check conditional logic
    $show_button = true;
    
    if (isset($action['conditional'])) {
        $conditional = $action['conditional'];
        
        // Check data source condition
        if (isset($conditional['data_source'])) {
            $data_source = $item['data_source'] ?? '';
            if ($data_source !== $conditional['data_source']) {
                $show_button = false;
            }
        }
        
        // Check date range condition
        if ($show_button && isset($conditional['date_range_check'])) {
            $date_check = $conditional['date_range_check'];
            $field_value = $item[$date_check['field']] ?? 0;
            $min = $date_check['min'] ?? null;
            $max = $date_check['max'] ?? null;
            
            if (($min !== null && $field_value < $min) || ($max !== null && $field_value > $max)) {
                $show_button = false;
            }
        }
    }
    
    if (!$show_button) {
        return;
    }
    
    // Extract action configuration
    $hx_post = $action['hx-post'] ?? '';
    $label = $action['label'] ?? 'Send Reminder';

    // If modal trigger is enabled, automatically set modal-specific HTMX attributes
    $modal_trigger = isset($action['modal_trigger']) && $action['modal_trigger'];
    if ($modal_trigger) {
        $hx_target = '#modal_body';
        $hx_swap = 'innerHTML';
        $alpine_click = '@click="showModal = true"';
    } else {
        // Use custom values if modal trigger is not enabled
        $hx_target = $action['hx-target'] ?? '#content';
        $hx_swap = $action['hx-swap'] ?? 'innerHTML';
        $alpine_click = '';
    }
    
    // Build hx-vals from data_fields configuration
    $hx_vals = [];
    if (isset($action['data_fields']) && is_array($action['data_fields'])) {
        foreach ($action['data_fields'] as $key => $field_name) {
            if (strpos($field_name, 'session:') === 0) {
                // Handle session variables
                $session_key = str_replace('session:', '', $field_name);
                $hx_vals[$key] = $_SESSION[$session_key] ?? '';
            } else {
                $hx_vals[$key] = $item[$field_name] ?? '';
            }
        }
    }
@endphp

@if($show_button)
<button type="button"
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-600 bg-orange-50 border border-orange-200 rounded hover:bg-orange-100 hover:text-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1 mr-2"
        hx-post="{{ $hx_post }}"
        hx-target="{{ $hx_target }}"
        hx-swap="{{ $hx_swap }}"
        hx-vals='{{ json_encode($hx_vals) }}'
        {!! $alpine_click !!}
        title="Send renewal reminder email">
    @if($icon)
        <span class="mr-1">{!! $icon !!}</span>
    @endif
    {{ $label }}
</button>
@endif
