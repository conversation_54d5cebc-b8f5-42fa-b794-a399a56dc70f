@props([
    'field_value' => '',
    'item' => [],
    'icon' => '',
    'action' => []
])

@php
    // Check conditional logic
    $show_button = true;
    
    if (isset($action['conditional'])) {
        $conditional = $action['conditional'];
        
        // Check data source condition
        if (isset($conditional['data_source'])) {
            $data_source = $item['data_source'] ?? '';
            if ($data_source !== $conditional['data_source']) {
                $show_button = false;
            }
        }
    }
    
    if (!$show_button) {
        return;
    }
    
    $label = $action['label'] ?? 'Delete';
@endphp

@if($show_button)
<button type="button"
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded hover:bg-red-100 hover:text-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
        title="Delete subscription">
    @if($icon)
        <span class="mr-1">{!! $icon !!}</span>
    @endif
    {{ $label }}
</button>
@endif
