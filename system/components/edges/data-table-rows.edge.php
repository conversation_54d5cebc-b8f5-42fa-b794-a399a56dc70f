@props([
    'items' => [], // data array of items
    'columns' => [], // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
    'column_preferences' => [],
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],
    'items_per_page' => 30
])


@php
    // Ensure arrays are properly initialized
    $items = $items ?? [];
    $columns = $columns ?? [];
    $column_preferences = $column_preferences ?? [];
@endphp


@print_rr([
'items' => count($items) > 5 ? array_slice($items, random_int(0, count($items) - 5), 5) : $items,
'count' => count($items),
'columns' => $columns,
'column_count' => count($columns),
'available_fields' => $available_fields
],null,'tcs_processed_rows_items')


@foreach ($items as $item)
    <tr class="border-t {{ $loop->first ? 'border-gray-300' : 'border-gray-200' }} {{ $rows['class_postfix'] }}"
        id="{{ $rows['id_prefix'] . $item[$rows['id_field']] . ($rows['id_postfix'] ?? '') }}" {{ $rows['extra_parameters'] }}>
        @foreach ($columns as $col)
            @php
                $column_info = data_table_storage::process_column_for_display($col, $column_preferences, $loop->index);
            @endphp
            @if(!$column_info['is_hidden'])
                @if ($col['replacements'])
                    @php $item[$column_info['col_field']] = str_replace(array_keys($col['replacements']),$col['replacements'],$item[$column_info['col_field']]) @endphp
                @endif
                <td class="{{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell' }}" data-column-field="{{ $column_info['col_field'] }}">
                    <div class="flex items-center gap-2"><div class="flex-1">
                            @if (isset($col['content']))
                                @if (is_callable($col['content']))
                                    {!! $col['content']($item,$loop->first) !!}
                                @else
                                    {!! $col['content'] ?? 'content' !!}
                                @endif
                            @else
                                @if (is_array($column_info['col_fields']))
                                    {!! data_table_storage::process_field_values($column_info['col_fields'], $item) !!}
                                @else
                                    {{ $item[$column_info['col_field']] ?? '' }}
                                @endif
                            @endif
                        </div>
                        @if (!empty($col['action_buttons']))
                            <div class="flex items-center gap-1">
                                @foreach ($col['action_buttons'] as $action)
                                    @php
                                        $field_value = $item[$action['field']] ?? '';
                                        // Use ICONS constant to get proper SVG icon
                                        $icon_display = array_key_exists($action['icon'], ICONS)
                                            ? \icons\icon($action['icon'], 'size-4')
                                            : \icons\icon('question-mark-circle', 'size-4');
                                    @endphp
                                    <x-{{ $action['template'] }} :field_value="$field_value" :item="$item" :icon="$icon_display" />
                                @endforeach
                            </div>
                        @endif
                    </div>
                </td>
            @endif
        @endforeach
    </tr>
    @if ($items_per_page > 1 && $loop->iteration > $items_per_page )
        @break
    @endif
@endforeach
