@props([
    'field_value' => '',
    'item' => [],
    'icon' => '',
    'action' => []
])

@php
    // Check conditional logic
    $show_button = true;
    
    if (isset($action['conditional'])) {
        $conditional = $action['conditional'];
        
        // Check data source condition
        if (isset($conditional['data_source'])) {
            $data_source = $item['data_source'] ?? '';
            if ($data_source !== $conditional['data_source']) {
                $show_button = false;
            }
        }
        
        // Check field condition
        if ($show_button && isset($conditional['field_check'])) {
            $field_check = $conditional['field_check'];
            $field_value = $item[$field_check['field']] ?? null;
            $expected_value = $field_check['value'] ?? null;
            
            if ($field_value !== $expected_value) {
                $show_button = false;
            }
        }
    }
    
    if (!$show_button) {
        return;
    }
    
    $label = $action['label'] ?? 'Add to Unity';
@endphp

@if($show_button)
<button type="button"
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 bg-green-50 border border-green-200 rounded hover:bg-green-100 hover:text-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
        title="Add subscription to Unity">
    @if($icon)
        <span class="mr-1">{!! $icon !!}</span>
    @endif
    {{ $label }}
</button>
@endif
