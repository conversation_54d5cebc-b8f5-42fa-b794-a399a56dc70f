@props([
    'table_name' => '',
    'callback' => '',
    'current_data_source_type' => 'hardcoded',
    'current_data_source_id' => null,
    'available_data_sources' => [],
    'processed_columns' => [],
    'hidden_columns' => [],
    'available_field_list' => [],
    'oob_swap' => false
])

@print_rr($oob_swap, 'oob_swap')

@if($oob_swap)

    <div id="column_manager_panel_{{ $table_name }}" hx-swap-oob="innerHTML">

@endif

<!-- Header -->
<div class="p-4 border-b border-gray-200 bg-gray-50">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
        <div class="flex gap-2">

            <button type="button" class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                + Column
            </button>
            <button type="button"
                    hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/show_all_columns"
                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                Show All
            </button>
            <button type="button"
                    hx-post="{{ APP_ROOT }}/api/data_table_storage/hide_all_columns"
                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                Hide All
            </button>
        </div>
    </div>

    <!-- Data Source Selector -->
    <div class="flex items-center space-x-3 mb-3">
        <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
        <select id="data-source-select"
                hx-post="{{ APP_ROOT }}/api/data_table_storage/update_data_source"
                hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                hx-target=".data_table"
                hx-swap="outerHTML"
                hx-trigger="change"
                name="data_source_selection"
                class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            <option value="hardcoded" {{ $current_data_source_type === 'hardcoded' ? 'selected' : '' }}>
                Default (Hardcoded Data)
            </option>
            @if(!empty($available_data_sources))
                @php
                    $grouped_sources = [];
                    foreach ($available_data_sources as $source) {
                        $category = $source['category'];
                        if (!isset($grouped_sources[$category])) {
                            $grouped_sources[$category] = [];
                        }
                        $grouped_sources[$category][] = $source;
                    }

                    $category_labels = [
                        'data_table' => 'Data Tables',
                        'email' => 'Email & Campaigns',
                        'users' => 'User Management',
                        'system' => 'System Tables',
                        'autodesk' => 'Autodesk Integration',
                        'other' => 'Other'
                    ];
                @endphp
                @foreach($grouped_sources as $category => $sources)
                    <optgroup label="{{ $category_labels[$category] ?? ucfirst($category) }}">
                        @foreach($sources as $source)
                            <option value="{{ $source['id'] }}"
                                    {{ $current_data_source_id == $source['id'] ? 'selected' : '' }}>
                                {{ $source['name'] }}
                                @if($source['description'])
                                    - {{ $source['description'] }}
                                @endif
                            </option>
                        @endforeach
                    </optgroup>
                @endforeach
            @endif
        </select>
        @if($current_data_source_type === 'data_source' && $current_data_source_id)
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Data Source
                    </span>
        @else
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Hardcoded
                    </span>
        @endif
    </div>

    <!-- Default Configuration Option -->
    <div class="flex items-center space-x-3 mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
        <div class="flex items-center">
            <input type="checkbox"
                   id="apply_default_config_{{ $table_name }}"
                   hx-post="{{ APP_ROOT }}/api/unified_field_definitions/apply_default_config"
                   hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                   hx-target=".data_table"
                   hx-swap="outerHTML"
                   hx-trigger="change"
                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
            <label for="apply_default_config_{{ $table_name }}" class="ml-2 text-xs font-medium text-gray-700">
                Apply default field configuration
            </label>
        </div>
        <div class="flex-1 flex items-center">
            <div class="relative" x-data="{ showTooltip: false }">
                <button type="button"
                        @mouseenter="showTooltip = true"
                        @mouseleave="showTooltip = false"
                        class="ml-2 text-gray-400 hover:text-gray-600">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </button>
                <div x-show="showTooltip"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95"
                     class="absolute z-50 w-64 p-2 mt-1 text-xs text-white bg-gray-800 rounded shadow-lg -top-2 left-6">
                    Uses the "Show by default" settings from unified field definitions to configure initial column
                    display.
                    <div class="absolute top-2 -left-1 w-2 h-2 bg-gray-800 transform rotate-45"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Column Form -->
    <div x-transition class="mb-3 p-2 bg-blue-50 rounded border">
        <form hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_column"
              hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
              hx-target=".data_table"
              hx-swap="outerHTML"
              class="flex gap-2 items-center">
            <input type="text"
                   name="column_name"
                   placeholder="Column name (e.g., Contact Info)"
                   class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                   required>
            <button type="submit"
                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                Add
            </button>
            <button type="button"
                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                Cancel
            </button>
        </form>
    </div>

    <div class="text-xs text-gray-500">
        Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
    </div>
</div>

<!-- Column List -->
<div class="flex-1 overflow-y-auto p-4" >
    <div class="sortable space-y-3"
          data-sortable-group="columns"
          data-sortable-handle=".column-drag-handle"
          hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/reorder_columns"
          hx-trigger="sorted"
          hx-target=".data_table"
          hx-swap="outerHTML"
          hx-include=".column-data"
          x-ref="columnList">
        <input type="hidden" name="table_name" class="column-data" value="{{ $table_name }}">
        <input type="hidden" name="callback" class="column-data" value="{{ $callback }}">
        <input type="hidden" name="data_source" class="column-data" value="{{ $current_data_source_id }}">

        @foreach($processed_columns as $column)
            <div class="border border-gray-200 rounded-lg bg-white" data-column-id="{{ $column['id'] }}">
                <input type="hidden" name="column_ids[]" class="column-data" value="{{ $column['id'] }}">

                <!-- Column Header -->
                <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">
                    <!-- Column Drag Handle -->
                    <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 8h16M4 16h16"/>
                        </svg>
                    </div>

                    <!-- Visibility Checkbox -->
                    <label class="flex items-center flex-1 cursor-pointer">
                        @php
                            $is_checked = !in_array($column['id'], $hidden_columns);
                        @endphp
                        <input type="checkbox"
                               {{ $is_checked ? 'checked' : '' }}
                               hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/toggle_column"
                               hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "data_source": "{{ $current_data_source_id }}"}'
                               hx-target=".data_table"
                               hx-swap="outerHTML"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                        <span class="">
                            <input type="text"
                                   name="label"
                                   value="{{ $column['label'] }}"
                                   hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                    </label>
                    <!-- Add Field Dropdown -->
                    <div class="">
                        <select hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_field_to_column"
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-trigger="change[target.value != '']"
                                hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                name="field_name"
                                @htmx:after-request="$event.target.value = ''"
                                class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                            <option value="">+ Field...</option>
                            @php
                                // Use the cached field list from above to avoid repeated database calls
                                // $available_field_list is already set in the PHP block at the top
                            @endphp
                            @foreach($available_field_list as $field)
                                @if(!in_array($field, $column['fields'] ?? []))
                                    <option value="{{ $field }}">{{ $field }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <!-- Add Action Button -->
                    <div class="">
                        <button type="button"
                                hx-get="{{ APP_ROOT }}/api/data_table/column_preferences/show_add_action_form"
                                hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "loop_index": "{{ $loop->index }}"}'
                                hx-target=".add_action_form_container-{{ $column['id'] }}"
                                hx-swap="innerHTML"
                                class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                            + Add Action Button
                        </button>
                    </div>


                    <!-- Remove Column Button -->
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_column"
                            hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                            hx-confirm="Are you sure you want to remove this column?"
                            class="ml-2 text-red-400 hover:text-red-600"
                            title="Remove Column">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                    </button>
                </div>

                <!-- Fields Container -->
                <div class="sortable p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100"
                     data-sortable-group="fields"
                     data-sortable-handle=".field-drag-handle, .action-drag-handle"
                     data-sortable-target-id-attribute="data-column-id"
                     data-sortable-target-input-name="target_column_id"
                     data-sortable-data-class-prefix="field-data"
                     hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/move_field_simple"
                     hx-trigger="sorted"
                     hx-target=".data_table"
                     hx-include=".field-data_{{ $column['id'] }}"
                     hx-swap="outerHTML">
                    <input type="hidden" class="field-data_{{ $column['id'] }}" name="table_name" value="{{ $table_name }}">
                    <input type="hidden" class="field-data_{{ $column['id'] }}" name="callback" value="{{ $callback }}">
                    <input type="hidden" class="field-data_{{ $column['id'] }}" name="data_source" value="{{ $current_data_source_id }}">
                    <input type="hidden" class="field-data_{{ $column['id'] }}" name="target_column_id" value="{{ $column['id'] }}">


                    <!-- Add Action Form Container (Populated by HTMX) -->
                    <div class="add_action_form_container_{{ $column['id'] }}">
                        <!-- Form will be loaded here via HTMX -->
                    </div>

                    <!-- Existing Fields -->
                    @foreach($column['fields'] as $field)
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="{{ $field }}">
                            <input type="hidden" class="field-data field-data_{{ $column['id'] }}" name="field_names[]" value="{{ $field }}">
                            <!-- Field Drag Handle -->
                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">{{ $field }}</span>
                            <!-- Remove Field Button -->
                            <button type="button"
                                    hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "field_name": "{{ $field }}"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    @endforeach

                    <!-- Existing Action Buttons -->
                    @if(!empty($column['action_buttons']))
                        @foreach($column['action_buttons'] as $action)
                            <div class="action-item inline-flex items-center gap-x-1 bg-green-50 px-2 py-1 m-1 rounded border border-green-200 text-xs cursor-move"
                                 data-action-id="{{ $action['id'] }}">
                                <input type="hidden" class="field-data field-data_{{ $column['id'] }}" name="action_ids[]" value="{{ $action['id'] }}">
                                <!-- Action Drag Handle -->
                                <div class="action-drag-handle text-green-400 hover:text-green-600">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>

                                <!-- Icon Display -->
                                <span class="text-sm">
                                    @if(array_key_exists($action['icon'], ICONS))
                                        @icon($action['icon'], 'size-4')
                                    @else
                                        @icon('question-mark-circle', 'size-4')
                                    @endif
                                </span>

                                <span class="text-gray-700">{{ ucwords(str_replace(['-', '_'], ' ', $action['template'])) }}</span>
                                <span class="text-gray-500">({{ $action['field'] }})</span>

                                <!-- Remove Action Button -->
                                <button type="button"
                                        hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_action_button"
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "action_id": "{{ $action['id'] }}"}'
                                        hx-confirm="Are you sure you want to remove this action button?"
                                        class="text-gray-400 hover:text-red-600 ml-1">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        @endforeach
                    @endif

                    <!-- Drop Zone Indicator -->
                    @if(empty($column['fields']) && empty($column['action_buttons']))
                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>

<!-- Footer Actions -->
<div class="p-4 border-t border-gray-200 bg-gray-50">
    <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center">
            <button type="button"
                    hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/hide_all_columns"
                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                Hide All
            </button>
            @php
                $total_fields = array_sum(array_map(function($col) {
                    return count($col['fields'] ?? []);
                }, $processed_columns));
            @endphp
            <span class="text-xs text-gray-500">{{ count($processed_columns) }} columns, {{ $total_fields }} fields</span>
        </div>
        <button type="button"
                @click="open = false"
                class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
            Done
        </button>
    </div>
</div>
@if($oob_swap)
    </div>
@endif
