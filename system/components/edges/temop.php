<div id="column_manager_autodesk_subscriptions"
     x-data="{
         open: false,
         newColumnName: '',
         showAddColumn: false,

         toggleRenameInput(index) {
             const input = this.$refs['renameInput_' + index];
             if (!input) return;

             const isHidden = input.style.display === 'none' || !input.style.display;
             input.style.display = isHidden ? 'block' : 'none';

             if (isHidden) {
                 this.$nextTick(() => {
                     input.querySelector('input').focus();
                     input.querySelector('input').select();
                 });
             }
         },

         hideRenameInput(index) {
             const input = this.$refs['renameInput_' + index];
             if (input) input.style.display = 'none';
         },

         toggleActionForm(index) {
             const form = this.$refs['actionForm_' + index];
             if (!form) return;

             const isHidden = form.style.display === 'none' || !form.style.display;
             form.style.display = isHidden ? 'block' : 'none';
         },

         hideActionForm(index) {
             const form = this.$refs['actionForm_' + index];
             if (form) form.style.display = 'none';
         }

     }"

     class="relative inline-block text-left"
     @click.away="open = false">


    <button type="button"
            @click="open = !open"
            class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            aria-expanded="false"
            aria-haspopup="true">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z"/>
        </svg>
        Columns
        <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
             stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5"/>
        </svg>
    </button>
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-20 mt-2 w-126 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
         style="height: 80vh;"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1"
         id="column_manager_panel_autodesk_subscriptions"
    >




        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
                <div class="flex gap-2">

                    <button type="button"
                            @click="showAddColumn = !showAddColumn"
                            class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                        + Column
                    </button>
                    <button type="button"
                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/show_all_columns"
                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        Show All
                    </button>
                    <button type="button"
                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/hide_all_columns"
                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                        Hide All
                    </button>
                </div>
            </div>


            <div class="flex items-center space-x-3 mb-3">
                <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
                <select id="data-source-select"
                        hx-post="/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source"
                        hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                        hx-target=".data_table"
                        hx-swap="outerHTML"
                        hx-trigger="change"
                        name="data_source_selection"
                        class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="hardcoded" >
                        Default (Hardcoded Data)
                    </option>
                    <optgroup label="Other">
                        <option value="1"
                        >
                            Autodesk_autorenew                                                            </option>
                    </optgroup>
                    <optgroup label="Autodesk Integration">
                        <option value="30"
                                selected>
                            Autodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                        <option value="31"
                        >
                            Autodesk Accounts                                                                    - Customer account information                                                            </option>
                        <option value="32"
                        >
                            Expiring Subscriptions                                                                    - Active subscriptions expiring within 90 days                                                            </option>
                        <option value="33"
                        >
                            Autodesk Email History                                                                    - Email communication history                                                            </option>
                        <option value="34"
                        >
                            Copy of Autodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                        <option value="35"
                        >
                            Full Autodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                        <option value="51"
                        >
                            tstAutodesk Subscriptions                                                                    - Complete subscription data with customer relationships                                                            </option>
                        <option value="63"
                        >
                            Autodesk_products                                                            </option>
                    </optgroup>
                    <optgroup label="Csv_import">
                        <option value="79"
                        >
                            CSV Import: Sketchup                                                                    - Auto-generated data source for CSV import (79 rows, 59 columns)                                                            </option>
                    </optgroup>
                </select>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Data Source
                    </span>
            </div>


            <div class="flex items-center space-x-3 mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                <div class="flex items-center">
                    <input type="checkbox"
                           id="apply_default_config_autodesk_subscriptions"
                           hx-post="/baffletrain/autocadlt/autobooks/api/unified_field_definitions/apply_default_config"
                           hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                           hx-target=".data_table"
                           hx-swap="outerHTML"
                           hx-trigger="change"
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="apply_default_config_autodesk_subscriptions" class="ml-2 text-xs font-medium text-gray-700">
                        Apply default field configuration
                    </label>
                </div>
                <div class="flex-1 flex items-center">
                    <div class="relative" x-data="{ showTooltip: false }">
                        <button type="button"
                                @mouseenter="showTooltip = true"
                                @mouseleave="showTooltip = false"
                                class="ml-2 text-gray-400 hover:text-gray-600">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </button>
                        <div x-show="showTooltip"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute z-50 w-64 p-2 mt-1 text-xs text-white bg-gray-800 rounded shadow-lg -top-2 left-6">
                            Uses the "Show by default" settings from unified field definitions to configure initial column
                            display.
                            <div class="absolute top-2 -left-1 w-2 h-2 bg-gray-800 transform rotate-45"></div>
                        </div>
                    </div>
                </div>
            </div>


            <div x-show="showAddColumn" x-transition class="mb-3 p-2 bg-blue-50 rounded border">
                <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_column"
                      hx-vals='{"table_name": "autodesk_subscriptions", "callback": ""}'
                      hx-target=".data_table"
                      hx-swap="outerHTML"
                      @htmx:after-request="showAddColumn = false; newColumnName = ''"
                      class="flex gap-2 items-center">
                    <input type="text"
                           name="column_name"
                           x-model="newColumnName"
                           placeholder="Column name (e.g., Contact Info)"
                           class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                           required>
                    <button type="submit"
                            class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Add
                    </button>
                    <button type="button"
                            @click="showAddColumn = false"
                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </form>
            </div>

            <div class="text-xs text-gray-500">
                Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
            </div>
        </div>


        <div class="flex-1 overflow-y-auto p-4">
            <form class="sortable column-sortable space-y-3"
                  hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/reorder_columns"
                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "data_source": "30"}'
                  hx-target=".data_table"
                  hx-swap="outerHTML"
                  hx-trigger="end"
                  data-table-name="autodesk_subscriptions"
                  data-callback=""
                  data-data-source="30"
                  x-ref="columnList">
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_10_27642de9e05d6c3bf6714d4bc017ef1f">
                    <input type="hidden" name="column_order[]" value="col_10_27642de9e05d6c3bf6714d4bc017ef1f"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Name"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(0)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_0" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(0)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(0)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_first_name">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_first_name</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f", "field_name": "endcust_first_name"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_last_name">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_last_name</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_10_27642de9e05d6c3bf6714d4bc017ef1f", "field_name": "endcust_last_name"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_9_2a0d15709399fe8056cd9c0dcd396632">
                    <input type="hidden" name="column_order[]" value="col_9_2a0d15709399fe8056cd9c0dcd396632"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Customer"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(1)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_1" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(1)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(1)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_name">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_name</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632", "field_name": "endcust_name"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_account_csn">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_account_csn</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_9_2a0d15709399fe8056cd9c0dcd396632", "field_name": "endcust_account_csn"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_1_9516adb5b45361820e47ff8dcdb18ce2">
                    <input type="hidden" name="column_order[]" value="col_1_9516adb5b45361820e47ff8dcdb18ce2"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Ids"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(2)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_2" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(2)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(2)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_subscriptionId">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_subscriptionId</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2", "field_name": "subs_subscriptionId"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_subscriptionReferenceNumber">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_subscriptionReferenceNumber</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_1_9516adb5b45361820e47ff8dcdb18ce2", "field_name": "subs_subscriptionReferenceNumber"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_2_8c2bbee4e6042ff69ee6e520b28fe7b0">
                    <input type="hidden" name="column_order[]" value="col_2_8c2bbee4e6042ff69ee6e520b28fe7b0"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_8c2bbee4e6042ff69ee6e520b28fe7b0", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="SubscriptionReferenceNumber"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_8c2bbee4e6042ff69ee6e520b28fe7b0"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_8c2bbee4e6042ff69ee6e520b28fe7b0"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(3)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_8c2bbee4e6042ff69ee6e520b28fe7b0"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_3" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_2_8c2bbee4e6042ff69ee6e520b28fe7b0"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(3)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(3)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_3_281a0f1408cea80f73a2a47a2d3b7f88">
                    <input type="hidden" name="column_order[]" value="col_3_281a0f1408cea80f73a2a47a2d3b7f88"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_281a0f1408cea80f73a2a47a2d3b7f88", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Quantity"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_281a0f1408cea80f73a2a47a2d3b7f88"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_281a0f1408cea80f73a2a47a2d3b7f88"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(4)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_281a0f1408cea80f73a2a47a2d3b7f88"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_4" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_3_281a0f1408cea80f73a2a47a2d3b7f88"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(4)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(4)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_4_7cb10236b42be9cb3a374f85923fba0b">
                    <input type="hidden" name="column_order[]" value="col_4_7cb10236b42be9cb3a374f85923fba0b"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Deets"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(5)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_5" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(5)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(5)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_status">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_status</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b", "field_name": "subs_status"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_quantity">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_quantity</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_4_7cb10236b42be9cb3a374f85923fba0b", "field_name": "subs_quantity"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_5_3895d5b13f20f13cd549c4bd659a3f35">
                    <input type="hidden" name="column_order[]" value="col_5_3895d5b13f20f13cd549c4bd659a3f35"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Dates"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(6)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_6" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(6)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(6)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_startDate">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_startDate</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35", "field_name": "subs_startDate"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="subs_endDate">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">subs_endDate</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_5_3895d5b13f20f13cd549c4bd659a3f35", "field_name": "subs_endDate"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99">
                    <input type="hidden" name="column_order[]" value="col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="EndDate"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(7)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_7" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_6_5729e87e2ca3ba1aab1b9b9ea8dbed99"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(7)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(7)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_7_6eae894e9cf944fe99f4ecc1e647b8e4">
                    <input type="hidden" name="column_order[]" value="col_7_6eae894e9cf944fe99f4ecc1e647b8e4"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_6eae894e9cf944fe99f4ecc1e647b8e4", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Id"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_6eae894e9cf944fe99f4ecc1e647b8e4"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_6eae894e9cf944fe99f4ecc1e647b8e4"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(8)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_6eae894e9cf944fe99f4ecc1e647b8e4"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_8" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_6eae894e9cf944fe99f4ecc1e647b8e4"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(8)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(8)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="endcust_id">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">endcust_id</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_7_6eae894e9cf944fe99f4ecc1e647b8e4", "field_name": "endcust_id"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_8_691981d95f08241e3b5c73772b36588a">
                    <input type="hidden" name="column_order[]" value="col_8_691981d95f08241e3b5c73772b36588a"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_691981d95f08241e3b5c73772b36588a", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Account Csn"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_691981d95f08241e3b5c73772b36588a"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_691981d95f08241e3b5c73772b36588a"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(9)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_691981d95f08241e3b5c73772b36588a"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_9" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_8_691981d95f08241e3b5c73772b36588a"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(9)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(9)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_11_65cc133083b5dbc280a7c9a6f6fa7964">
                    <input type="hidden" name="column_order[]" value="col_11_65cc133083b5dbc280a7c9a6f6fa7964"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_65cc133083b5dbc280a7c9a6f6fa7964", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Last Name"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_65cc133083b5dbc280a7c9a6f6fa7964"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_65cc133083b5dbc280a7c9a6f6fa7964"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(10)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_65cc133083b5dbc280a7c9a6f6fa7964"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_10" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_11_65cc133083b5dbc280a7c9a6f6fa7964"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(10)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(10)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_12_abc01573ee70362ec6ca14aef7740b6b">
                    <input type="hidden" name="column_order[]" value="col_12_abc01573ee70362ec6ca14aef7740b6b"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Quote Id"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(11)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_11" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(11)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(11)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>


                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="lastquote_quote_id">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">lastquote_quote_id</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b", "field_name": "lastquote_quote_id"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="lastquote_quote_number">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">lastquote_quote_number</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b", "field_name": "lastquote_quote_number"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                             data-field-name="lastquote_quoted_date">

                            <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 8h16M4 16h16"/>
                                </svg>
                            </div>
                            <span class="text-gray-700">lastquote_quoted_date</span>

                            <button type="button"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_12_abc01573ee70362ec6ca14aef7740b6b", "field_name": "lastquote_quoted_date"}'
                                    class="text-gray-400 hover:text-red-600 ml-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>




                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_13_3fa70f7dd6019a6e0d8eec7bb133022f">
                    <input type="hidden" name="column_order[]" value="col_13_3fa70f7dd6019a6e0d8eec7bb133022f"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_3fa70f7dd6019a6e0d8eec7bb133022f", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Quote Number"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_3fa70f7dd6019a6e0d8eec7bb133022f"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_3fa70f7dd6019a6e0d8eec7bb133022f"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(12)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_3fa70f7dd6019a6e0d8eec7bb133022f"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_12" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_3fa70f7dd6019a6e0d8eec7bb133022f"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(12)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="button"
                                            hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                            hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_13_3fa70f7dd6019a6e0d8eec7bb133022f"}'
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            @htmx:after-request="hideActionForm(12)"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(12)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_14_3845d1ad32aed7fc7ca573032f380116">
                    <input type="hidden" name="column_order[]" value="col_14_3845d1ad32aed7fc7ca573032f380116"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_3845d1ad32aed7fc7ca573032f380116", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Quoted Date"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_3845d1ad32aed7fc7ca573032f380116"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_3845d1ad32aed7fc7ca573032f380116"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(13)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_3845d1ad32aed7fc7ca573032f380116"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_13" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_14_3845d1ad32aed7fc7ca573032f380116"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(13)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(13)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_new_1754434879">
                    <input type="hidden" name="column_order[]" value="col_new_1754434879"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754434879", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Actions"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754434879"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754434879"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(14)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754434879"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_14" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754434879"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(14)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(14)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="col_new_1754662026">
                    <input type="hidden" name="column_order[]" value="col_new_1754662026"/>


                    <div class="flex items-center p-3 bg-gray-50 gap-1 rounded-t-lg">

                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>


                        <label class="flex items-center flex-1 cursor-pointer">
                            <input type="checkbox"
                                   checked                               hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754662026", "data_source": "30"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="">
                            <input type="text"
                                   name="label"
                                   value="Email address"
                                   hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/rename_column"
                                   hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754662026"}'
                                   hx-target=".data_table"
                                   hx-trigger="changed delay:500ms"
                                   hx-swap="outerHTML"
                                   class="flex-1 text-sm border-none px-2 py-1 bg-transparent  focus:shadow-inner outline-0">
                            </span>
                        </label>

                        <div class="">
                            <select hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754662026"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="w-24 text-xs px-2 py-1 bg-blue-100 border border-blue-300 text-blue-700 rounded hover:bg-blue-200 ">
                                <option value="">+ Field...</option>
                                <option value="subs_id">subs_id</option>
                                <option value="subs_subscriptionId">subs_subscriptionId</option>
                                <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                <option value="subs_quantity">subs_quantity</option>
                                <option value="subs_status">subs_status</option>
                                <option value="subs_startDate">subs_startDate</option>
                                <option value="subs_endDate">subs_endDate</option>
                                <option value="endcust_id">endcust_id</option>
                                <option value="endcust_account_csn">endcust_account_csn</option>
                                <option value="endcust_name">endcust_name</option>
                                <option value="endcust_first_name">endcust_first_name</option>
                                <option value="endcust_last_name">endcust_last_name</option>
                                <option value="lastquote_quote_id">lastquote_quote_id</option>
                                <option value="lastquote_quote_number">lastquote_quote_number</option>
                                <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                <option value="subs_enddatediff">subs_enddatediff</option>
                            </select>
                        </div>

                        <div class="">
                            <button type="button"
                                    @click="toggleActionForm(15)"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>



                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754662026"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>



                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">



                        <div x-ref="actionForm_15" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "autodesk_subscriptions", "callback": "", "column_id": "col_new_1754662026"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm(15)"
                                  class="space-y-2">


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        <option value="action-delete-button">Action Delete Button</option>
                                        <option value="action-edit-button">Action Edit Button</option>
                                        <option value="action-generic-view-button">Action Generic View Button</option>
                                        <option value="action-view-button">Action View Button</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        <option value="subs_id">subs_id</option>
                                        <option value="subs_subscriptionId">subs_subscriptionId</option>
                                        <option value="subs_subscriptionReferenceNumber">subs_subscriptionReferenceNumber</option>
                                        <option value="subs_quantity">subs_quantity</option>
                                        <option value="subs_status">subs_status</option>
                                        <option value="subs_startDate">subs_startDate</option>
                                        <option value="subs_endDate">subs_endDate</option>
                                        <option value="endcust_id">endcust_id</option>
                                        <option value="endcust_account_csn">endcust_account_csn</option>
                                        <option value="endcust_name">endcust_name</option>
                                        <option value="endcust_first_name">endcust_first_name</option>
                                        <option value="endcust_last_name">endcust_last_name</option>
                                        <option value="lastquote_quote_id">lastquote_quote_id</option>
                                        <option value="lastquote_quote_number">lastquote_quote_number</option>
                                        <option value="lastquote_quoted_date">lastquote_quoted_date</option>
                                        <option value="subs_enddatediff">subs_enddatediff</option>
                                    </select>
                                </div>


                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>


                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm(15)"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>






                        <div class="text-xs text-gray-400 text-center py-2">
                            Drop fields or action buttons here, or use dropdowns above
                        </div>
                    </div>
                </div>
            </form>
        </div>
