@props([
    'field_value' => '',
    'item' => [],
    'icon' => '',
    'action' => []
])

@php
    print_rr($action,'tcs_action');
    // Extract action configuration
    $hx_post = $action['hx-post'] ?? (APP_ROOT . '/api/view');
    $label = $action['label'] ?? 'View';

    // If modal trigger is enabled, automatically set modal-specific HTMX attributes
    $modal_trigger = isset($action['modal_trigger']) && $action['modal_trigger'];
    if ($modal_trigger) {
        $hx_target = '#modal_body';
        $hx_swap = 'innerHTML';
        $alpine_click = '@click="showModal = true"';
    } else {
        // Use custom values if modal trigger is not enabled
        $hx_target = $action['hx-target'] ?? '#content';
        $hx_swap = $action['hx-swap'] ?? 'innerHTML';
        $alpine_click = '';
    }

    // Build hx-vals from data_fields configuration
    $hx_vals = [];
    if (isset($action['data_fields']) && is_array($action['data_fields'])) {
        foreach ($action['data_fields'] as $key => $field_name) {
            if ($key === 'tab_title_template') {
                // Handle template strings like 'Customer {endcust_account_csn}'
                $template = $field_name;
                $hx_vals['tab_title'] = preg_replace_callback('/\{(\w+)\}/', function($matches) use ($item) {
                    return $item[$matches[1]] ?? '';
                }, $template);
            } else {
                $hx_vals[$key] = $item[$field_name] ?? '';
            }
        }
    }

    // Fallback to using field_value for CSN if not set
    if (!isset($hx_vals['csn']) && !empty($field_value)) {
        $hx_vals['csn'] = $field_value;
    }

    // Set default tab title if not set
    if (!isset($hx_vals['tab_title'])) {
        $hx_vals['tab_title'] = 'Customer ' . $field_value;
    }
@endphp

<button type="button"
        class="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded hover:bg-indigo-100 hover:text-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1"
        hx-post="{{ $hx_post }}"
        hx-target="{{ $hx_target }}"
        hx-swap="{{ $hx_swap }}"
        hx-vals='{{ json_encode($hx_vals) }}'
        {!! $alpine_click !!}
        data-tab-title="{{ $hx_vals['tab_title'] ?? '' }}"
        data-target="{{ $hx_target }}"
        @if(isset($hx_vals['subscription_number']))
        data-subscription-number="{{ $hx_vals['subscription_number'] }}"
        @endif
        title="View customer details">
    @if($icon)
        <span class="mr-1">{!! $icon !!}</span>
    @endif
    {{ $label }}
</button>
