# Generic Data View System

This system provides a generic way to display detailed views of any datatable row data without needing to create custom view templates for each data type.

## Components

### 1. Generic View Template (`view-generic_data_display.edge.php`)
A flexible template that can display any data structure with:
- Automatic field categorization (primary, contact/address, system fields)
- Smart formatting for emails, URLs, booleans, arrays
- Collapsible sections for raw data and system information
- Print-friendly styling
- Copy-to-clipboard functionality

### 2. Generic View API (`generic_view.api.php`)
Handles data retrieval from various sources:
- Database tables
- CSV files (future)
- API endpoints (future)
- Automatic title generation
- Metadata collection

### 3. Generic Action Button (`action-generic-view-button.edge.php`)
A reusable button component that can be added to any datatable column.

## Usage in Datatables

### Method 1: Using the Column Manager
1. Open the column manager for your datatable
2. Find the column where you want to add the view button
3. Click "Add Action Button"
4. Fill in the form:
   - **Template**: `action-generic-view-button`
   - **Field**: The field containing the record ID (e.g., `id`, `user_id`, `subscription_id`)
   - **Icon**: `👁️` (or any emoji/icon you prefer)

### Method 2: Manual Column Definition
Add an action column to your datatable structure:

```php
$columns = [
    // ... other columns ...
    [
        'label' => 'Actions',
        'content' => function($item) {
            return Edge::render('action-generic-view-button', [
                'field_value' => $item['id'], // or whatever your ID field is
                'item' => $item,
                'table_name' => 'your_table_name',
                'id_field' => 'id', // the name of your ID field
                'icon' => '👁️',
                'label' => 'View'
            ]);
        }
    ]
];
```

### Method 3: Using in Template Functions
In your table function files (e.g., `resources/functions/your_table.fn.php`):

```php
function your_table_columns() {
    return [
        ['label' => 'Name', 'fields' => ['name']],
        ['label' => 'Email', 'fields' => ['email']],
        // ... other columns ...
        [
            'label' => 'Actions',
            'content' => function($item) {
                return Edge::render('action-generic-view-button', [
                    'field_value' => $item['id'],
                    'item' => $item,
                    'table_name' => 'users', // your actual table name
                    'id_field' => 'id'
                ]);
            }
        ]
    ];
}
```

## Parameters

### Required Parameters
- `field_value`: The ID value of the record to view
- `table_name`: The name of the database table or data source

### Optional Parameters
- `item`: The full row data array (used as fallback for field_value)
- `id_field`: The name of the ID field (default: 'id')
- `data_source`: For non-database sources (future use)
- `icon`: The icon to display (default: '👁️')
- `label`: The button label (default: 'View')

## Features

### Automatic Field Categorization
The template automatically organizes fields into logical groups:

- **Primary Information**: Main data fields
- **Contact & Address Information**: Fields containing address, email, phone, etc.
- **System Information**: ID fields, timestamps, etc.

### Smart Formatting
- Email addresses become clickable mailto links
- URLs become clickable links
- Boolean values display as Yes/No badges
- Arrays display as formatted JSON
- Empty/null values are filtered out

### Responsive Design
- Mobile-friendly layout
- Collapsible sections to save space
- Print-optimized styling

## Extending the System

### Adding New Data Sources
To support CSV files, APIs, or other data sources, extend the functions in `generic_view.api.php`:

1. Implement `get_csv_record()` for CSV support
2. Implement `get_api_record()` for API support
3. Add configuration management for data sources

### Custom Field Formatting
Modify the template's field categorization logic in `view-generic_data_display.edge.php` to handle specific field types or naming conventions for your data.

### Custom Actions
Add additional action buttons by creating new button components following the same pattern as `action-generic-view-button.edge.php`.

## Benefits

1. **No Custom Templates Needed**: Works with any data structure
2. **Consistent UI**: All views use the same professional layout
3. **Easy to Implement**: Just add the action button to any datatable
4. **Flexible**: Supports various data sources and field types
5. **Maintainable**: Single template to update for all generic views
6. **User-Friendly**: Automatic formatting and organization of data
