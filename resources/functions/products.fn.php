<?php
namespace autodesk;
use autodesk_api\autodesk_api;
use edge\Edge;
use data_table\data_table;


function generate_product_table($criteria = [], $just_table = false,$cols_hidden = [],$just_body = false){
    $autodesk = new autodesk_api();
    print_rr($criteria,'critty');


    $grouped_products = $columns = $uniques = [];

    $table_structure = [
        'table_id' => 'autodesk_products',
        'db_table' => 'pac',
        'columns' => [
            ['label' => 'Name', 'fields' => ['pac_offeringName'], 'auto_filter' => true, 'filter_limit' => 100],
            ['label' => 'Code', 'fields' => ['pac_offeringCode']],
            ['label' => 'Id', 'fields' => ['pac_offeringId']],
            ['label' => 'term', 'fields' => ['pac_term_description'], 'filters' => ['Annual' => 'Annual','3 Year' => '3 Year']],
            ['label' => 'Action', 'fields' => ['pac_orderAction'], 'filters' => ['New' => 'New','Renewal' => 'Renewal']],
            ['label' => 'SPCode', 'fields' => ['pac_specialProgramDiscount_code'], 'filters' => ['M2S' => 'M2S','SU1' => 'SU1','SU2' => 'SU2','SU4' => 'SU4','MY4' => 'MY4','SU3' => 'SU3','M2M' => 'M2M']],
            ['label' => 'frmQty', 'field' => 'pac_fromQty', 'auto_filter' => true],
            ['label' => 'toQty', 'field' => 'pac_toQty', 'auto_filter' => true],
            ['label' => 'SRP', 'field' => 'pac_SRP'],
            ['label' => 'SPDiscount', 'field' => 'pac_costAfterSpecialProgramDiscount'],
            ['label' => 'Renewal', 'field' => 'pac_costAfterRenewalDiscount'],
            ['label' => 'VolDiscount', 'field' => 'pac_costAfterTransactionVolumeDiscount'],
            ['label' => 'SDDiscount', 'field' => 'pac_costAfterServiceDurationDiscount'],
            ['label' => 'Actions','content' => function ($item) {
                Edge::render('forms-button', [
                    'type' => 'button',
                    'label' => 'View',
                    'hx-post' => APP_ROOT . '/api/view',
                    'hx-swap' => 'innerHTML',
                    'hx-target' => '#modal_body',
                    'hx-vals' => json_encode([
                        "subscription_number" => $item['subs_subscriptionReferenceNumber'],
                        "orders_id" => $item['orders_id'],
                        "subscription_id" => $item['id'],
                    ]),
                    '@click' => 'showModal = true',
                    'data_target' => '#modal_body',
                    'data_subscription_number' => $item['subs_subscriptionReferenceNumber']
                ]);
            },
            ],
        ]
    ];
    $default_criteria = [
        "order_by" => "pac_offeringName",
        "limit" => 30,
        "search_columns" => [
            "pac.offeringName",
            "pac.offeringCode",
            "pac.offeringId"
        ]
    ];
    $criteria = array_merge($default_criteria, $criteria);
    $columns = filter_db_schema($table_structure);

    $autodesk = new autodesk_api();
    $data = $autodesk->products->get_all($columns, $criteria);

    $filter_criteria = ['limit' => 10];
    foreach ($table_structure['columns'] as $key => $col) {
        if (!isset($col['auto_filter'])) continue;
        $first_field = $col['fields'][0] ?? '';
        $table_structure['columns'][$key]['filter_data'] = $autodesk->products->get_distinct([$first_field], $filter_criteria);
    }
    return data_table::process_data_table(
        table_structure: $table_structure,
        items: $data,
        callback: __FUNCTION__,
        criteria: $criteria,
        cols_hidden: $cols_hidden,
        just_table: $just_table
    );
}

