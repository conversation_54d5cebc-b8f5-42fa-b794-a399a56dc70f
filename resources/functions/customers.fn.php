<?php
namespace autodesk;
use autodesk_api\autodesk_api;
use edge\Edge;
use data_table\data_table;


function generate_customer_table($criteria = []) {
    $just_body = $criteria['just_body'] ?? false;
    $cols_hidden = $criteria['cols_hidden'] ?? [];
    $just_table = $criteria['just_table'] ?? false;
    $table_structure = [
        'table_id' => 'customers',
        'db_table' => 'autodesk_accounts',
        'columns' => [
            ['label' => 'Company', 'fields' => ["endcust_name", "endcust_email"]],
            ['label' => 'Customer', 'fields' => ["endcust_first_name", "endcust_last_name"]],
            ['label' => 'CSN', 'fields' => ["endcust_account_csn"]],
            ['label' => 'Primary Admin', 'fields' => ["endcust_primary_admin_first_name", "endcust_primary_admin_last_name"]],
            ['label' => 'Location', 'fields' => ['endcust_city', 'endcust_postal_code']],
            ['label' => 'Last Modified', 'fields' => ["endcust_last_modified"]],
            [
                'label' => 'Actions',
                'filter' => false,
                'action_buttons' => [ [
                        'id' => 'view_customer',
                        'template' => 'action-customer-view-button',
                        'field' => 'endcust_account_csn',
                        'icon' => 'book-open',
                        'label' => 'View',
                        'hx-post' => APP_ROOT . '/api/view',
                        'modal_trigger' => true, // Automatically sets hx-target="#modal_body" and hx-swap="innerHTML"
                        'data_fields' => [
                            'csn' => 'endcust_account_csn',
                            'tab_title_template' => 'Customer {endcust_account_csn}',
                            'subscription_number' => 'subs_subscriptionReferenceNumber'
                        ]
                    ]
                ]
            ]
        ]
    ];

    $default_criteria = [
        "order_by" => "endcust_last_modified",
        "limit" => 300,
        "search_columns" => [
            "endcust.account_csn",
            "endcust.name",
            "endcust.address1",
            "endcust.address2",
            "endcust.address3",
            "endcust.city",
            "endcust.state_province",
            "endcust.postal_code",
            "endcust.country",
            "endcust.primary_admin_first_name",
            "endcust.primary_admin_last_name",
            "endcust.primary_admin_email",
            "endcust.team_id",
            "endcust.team_name",
            "endcust.first_name",
            "endcust.last_name",
            "endcust.email"
        ]

    ];
    $criteria = array_merge($default_criteria, $criteria);
    $columns = filter_db_schema($table_structure);

    $autodesk = new autodesk_api();
    $items = $autodesk->customers->get_all($columns, $criteria);

    $filter_criteria = ['limit' => 10];
    foreach ($table_structure['columns'] as $key => $col) {
        if (!isset($col['auto_filter'])) continue;
        $first_field = $col['fields'][0] ?? '';
        $table_structure['columns'][$key]['filter_data'] = $autodesk->customers->get_distinct([$first_field], $filter_criteria);
    }
    return data_table::process_data_table(
        table_structure: $table_structure,
        items: $items,
        callback: __FUNCTION__,
        criteria: $criteria,
        cols_hidden: $cols_hidden,
        just_body: $just_body,
        just_table: $just_table,
        table_name: 'autodesk_customers',
        include_column_manager: true
    );
}