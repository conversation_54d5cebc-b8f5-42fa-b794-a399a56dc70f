<?php
/**
 * Test script to verify inheritance execution state is working correctly
 */

// Simple bootstrap
define('DS', DIRECTORY_SEPARATOR);
define('FS_APP_ROOT', __DIR__ . DS);
define('FS_TEMP', __DIR__ . DS . 'temp');

// Create temp directory if it doesn't exist
if (!is_dir(FS_TEMP)) {
    mkdir(FS_TEMP, 0755, true);
}
if (!is_dir(FS_TEMP . DS . 'edge')) {
    mkdir(FS_TEMP . DS . 'edge', 0755, true);
}

require_once 'system/classes/edge/edge.class.php';
use edge\edge as Edge;

// Test data
$test_data = [
    'items' => [
        ['id' => 1, 'name' => 'Test User', 'email' => '<EMAIL>']
    ],
    'columns' => [
        ['label' => 'ID', 'field' => 'id'],
        ['label' => 'Name', 'field' => 'name']
    ],
    'table_name' => 'test_inheritance',
    'external_call' => true
];

echo "<h1>Inheritance Execution State Test</h1>\n";

// Test 1: Check compiled template content
echo "<h2>Test 1: Check Compiled Template Content</h2>\n";

// Force recompilation by rendering a template
try {
    $result = Edge::render('data-table-structure', $test_data);
    
    // Check the compiled template file
    $compiled_path = 'temp/edge/data-table-structure.edge.php';
    if (file_exists($compiled_path)) {
        $compiled_content = file_get_contents($compiled_path);
        
        echo "<h3>Compiled Template Analysis:</h3>\n";
        
        // Check if conditional execution logic is present
        if (strpos($compiled_content, 'edge::$inheritance_execution_state') !== false) {
            echo "<p style='color: green;'>✓ Conditional execution logic found in compiled template</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Conditional execution logic NOT found in compiled template</p>\n";
        }
        
        // Show the conditional logic
        if (preg_match('/\/\/ Init block.*?if \(!isset.*?\}/s', $compiled_content, $matches)) {
            echo "<h4>Conditional Logic Found:</h4>\n";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($matches[0]);
            echo "</pre>\n";
        }
        
        // Count how many init blocks are in the compiled template
        $init_count = substr_count($compiled_content, 'Init block from');
        echo "<p><strong>Number of init blocks in compiled template:</strong> $init_count</p>\n";
        
    } else {
        echo "<p style='color: red;'>✗ Compiled template not found at: $compiled_path</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error rendering template: " . $e->getMessage() . "</p>\n";
}

// Test 2: Check execution state tracking
echo "<h2>Test 2: Execution State Tracking</h2>\n";

// Reset state and render template
Edge::resetInheritanceState();
echo "<p>Initial state: " . json_encode(Edge::getInheritanceState()) . "</p>\n";

try {
    $result = Edge::render('data-table-structure', $test_data);
    echo "<p>State after rendering data-table-structure: " . json_encode(Edge::getInheritanceState()) . "</p>\n";
    
    // Render again to see if state persists
    $result2 = Edge::render('data-table-rows', $test_data);
    echo "<p>State after rendering data-table-rows: " . json_encode(Edge::getInheritanceState()) . "</p>\n";
    
    if (!empty(Edge::getInheritanceState())) {
        echo "<p style='color: green;'>✓ Execution state is being tracked</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Execution state is NOT being tracked</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error in execution state test: " . $e->getMessage() . "</p>\n";
}

// Test 3: Multiple render test
echo "<h2>Test 3: Multiple Render Test</h2>\n";

Edge::resetInheritanceState();
echo "<p>Starting fresh render chain...</p>\n";

try {
    // Render parent first
    $parent_result = Edge::render('data-table', $test_data);
    $state_after_parent = Edge::getInheritanceState();
    echo "<p>State after parent render: " . json_encode($state_after_parent) . "</p>\n";
    
    // Now render child - should not re-execute parent init
    $child_result = Edge::render('data-table-structure', $test_data);
    $state_after_child = Edge::getInheritanceState();
    echo "<p>State after child render: " . json_encode($state_after_child) . "</p>\n";
    
    if ($state_after_parent === $state_after_child) {
        echo "<p style='color: green;'>✓ Child did not re-execute parent init blocks</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ Child execution changed state (may be expected if child has its own init)</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error in multiple render test: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p>This test verifies that:</p>\n";
echo "<ul>\n";
echo "<li>Compiled templates contain conditional execution logic</li>\n";
echo "<li>Execution state is properly tracked across renders</li>\n";
echo "<li>Parent-child execution doesn't duplicate init blocks</li>\n";
echo "</ul>\n";

echo "<p><em>Check the debug logs for 'tcs_master1' entries to see if duplication is eliminated.</em></p>\n";
?>
