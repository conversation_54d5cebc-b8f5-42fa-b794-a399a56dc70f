<?php
/**
 * Debug inheritance compilation
 */

// Include the edge class directly
require_once 'system/classes/edge/edge.class.php';

// Test the inheritance processing methods directly
$test_source = '@props([
    \'parent\' => \'data-table\',
    \'child_prop\' => \'value\'
])

@init
    echo "Child init block";
@endinit

<div>Child template content</div>';

echo "<h1>Debug Inheritance Compilation</h1>\n";

// Test extracting parent
$reflection = new ReflectionClass('edge\edge');
$extractParentMethod = $reflection->getMethod('extractParentFromProps');
$extractParentMethod->setAccessible(true);

$parent = $extractParentMethod->invoke(null, $test_source);
echo "<p><strong>Extracted parent:</strong> " . ($parent ?: 'none') . "</p>\n";

// Test building inheritance chain
if ($parent) {
    $buildChainMethod = $reflection->getMethod('buildInheritanceChain');
    $buildChainMethod->setAccessible(true);
    
    try {
        $chain = $buildChainMethod->invoke(null, $parent);
        echo "<p><strong>Inheritance chain:</strong> " . implode(' → ', $chain) . "</p>\n";
    } catch (Exception $e) {
        echo "<p><strong>Chain building error:</strong> " . $e->getMessage() . "</p>\n";
    }
}

// Test the full inheritance processing
$processMethod = $reflection->getMethod('processTemplateInheritance');
$processMethod->setAccessible(true);

try {
    $processed = $processMethod->invoke(null, $test_source, 'test-child', []);
    echo "<h2>Processed Template Source:</h2>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap;'>";
    echo htmlspecialchars($processed);
    echo "</pre>\n";
} catch (Exception $e) {
    echo "<p><strong>Processing error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p><strong>Stack trace:</strong></p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

// Test compilation of the processed source
try {
    $compileMethod = $reflection->getMethod('compile');
    $compileMethod->setAccessible(true);
    
    $compiled = $compileMethod->invoke(null, $processed, 'test-child', []);
    echo "<h2>Final Compiled Template:</h2>\n";
    echo "<pre style='background: #f0f8ff; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap;'>";
    echo htmlspecialchars($compiled);
    echo "</pre>\n";
    
    // Check if conditional logic is present
    if (strpos($compiled, 'inheritance_execution_state') !== false) {
        echo "<p style='color: green;'>✓ Conditional execution logic found in compiled output</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Conditional execution logic NOT found in compiled output</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p><strong>Compilation error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p><strong>Stack trace:</strong></p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>Analysis</h2>\n";
echo "<p>This debug script tests the inheritance compilation process step by step to identify where the issue might be.</p>\n";
?>
