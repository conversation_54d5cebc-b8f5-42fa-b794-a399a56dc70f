<?php
/**
 * Test column manager inheritance issue
 */

// Include edge class
require_once 'system/classes/edge/edge.class.php';

// Test the inheritance processing for data-table-column-manager
$reflection = new ReflectionClass('edge\edge');
$processMethod = $reflection->getMethod('processTemplateInheritance');
$processMethod->setAccessible(true);

// Get the actual source of data-table-column-manager
$column_manager_source = file_get_contents('system/components/edges/data-table-column-manager.edge.php');

echo "<h1>Column Manager Inheritance Test</h1>\n";

echo "<h2>Original Column Manager Template:</h2>\n";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
echo htmlspecialchars(substr($column_manager_source, 0, 500)) . "...";
echo "</pre>\n";

try {
    $processed = $processMethod->invoke(null, $column_manager_source, 'data-table-column-manager', []);
    
    echo "<h2>Processed Template (with inheritance):</h2>\n";
    echo "<pre style='background: #f0f8ff; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap;'>";
    echo htmlspecialchars($processed);
    echo "</pre>\n";
    
    // Check if the conditional logic is preserved
    if (strpos($processed, 'if ($external_call)') !== false) {
        echo "<p style='color: green;'>✓ Original conditional logic preserved</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Original conditional logic NOT preserved</p>\n";
    }
    
    // Check if inheritance execution state logic is present
    if (strpos($processed, 'inheritance_execution_state') !== false) {
        echo "<p style='color: green;'>✓ Inheritance execution state logic present</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Inheritance execution state logic NOT present</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>Analysis</h2>\n";
echo "<p>The issue is likely that:</p>\n";
echo "<ul>\n";
echo "<li>data-table is called with external_call=true → should execute init</li>\n";
echo "<li>data-table-column-manager is called as child with external_call=false → should NOT execute inherited init</li>\n";
echo "<li>But the inheritance system may not be preserving the original conditional logic</li>\n";
echo "</ul>\n";
?>
