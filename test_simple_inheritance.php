<?php
/**
 * Simple test to verify inheritance execution state
 */

// Create a simple parent template
$parent_template = '@props([
    \'title\' => \'Parent Template\',
    \'shared_prop\' => \'from_parent\'
])

@init
    echo "PARENT_INIT_EXECUTED\n";
@endinit

<div>Parent content: {{ $title }}</div>';

// Create a simple child template  
$child_template = '@props([
    \'parent\' => \'test-parent\',
    \'child_prop\' => \'from_child\'
])

@init
    echo "CHILD_INIT_EXECUTED\n";
@endinit

<div>Child content: {{ $child_prop }}</div>';

// Write templates to files
file_put_contents('system/components/edges/test-parent.edge.php', $parent_template);
file_put_contents('system/components/edges/test-child.edge.php', $child_template);

echo "<h1>Simple Inheritance Test</h1>\n";

// Include edge class
require_once 'system/classes/edge/edge.class.php';
use edge\edge as Edge;

// Test 1: Render child template directly (should execute both parent and child init)
echo "<h2>Test 1: Render child directly</h2>\n";
echo "<p>Expected: Both PARENT_INIT_EXECUTED and CHILD_INIT_EXECUTED should appear once each</p>\n";

try {
    Edge::forceResetInheritanceState();
    
    ob_start();
    $result = Edge::render('test-child', []);
    $output = ob_get_clean();
    
    echo "<h3>Output:</h3>\n";
    echo "<pre>" . htmlspecialchars($output) . "</pre>\n";
    
    echo "<h3>HTML Result:</h3>\n";
    echo "<div style='border: 1px solid #ccc; padding: 10px;'>" . $result . "</div>\n";
    
    // Count occurrences
    $parent_count = substr_count($output, 'PARENT_INIT_EXECUTED');
    $child_count = substr_count($output, 'CHILD_INIT_EXECUTED');
    
    echo "<p><strong>PARENT_INIT_EXECUTED count:</strong> $parent_count " . ($parent_count === 1 ? '✓' : '✗') . "</p>\n";
    echo "<p><strong>CHILD_INIT_EXECUTED count:</strong> $child_count " . ($child_count === 1 ? '✓' : '✗') . "</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}

// Test 2: Render child twice (should not duplicate init execution)
echo "<h2>Test 2: Render child twice</h2>\n";
echo "<p>Expected: Init blocks should not execute again</p>\n";

try {
    ob_start();
    $result2 = Edge::render('test-child', []);
    $output2 = ob_get_clean();
    
    echo "<h3>Second render output:</h3>\n";
    echo "<pre>" . htmlspecialchars($output2) . "</pre>\n";
    
    // Count occurrences in second render
    $parent_count2 = substr_count($output2, 'PARENT_INIT_EXECUTED');
    $child_count2 = substr_count($output2, 'CHILD_INIT_EXECUTED');
    
    echo "<p><strong>PARENT_INIT_EXECUTED count (2nd render):</strong> $parent_count2 " . ($parent_count2 === 0 ? '✓' : '✗') . "</p>\n";
    echo "<p><strong>CHILD_INIT_EXECUTED count (2nd render):</strong> $child_count2 " . ($child_count2 === 0 ? '✓' : '✗') . "</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}

// Test 3: Check execution state
echo "<h2>Test 3: Execution State</h2>\n";
$state = Edge::getInheritanceState();
echo "<p><strong>Current execution state:</strong></p>\n";
echo "<pre>" . json_encode($state, JSON_PRETTY_PRINT) . "</pre>\n";

// Cleanup
unlink('system/components/edges/test-parent.edge.php');
unlink('system/components/edges/test-child.edge.php');

echo "<h2>Summary</h2>\n";
echo "<p>This test creates simple parent/child templates and verifies that:</p>\n";
echo "<ul>\n";
echo "<li>Child templates inherit parent properties</li>\n";
echo "<li>Init blocks execute only once per hierarchy</li>\n";
echo "<li>Execution state is properly tracked</li>\n";
echo "</ul>\n";
?>
